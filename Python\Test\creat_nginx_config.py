import requests
code = "AUTHORIZATION_CODE_FROM_REDIRECT"

token_url = "https://api.twitter.com/2/oauth2/token"
data = {
    "code": code,
    "grant_type": "authorization_code",
    "client_id": 30093313,
    "redirect_uri": "http://localhost:8000/callback",
    "code_verifier": "challenge",
}

response = requests.post(token_url, data=data, auth=(client_id, "YOUR_CLIENT_SECRET"))
access_token = response.json()["access_token"]

print(f"Access Token: {access_token}")