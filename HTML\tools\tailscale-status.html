<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tailscale状态查看</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .control-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            text-align: center;
        }

        .execute-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.1em;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .execute-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .execute-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: none;
            margin-top: 15px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results {
            margin-top: 30px;
        }

        .result-section {
            margin-bottom: 30px;
        }

        .result-title {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }

        .raw-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9em;
            line-height: 1.5;
            white-space: pre-wrap;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }

        .devices-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            table-layout: fixed;
        }

        .devices-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            position: relative;
            user-select: none;
        }

        .devices-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e2e8f0;
            word-wrap: break-word;
            overflow: hidden;
        }

        .devices-table tr:hover {
            background: #f7fafc;
        }

        /* 列宽设置 */
        .col-ip { width: 140px; }
        .col-name { width: 180px; }
        .col-os { width: 100px; }
        .col-status { width: 100px; }
        .col-connection { width: 120px; }
        .col-address { width: 200px; }
        .col-speed { width: 150px; }

        /* 可拖动调整宽度的样式 */
        .resizer {
            position: absolute;
            top: 0;
            right: 0;
            width: 5px;
            height: 100%;
            cursor: col-resize;
            background: transparent;
            z-index: 1;
        }

        .resizer:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .resizing {
            cursor: col-resize;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-offline {
            background: #fed7d7;
            color: #742a2a;
        }

        .status-current {
            background: #bee3f8;
            color: #2a4365;
        }

        .ip-address {
            font-family: 'Consolas', 'Monaco', monospace;
            background: #edf2f7;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.9em;
        }

        .error-message {
            background: #fed7d7;
            color: #742a2a;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #e53e3e;
        }

        .info-message {
            background: #e6fffa;
            color: #234e52;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #38b2ac;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 Tailscale状态查看</h1>
            <p>实时查看Tailscale网络设备状态</p>
        </div>

        <div class="content">
            <div class="info-message">
                <strong>说明：</strong>此工具需要本地HTTP服务器支持才能调用系统命令。当前为演示模式，显示模拟数据。
            </div>

            <div class="control-panel">
                <button id="executeBtn" class="execute-btn" onclick="executeTailscaleStatus()">
                    🚀 执行 tailscale status
                </button>
                <div id="loading" class="loading">
                    <div class="spinner"></div>
                    <p style="margin-top: 10px;">正在执行命令...</p>
                </div>
            </div>

            <div id="results" class="results" style="display: none;">
                <div class="result-section">
                    <h3 class="result-title">📋 原始输出</h3>
                    <div id="rawOutput" class="raw-output"></div>
                </div>

                <div class="result-section">
                    <h3 class="result-title">📊 设备状态表</h3>
                    <table id="devicesTable" class="devices-table">
                        <thead>
                            <tr>
                                <th class="col-ip">
                                    IP地址
                                    <div class="resizer" data-column="0"></div>
                                </th>
                                <th class="col-name">
                                    设备名称
                                    <div class="resizer" data-column="1"></div>
                                </th>
                                <th class="col-os">
                                    系统
                                    <div class="resizer" data-column="2"></div>
                                </th>
                                <th class="col-status">
                                    状态
                                    <div class="resizer" data-column="3"></div>
                                </th>
                                <th class="col-connection">
                                    连接方式
                                    <div class="resizer" data-column="4"></div>
                                </th>
                                <th class="col-address">
                                    连接地址
                                    <div class="resizer" data-column="5"></div>
                                </th>
                                <th class="col-speed">
                                    传输速率
                                </th>
                            </tr>
                        </thead>
                        <tbody id="devicesTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟的tailscale status输出数据
        const mockTailscaleOutput = `C:\\Users\\<USER>\n');
            const devices = [];

            for (const line of lines) {
                // 跳过命令行和空行
                if (line.includes('tailscale status') || line.trim() === '') {
                    continue;
                }

                // 解析设备信息行
                const match = line.match(/^(\d+\.\d+\.\d+\.\d+)\s+(\S+)\s+(\S+)\s+(\S+)\s+(.*)$/);
                if (match) {
                    const [, ip, name, user, os, statusAndConnection] = match;

                    let status, connectionType = '', connectionAddress = '', transferSpeed = '';

                    if (statusAndConnection.includes('active')) {
                        status = 'active';
                        const connectionInfo = statusAndConnection.replace('active;', '').trim();

                        // 解析连接信息
                        const connectionParts = parseConnectionInfo(connectionInfo);
                        connectionType = connectionParts.type;
                        connectionAddress = connectionParts.address;
                        transferSpeed = connectionParts.speed;

                    } else if (statusAndConnection.includes('offline')) {
                        status = 'offline';
                    } else if (statusAndConnection === '-') {
                        status = 'current';
                    } else {
                        status = 'unknown';
                    }

                    devices.push({
                        ip,
                        name,
                        os,
                        status,
                        connectionType,
                        connectionAddress,
                        transferSpeed
                    });
                }
            }

            return devices;
        }

        function parseConnectionInfo(connectionInfo) {
            if (!connectionInfo || connectionInfo === '') {
                return { type: '', address: '', speed: '' };
            }

            let type = '', address = '', speed = '';

            // 检查连接类型
            if (connectionInfo.includes('direct')) {
                type = '直连';

                // 提取地址信息
                const directMatch = connectionInfo.match(/direct\s+([^,]+)/);
                if (directMatch) {
                    address = directMatch[1].trim();
                }

                // 提取传输速率
                const speedMatch = connectionInfo.match(/tx\s+(\d+)\s+rx\s+(\d+)/);
                if (speedMatch) {
                    const tx = parseInt(speedMatch[1]);
                    const rx = parseInt(speedMatch[2]);
                    speed = `↑${formatBytes(tx)} ↓${formatBytes(rx)}`;
                }
            } else if (connectionInfo.includes('relay')) {
                type = '中继';
                address = connectionInfo.replace('relay', '').trim();
            } else {
                // 其他连接信息
                type = '其他';
                address = connectionInfo;
            }

            return { type, address, speed };
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        function getStatusClass(status) {
            switch (status) {
                case 'active': return 'status-active';
                case 'offline': return 'status-offline';
                case 'current': return 'status-current';
                default: return 'status-offline';
            }
        }

        function getStatusText(status) {
            switch (status) {
                case 'active': return '在线';
                case 'offline': return '离线';
                case 'current': return '当前设备';
                default: return '未知';
            }
        }

        function displayError(message) {
            const results = document.getElementById('results');
            results.innerHTML = `
                <div class="error-message">
                    <strong>错误：</strong>${message}
                </div>
            `;
            results.style.display = 'block';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Tailscale状态查看工具已加载');
            initializeColumnResizing();
        });

        // 初始化列宽调整功能
        function initializeColumnResizing() {
            const table = document.getElementById('devicesTable');
            const resizers = table.querySelectorAll('.resizer');
            let isResizing = false;
            let currentResizer = null;
            let startX = 0;
            let startWidth = 0;

            resizers.forEach(resizer => {
                resizer.addEventListener('mousedown', function(e) {
                    isResizing = true;
                    currentResizer = this;
                    startX = e.clientX;

                    const columnIndex = parseInt(this.dataset.column);
                    const th = this.parentElement;
                    startWidth = th.offsetWidth;

                    document.body.classList.add('resizing');
                    e.preventDefault();
                });
            });

            document.addEventListener('mousemove', function(e) {
                if (!isResizing || !currentResizer) return;

                const columnIndex = parseInt(currentResizer.dataset.column);
                const th = currentResizer.parentElement;
                const diff = e.clientX - startX;
                const newWidth = Math.max(50, startWidth + diff); // 最小宽度50px

                // 设置列宽
                th.style.width = newWidth + 'px';

                // 同时设置对应的td宽度
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    const td = row.children[columnIndex];
                    if (td) {
                        td.style.width = newWidth + 'px';
                    }
                });
            });

            document.addEventListener('mouseup', function() {
                if (isResizing) {
                    isResizing = false;
                    currentResizer = null;
                    document.body.classList.remove('resizing');
                }
            });
        }
    </script>
</body>
</html>
