# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON><PERSON>
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-22 20:49+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/settings/custom_color_setting_card/index.rst:2
#: 5c57a474c54540c2850af48a65ddad85
msgid "custom_color_setting_card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/custom_color_setting_card/index.rst:8
#: 06ad32363e2e47a886102d7ebe5f9b31
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/custom_color_setting_card/index.rst:18:<autosummary>:1
#: e0b68bb5040f4b13a0c4cefd99b2517a
msgid ""
":py:obj:`CustomColorSettingCard "
"<qfluentwidgets.components.settings.custom_color_setting_card.CustomColorSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/custom_color_setting_card/index.rst:23
#: ../../source/autoapi/qfluentwidgets/components/settings/custom_color_setting_card/index.rst:18:<autosummary>:1
#: 3d96cd6fc43b4c97909fb861d84b4fb5 f625393ca40e413e8bbcbfbf67cf239f
msgid "Custom color setting card"
msgstr "自定义颜色设置卡"

#: ../../source/autoapi/qfluentwidgets/components/settings/custom_color_setting_card/index.rst:21
#: a6c6c63b97ae4197813ae2678fcd26f3
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.settings.expand_setting_card.ExpandGroupSettingCard`"
msgstr ""

