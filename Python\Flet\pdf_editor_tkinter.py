#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文本替换编辑工具 - tkinter版本
基于tkinter框架开发的GUI应用程序，支持PDF文件的文本查找和替换功能
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import tempfile
from pathlib import Path
import traceback

try:
    import PyPDF2
    import pdfplumber
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.lib.styles import getSampleStyleSheet
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.units import inch
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    print("请安装: pip install PyPDF2 pdfplumber reportlab")


class PDFTextEditor:
    def __init__(self):
        self.current_pdf_path = None
        self.pdf_text = ""
        self.modified_text = ""
        self.replacement_pairs = []
        
    def extract_text_from_pdf(self, pdf_path):
        """从PDF文件中提取文本"""
        import warnings

        # 抑制字体相关的警告
        warnings.filterwarnings("ignore", message=".*FontBBox.*")
        warnings.filterwarnings("ignore", message=".*font descriptor.*")

        try:
            text = ""

            # 首先尝试使用pdfplumber
            try:
                with pdfplumber.open(pdf_path) as pdf:
                    for page_num, page in enumerate(pdf.pages):
                        try:
                            page_text = page.extract_text()
                            if page_text:
                                text += page_text + "\n"
                        except Exception as page_error:
                            print(f"警告: 第{page_num+1}页提取失败: {page_error}")
                            continue

                if text.strip():
                    return text

            except Exception as pdfplumber_error:
                print(f"pdfplumber提取失败，尝试PyPDF2: {pdfplumber_error}")

            # 如果pdfplumber失败，尝试PyPDF2
            try:
                with open(pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page_num, page in enumerate(pdf_reader.pages):
                        try:
                            page_text = page.extract_text()
                            if page_text:
                                text += page_text + "\n"
                        except Exception as page_error:
                            print(f"警告: 第{page_num+1}页提取失败: {page_error}")
                            continue

                if text.strip():
                    return text

            except Exception as pypdf2_error:
                print(f"PyPDF2提取失败: {pypdf2_error}")

            # 如果两种方法都失败，返回错误信息
            if not text.strip():
                raise Exception("无法从PDF中提取文本。可能的原因：\n1. PDF是扫描版图片\n2. PDF文件损坏\n3. PDF使用了不支持的编码")

            return text

        except Exception as e:
            raise Exception(f"PDF文本提取失败: {str(e)}")
    
    def replace_text(self, original_text, find_text, replace_text):
        """执行文本替换"""
        if not find_text:
            return original_text
        return original_text.replace(find_text, replace_text)
    
    def create_pdf_from_text(self, text, output_path):
        """从文本创建PDF文件"""
        import warnings

        # 抑制字体相关的警告
        warnings.filterwarnings("ignore", message=".*FontBBox.*")
        warnings.filterwarnings("ignore", message=".*font descriptor.*")

        try:
            # 创建PDF文档
            doc = SimpleDocTemplate(output_path, pagesize=A4)

            # 尝试注册中文字体
            font_name = 'Helvetica'  # 默认字体
            try:
                # Windows系统中文字体路径
                font_paths = [
                    "C:/Windows/Fonts/msyh.ttc",      # 微软雅黑
                    "C:/Windows/Fonts/simsun.ttc",    # 宋体
                    "C:/Windows/Fonts/simhei.ttf",    # 黑体
                    "C:/Windows/Fonts/simkai.ttf",    # 楷体
                    "C:/Windows/Fonts/NotoSansCJK-Regular.ttc"  # Noto字体
                ]

                for font_path in font_paths:
                    if os.path.exists(font_path):
                        try:
                            pdfmetrics.registerFont(TTFont('Chinese', font_path))
                            font_name = 'Chinese'
                            print(f"成功注册字体: {font_path}")
                            break
                        except Exception as font_error:
                            print(f"字体注册失败 {font_path}: {font_error}")
                            continue

            except Exception as e:
                print(f"字体注册过程出错: {e}")

            # 创建样式
            styles = getSampleStyleSheet()
            normal_style = styles['Normal']
            normal_style.fontName = font_name
            normal_style.fontSize = 12
            normal_style.leading = 14
            normal_style.wordWrap = 'CJK'  # 支持中文换行

            # 将文本分段并创建段落
            story = []
            paragraphs = text.split('\n')

            for para_text in paragraphs:
                if para_text.strip():
                    try:
                        # 清理文本，移除可能导致问题的字符
                        clean_text = para_text.replace('\x00', '').replace('\ufffd', '')

                        # 如果使用默认字体，过滤非ASCII字符
                        if font_name == 'Helvetica':
                            clean_text = ''.join(char if ord(char) < 128 else '?' for char in clean_text)

                        para = Paragraph(clean_text, normal_style)
                        story.append(para)

                    except Exception as para_error:
                        print(f"段落创建失败: {para_error}")
                        # 创建简化的段落
                        try:
                            safe_text = para_text.encode('ascii', 'ignore').decode()
                            if safe_text.strip():
                                para = Paragraph(safe_text, normal_style)
                                story.append(para)
                        except:
                            continue
                else:
                    story.append(Spacer(1, 0.2*inch))

            # 构建PDF
            if story:
                doc.build(story)
                return True
            else:
                raise Exception("没有有效内容可以创建PDF")

        except Exception as e:
            raise Exception(f"PDF创建失败: {str(e)}")


class PDFEditorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("PDF文本替换编辑工具")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 初始化PDF编辑器
        self.pdf_editor = PDFTextEditor()
        
        # 设置样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        
        # 配置样式
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Status.TLabel', font=('Arial', 10))
        
    def create_widgets(self):
        """创建界面控件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="PDF文本替换编辑工具", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 文件导入区域
        import_frame = ttk.LabelFrame(main_frame, text="📁 文件导入", padding="10")
        import_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        import_frame.columnconfigure(1, weight=1)
        
        self.select_button = ttk.Button(import_frame, text="选择PDF文件", command=self.select_file)
        self.select_button.grid(row=0, column=0, padx=(0, 10))
        
        self.file_path_var = tk.StringVar(value="未选择文件")
        self.file_path_label = ttk.Label(import_frame, textvariable=self.file_path_var, foreground="gray")
        self.file_path_label.grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        # 主要内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        content_frame.columnconfigure(0, weight=2)
        content_frame.columnconfigure(1, weight=1)
        content_frame.rowconfigure(0, weight=1)
        
        # 左侧：预览区域
        preview_frame = ttk.LabelFrame(content_frame, text="👀 内容预览", padding="10")
        preview_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)
        
        self.preview_text = scrolledtext.ScrolledText(
            preview_frame, 
            wrap=tk.WORD, 
            width=60, 
            height=25,
            state=tk.DISABLED,
            font=('Consolas', 10)
        )
        self.preview_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 右侧：修改区域
        edit_frame = ttk.LabelFrame(content_frame, text="🔄 文本替换", padding="10")
        edit_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        edit_frame.columnconfigure(0, weight=1)
        
        # 原文输入
        ttk.Label(edit_frame, text="原文:", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.find_text = scrolledtext.ScrolledText(edit_frame, wrap=tk.WORD, width=40, height=6)
        self.find_text.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 替换文本输入
        ttk.Label(edit_frame, text="变更为:", style='Heading.TLabel').grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.replace_text = scrolledtext.ScrolledText(edit_frame, wrap=tk.WORD, width=40, height=6)
        self.replace_text.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        # 按钮区域
        button_frame = ttk.Frame(edit_frame)
        button_frame.grid(row=4, column=0, sticky=(tk.W, tk.E))
        button_frame.columnconfigure(0, weight=1)
        button_frame.columnconfigure(1, weight=1)
        
        self.replace_button = ttk.Button(button_frame, text="执行替换", command=self.replace_text_action)
        self.replace_button.grid(row=0, column=0, padx=(0, 5), sticky=(tk.W, tk.E))
        
        self.save_button = ttk.Button(button_frame, text="保存PDF", command=self.save_pdf)
        self.save_button.grid(row=0, column=1, padx=(5, 0), sticky=(tk.W, tk.E))
        
        # 状态栏
        status_frame = ttk.Frame(main_frame, relief=tk.SUNKEN, borderwidth=1)
        status_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(1, weight=1)
        
        ttk.Label(status_frame, text="状态:", style='Status.TLabel').grid(row=0, column=0, padx=(5, 0))
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var, style='Status.TLabel', foreground="green")
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(5, 0))
        
    def update_status(self, message, color="black"):
        """更新状态信息"""
        self.status_var.set(message)
        self.status_label.configure(foreground=color)
        self.root.update()
        
    def select_file(self):
        """选择PDF文件"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择PDF文件",
                filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
            )
            
            if file_path:
                self.pdf_editor.current_pdf_path = file_path
                self.file_path_var.set(f"已选择: {Path(file_path).name}")
                self.file_path_label.configure(foreground="black")
                
                # 提取PDF文本
                self.update_status("正在提取PDF文本...", "blue")
                
                pdf_text = self.pdf_editor.extract_text_from_pdf(file_path)
                self.pdf_editor.pdf_text = pdf_text
                self.pdf_editor.modified_text = pdf_text
                
                # 更新预览
                self.preview_text.configure(state=tk.NORMAL)
                self.preview_text.delete(1.0, tk.END)
                self.preview_text.insert(1.0, pdf_text)
                self.preview_text.configure(state=tk.DISABLED)
                
                self.update_status(f"PDF加载成功，共提取 {len(pdf_text)} 个字符", "green")
                
        except Exception as e:
            self.update_status(f"错误: {str(e)}", "red")
            messagebox.showerror("错误", f"文件加载失败:\n{str(e)}")
    
    def replace_text_action(self):
        """执行文本替换"""
        try:
            if not self.pdf_editor.current_pdf_path:
                self.update_status("请先选择PDF文件", "red")
                messagebox.showwarning("警告", "请先选择PDF文件")
                return
            
            find_text = self.find_text.get(1.0, tk.END).strip()
            replace_text = self.replace_text.get(1.0, tk.END).strip()
            
            if not find_text:
                self.update_status("请输入要替换的原文", "red")
                messagebox.showwarning("警告", "请输入要替换的原文")
                return
            
            # 执行替换
            self.update_status("正在执行文本替换...", "blue")
            
            self.pdf_editor.modified_text = self.pdf_editor.replace_text(
                self.pdf_editor.modified_text, find_text, replace_text
            )
            
            # 更新预览
            self.preview_text.configure(state=tk.NORMAL)
            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(1.0, self.pdf_editor.modified_text)
            self.preview_text.configure(state=tk.DISABLED)
            
            # 记录替换操作
            replacement_info = f"'{find_text}' → '{replace_text}'"
            if replacement_info not in [pair[2] for pair in self.pdf_editor.replacement_pairs]:
                self.pdf_editor.replacement_pairs.append((find_text, replace_text, replacement_info))
            
            self.update_status(f"替换完成: {replacement_info}", "green")
            
        except Exception as e:
            self.update_status(f"替换失败: {str(e)}", "red")
            messagebox.showerror("错误", f"文本替换失败:\n{str(e)}")
    
    def save_pdf(self):
        """保存PDF文件"""
        try:
            if not self.pdf_editor.modified_text:
                self.update_status("没有可保存的内容", "red")
                messagebox.showwarning("警告", "没有可保存的内容")
                return
            
            file_path = filedialog.asksaveasfilename(
                title="保存PDF文件",
                defaultextension=".pdf",
                filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")],
                initialname="modified_document.pdf"
            )
            
            if file_path:
                self.update_status("正在生成PDF文件...", "blue")
                
                self.pdf_editor.create_pdf_from_text(self.pdf_editor.modified_text, file_path)
                
                self.update_status(f"PDF已保存至: {Path(file_path).name}", "green")
                messagebox.showinfo("成功", f"PDF文件已保存至:\n{file_path}")
                
        except Exception as e:
            self.update_status(f"保存失败: {str(e)}", "red")
            messagebox.showerror("错误", f"PDF保存失败:\n{str(e)}")


def main():
    root = tk.Tk()
    app = PDFEditorGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
