# MCP工具演示平台

这是一个使用Flet构建的本地MCP（Model Context Protocol）工具演示应用，展示了如何实现和使用各种MCP工具。

## 功能特性

### 支持的工具
1. **Fetch工具** - 执行HTTP请求
2. **Shell工具** - 执行系统命令
3. **FileSystem工具** - 文件系统操作

### 两种执行模式
1. **直接执行** - 本地直接调用工具功能
2. **MCP调用** - 通过LLM API模拟MCP协议调用

## 安装依赖

```bash
pip install flet requests
```

## 使用说明

### 1. 启动应用
```bash
python mcp_tool_demo.py
```

### 2. 配置LLM API（用于MCP调用）
- **API URL**: 输入你的LLM API端点（如OpenAI、Claude等）
- **API Key**: 输入对应的API密钥

### 3. 选择和配置工具

#### Fetch工具
- **URL**: 要请求的网址
- **HTTP方法**: GET、POST、PUT、DELETE
- **请求头**: JSON格式的HTTP头
- **请求体**: JSON格式的请求数据

示例配置：
```
URL: https://httpbin.org/json
方法: GET
请求头: {"User-Agent": "MCP-Tool-Demo"}
```

#### Shell工具
- **命令**: 要执行的系统命令
- **工作目录**: 命令执行的目录

示例配置：
```
命令: echo "Hello MCP!"
工作目录: ./
```

#### FileSystem工具
- **操作类型**: read（读取）、write（写入）、list（列表）
- **路径**: 文件或目录路径
- **内容**: 写入文件时的内容

示例配置：
```
操作: read
路径: ./README.md
```

### 4. 执行工具

#### 直接执行
点击"直接执行工具"按钮，应用会直接调用相应的工具功能。

#### MCP调用
点击"通过MCP调用"按钮，应用会：
1. 构建包含工具配置的prompt
2. 调用LLM API
3. 显示LLM的响应结果

## 实现原理

### MCP工具架构
```
MCPTool (基类)
├── FetchTool - HTTP请求工具
├── ShellTool - 系统命令工具
└── FileSystemTool - 文件操作工具
```

### 执行流程

#### 直接执行流程
1. 用户配置工具参数
2. 应用调用对应工具的execute方法
3. 工具执行实际操作
4. 返回结构化结果

#### MCP调用流程
1. 用户配置工具参数
2. 应用构建包含工具信息的prompt
3. 调用LLM API发送prompt
4. LLM理解工具调用意图并返回响应
5. 显示LLM的处理结果

### 关键代码结构

```python
# 工具基类
class MCPTool:
    async def execute(self, **kwargs) -> Dict[str, Any]:
        raise NotImplementedError

# 具体工具实现
class FetchTool(MCPTool):
    async def execute(self, url, method="GET", headers=None, data=None):
        # 执行HTTP请求
        pass

# MCP调用
def build_mcp_prompt(self) -> str:
    # 构建包含工具配置的prompt
    pass

async def call_llm_api(self, prompt: str) -> str:
    # 调用LLM API
    pass
```

## 预期响应示例

### Fetch工具响应
```json
{
  "success": true,
  "status_code": 200,
  "headers": {"content-type": "application/json"},
  "content": "...",
  "json": {...},
  "url": "https://httpbin.org/json",
  "method": "GET"
}
```

### Shell工具响应
```json
{
  "success": true,
  "command": "echo 'Hello MCP!'",
  "return_code": 0,
  "stdout": "Hello MCP!\n",
  "stderr": "",
  "cwd": "/current/directory"
}
```

### FileSystem工具响应
```json
{
  "success": true,
  "action": "read",
  "path": "./README.md",
  "content": "文件内容...",
  "size": 1024
}
```

## 注意事项

1. **安全性**: Shell工具可以执行系统命令，请谨慎使用
2. **API配置**: MCP调用需要有效的LLM API配置
3. **网络访问**: Fetch工具需要网络连接
4. **文件权限**: FileSystem工具需要相应的文件读写权限

## 扩展开发

要添加新的MCP工具：

1. 继承MCPTool基类
2. 实现execute方法
3. 在MCPToolDemo中注册工具
4. 创建对应的UI配置界面

```python
class NewTool(MCPTool):
    def __init__(self):
        super().__init__("newtool", "新工具描述")
    
    async def execute(self, **kwargs):
        # 实现工具逻辑
        return {"success": True, "result": "..."}
```

这个演示平台展示了MCP工具的基本概念和实现方式，可以作为开发更复杂MCP应用的起点。
