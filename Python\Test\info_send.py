import requests
import socket
import time


def tg_send(text):
    bot_token = 'bot' + '2121710944:AAErqi24J4H-x8Cms9rcAgEn-gxooPFS7E8'
    bot_url = 'https://api.telegram.org/' + bot_token + '/sendMessage'
    params = {
        'chat_id': 617924457 ,
        'text': text
    }
    resp = requests.get(bot_url, params=params)
    return resp.text


def wechat_send(text, result):
    url = 'https://sctapi.ftqq.com/SCT72234Tb3C6ypUKnQ9wSOG6RvWjXJzr.send'
    params = {
        'title': result,
        'desp': text,
        'channel': 9
    }
    requests.post(url, params=params)
    

resp = requests.get('https://ifconfig.me')
local_ip = resp.text
ghk_ip = socket.gethostbyname('ghk.400006.xyz')
# while True:
#     time.sleep(10)
#     if ghk_ip == local_ip :
#         result = 'IP无变化'
#     else:
#         result =f'IP已改变，需修改CF . {local_ip}'
#         send_message = f'当前ip：{local_ip} ,{result}'
#         wechat_send(send_message, result)
#     with open('ipcheck.log', 'a+') as f:
#         info = time.asctime(time.localtime(time.time())) + '    ' + result + '\n'
#         print(info)        
#         f.write(info)
