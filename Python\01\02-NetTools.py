import flet as ft
import ipaddress
import threading
import time
import datetime
from ping3 import ping
import subprocess
import socket
import concurrent.futures

class PingTool:
    def __init__(self):
        self.reset_stats()
        self.is_running = True
        self.output_callback = None

    def reset_stats(self):
        """Reset ping statistics"""
        self.stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'loss_rate': 0.0,
            'min_time': float('inf'),
            'max_time': 0.0,
            'total_time': 0.0,
            'avg_time': 0.0
        }

    def set_output_callback(self, callback):
        """Set callback function for output"""
        self.output_callback = callback

    def _output(self, message, color=None, weight=None, style=None):
        """Handle output through callback or return"""
        if self.output_callback:
            self.output_callback(message, color, weight, style)
        return message

    def ping_host(self, host, count=None, continuous=False):
        try:
            ping_count = 0
            self._output(f"\n正在 Ping {host} ...\n", color="blue", weight=ft.FontWeight.BOLD)
            self._output("─" * 60 + "\n", color="grey")

            while self.is_running:  
                try:
                    delay = ping(host, timeout=3)
                    time.sleep(1)
                    timestamp = datetime.datetime.now().strftime("%H:%M:%S")
                    
                    self.stats['total'] += 1
                    
                    if delay is None or delay is False:
                        self.stats['failed'] += 1
                        output = f"[{timestamp}] #{ping_count+1:<4} 请求超时\n"
                        self._output(output, color="red")
                    else:
                        self.stats['success'] += 1
                        delay_ms = delay * 1000
                        self.stats['min_time'] = min(self.stats['min_time'], delay_ms)
                        self.stats['max_time'] = max(self.stats['max_time'], delay_ms)
                        self.stats['total_time'] += delay_ms
                        self.stats['avg_time'] = self.stats['total_time'] / self.stats['success']
                        
                        output = f"[{timestamp}] #{ping_count+1:<4} 来自 {host:<15} 的回复: 时间={delay_ms:>6.1f}ms\n"
                        self._output(output, color="green")

                    self.stats['loss_rate'] = (self.stats['failed'] / self.stats['total'] * 100)

                    ping_count += 1  
                    if not continuous and count and ping_count >= count:  
                        self.is_running = False  
                        break
                        
                except Exception as e:
                    error_msg = f"Ping 错误: {str(e)}\n"
                    self._output(error_msg, color="red")
                    break

        except Exception as e:
            error_msg = f"设置 ping 时出错: {str(e)}\n"
            self._output(error_msg, color="red")
        finally:
            if not continuous:
                self._add_summary(host)

    def _add_summary(self, host):
        try:
            if self.stats['total'] == 0:
                return  

            self._output("\n" + "─" * 60 + "\n", color="grey")
            summary = f"{host} 的 Ping 统计信息:\n"
            self._output(summary, color="blue", weight=ft.FontWeight.BOLD)
            
            stats_line = f"    数据包: 已发送 = {self.stats['total']:<4}, "
            stats_line += f"已接收 = {self.stats['success']:<4}, "
            stats_line += f"丢失 = {self.stats['failed']:<4} "
            stats_line += f"({self.stats['loss_rate']:.1f}% 丢失)\n"
            self._output(stats_line, color="blue")
        
            if self.stats['success'] > 0:
                self._output("\n往返行程的估计时间(以毫秒为单位):\n", color="blue", weight=ft.FontWeight.BOLD)
                
                time_line = f"    最短 = {self.stats['min_time']:>6.1f}ms, "
                time_line += f"最长 = {self.stats['max_time']:>6.1f}ms, "
                time_line += f"平均 = {self.stats['avg_time']:>6.1f}ms\n"
                self._output(time_line, color="blue")
            
            self._output("─" * 60 + "\n", color="grey")
            
        except Exception as e:
            error_msg = f"生成统计信息时出错: {str(e)}\n"
            self._output(error_msg, color="red")

    def ping_network(self, network):
        """Ping all hosts in a network"""
        try:
            net = ipaddress.ip_network(network)
            total_hosts = net.num_addresses - 2  # Exclude network and broadcast addresses
            self._output(f"开始扫描网段 {network} (共 {total_hosts} 个主机)...\n", color="blue", weight=ft.FontWeight.BOLD)
            
            for ip in net.hosts():
                if not self.is_running:
                    break
                
                ip_str = str(ip)
                try:
                    timestamp = datetime.datetime.now().strftime("%H:%M:%S")
                    delay = ping(ip_str, timeout=0.3)
                    
                    if delay is not None and delay is not False:
                        delay_ms = delay * 1000
                        self.stats['success'] += 1
                        self.stats['min_time'] = min(self.stats['min_time'], delay_ms)
                        self.stats['max_time'] = max(self.stats['max_time'], delay_ms)
                        self.stats['total_time'] += delay_ms
                        self.stats['avg_time'] = self.stats['total_time'] / self.stats['success']
                        
                        result = f"[{timestamp}] {ip_str} 响应时间: {delay_ms:.1f}ms\n"
                        self._output(result, color="green")
                    else:
                        result = f"[{timestamp}] {ip_str} 不可达\n"
                        self.stats['failed'] += 1
                        self._output(result, color="red")
                    
                    self.stats['total'] += 1
                    self.stats['loss_rate'] = (self.stats['failed'] / self.stats['total'] * 100)
                    
                except Exception as e:
                    error_msg = f"Ping {ip_str} 时出错: {str(e)}\n"
                    self._output(error_msg, color="red")
                
                time.sleep(0.1)  
            
            self._add_summary(network)
            
        except Exception as e:
            error_msg = f"网段扫描出错: {str(e)}\n"
            self._output(error_msg, color="red")

class PortScanner:
    def __init__(self):
        self.reset_stats()
        self.is_running = True
        self.output_callback = None

    def reset_stats(self):
        """Reset scan statistics"""
        self.stats = {
            'total': 0,
            'open': 0,
            'closed': 0,
            'filtered': 0
        }
        self.is_running = True

    def set_output_callback(self, callback):
        """Set callback function for output"""
        self.output_callback = callback

    def _output(self, message, color=None, weight=None, style=None):
        """Handle output through callback or return"""
        if self.output_callback:
            self.output_callback(message, color, weight, style)
        return message

    def scan_port(self, host, port, timeout=0.5):
        """Scan a single port"""
        try:
            # Create a new socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            
            # Try to connect
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                service = ""
                try:
                    service = socket.getservbyport(port)
                    return True, f"open ({service})"
                except:
                    return True, "open"
            return False, "closed"
            
        except socket.gaierror:
            return False, "DNS解析失败"
        except socket.timeout:
            return False, "超时"
        except ConnectionRefusedError:
            return False, "连接被拒绝"
        except Exception as e:
            return False, f"错误: {str(e)}"
        finally:
            try:
                sock.close()
            except:
                pass

    def scan_ports(self, host, start_port, end_port, max_threads=50):
        """Scan a range of ports"""
        if not self.is_running:
            return
            
        try:
            socket.gethostbyname(host)  # 验证主机名是否有效
        except socket.gaierror:
            self._output(f"错误: 无法解析主机名 {host}\n", color="red")
            return
            
        try:
            self._output(f"开始扫描主机 {host} 的端口 {start_port}-{end_port}...\n", 
                        color="blue", weight=ft.FontWeight.BOLD)
            
            def scan_one_port(port):
                if not self.is_running:
                    return None
                
                timestamp = datetime.datetime.now().strftime("%H:%M:%S")
                is_open, status = self.scan_port(host, port)
                
                if not self.is_running:  # Check again after scan
                    return None
                    
                self.stats['total'] += 1
                
                if "open" in status:
                    self.stats['open'] += 1
                    self._output(f"[{timestamp}] 端口 {port}: {status}\n", color="green")
                elif "超时" in status or "DNS" in status:
                    self.stats['filtered'] += 1
                    self._output(f"[{timestamp}] 端口 {port}: {status}\n", color="yellow")
                else:
                    self.stats['closed'] += 1
                
                return port, status

            # 使用线程池进行并发扫描
            ports = list(range(start_port, end_port + 1))
            max_workers = min(max_threads, len(ports))
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = []
                for port in ports:
                    if not self.is_running:
                        break
                    futures.append(executor.submit(scan_one_port, port))
                
                for future in concurrent.futures.as_completed(futures):
                    if not self.is_running:
                        executor.shutdown(wait=False)
                        break
                    try:
                        result = future.result()
                        if result is None:  # Scan was cancelled
                            break
                    except Exception as e:
                        if self.is_running:  # Only show error if still running
                            self._output(f"扫描出错: {str(e)}\n", color="red")

            if self.is_running:
                # Show summary
                summary = f"\n扫描完成! 统计信息:\n"
                summary += f"总端口数: {self.stats['total']}\n"
                summary += f"开放端口: {self.stats['open']}\n"
                summary += f"关闭端口: {self.stats['closed']}\n"
                summary += f"已过滤端口: {self.stats['filtered']}\n"
                self._output(summary, color="blue", weight=ft.FontWeight.BOLD)

        except Exception as e:
            if self.is_running:
                self._output(f"扫描出错: {str(e)}\n", color="red")
        finally:
            self.is_running = False

def main(page: ft.Page):
    page.title = "Network Tools"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window_width = 1000
    page.window_height = 800
    page.padding = 20

    ping_tool = PingTool()
    port_scanner = PortScanner()
    ping_results = ft.Column(
        scroll=ft.ScrollMode.AUTO,
        height=400,
        width=700,
        spacing=10,
        auto_scroll=True,
    )

    def show_error(message):
        """Show error message in dialog"""
        def close_dlg(e):
            dlg.open = False
            page.update()

        dlg = ft.AlertDialog(
            title=ft.Text("错误", color="red"),
            content=ft.Text(message),
            actions=[
                ft.TextButton("确定", on_click=close_dlg),
            ],
        )
        page.dialog = dlg
        dlg.open = True
        page.update()

    def update_stats_table():
        """Update statistics table with current values"""
        try:
            total_row.cells[1].content.value = str(ping_tool.stats['total'])
            success_row.cells[1].content.value = str(ping_tool.stats['success'])
            failed_row.cells[1].content.value = str(ping_tool.stats['failed'])
            loss_rate_row.cells[1].content.value = f"{ping_tool.stats['loss_rate']:.1f}%"
            
            if ping_tool.stats['success'] > 0:
                min_time_row.cells[1].content.value = f"{ping_tool.stats['min_time']:.1f}ms"
                max_time_row.cells[1].content.value = f"{ping_tool.stats['max_time']:.1f}ms"
                avg_time_row.cells[1].content.value = f"{ping_tool.stats['avg_time']:.1f}ms"
            page.update()
        except Exception as e:
            show_error(f"更新统计表时出错: {str(e)}")

    def update_ui():
        def output_callback(message, color=None, weight=None, style=None):
            if color == "red" and ("错误" in message or "出错" in message):
                show_error(message)
            else:
                ping_results.controls.append(ft.Text(message, color=color, weight=weight, style=style))
                if len(ping_results.controls) > 0:
                    ping_results.scroll_to(offset=len(ping_results.controls) * 100, duration=300)
                update_stats_table()
                page.update()

        ping_tool.set_output_callback(output_callback)
        port_scanner.set_output_callback(output_callback)
        
        while ping_tool.is_running or port_scanner.is_running:
            time.sleep(0.1)
            page.update()

    def on_mode_change(e):
        if ping_mode.value == "network":
            ping_count.visible = False
            host_input.label = "网段地址 (例如: ***********/24)"
        elif ping_mode.value == "port_scan":
            ping_count.visible = False
            host_input.label = "目标主机"
            port_range_input.visible = True
        else:
            ping_count.visible = ping_mode.value == "count"
            host_input.label = "目标主机"
            port_range_input.visible = False
        page.update()

    def start_ping(e):
        try:
            host = host_input.value.strip()
            if not host:
                raise ValueError("请输入目标主机")

            # Disable UI controls
            start_button.disabled = True
            stop_button.disabled = False
            host_input.disabled = True
            ping_mode.disabled = True
            port_range_input.disabled = True
            if ping_mode.value == "count":
                ping_count.disabled = True

            # Reset status
            ping_tool.reset_stats()
            port_scanner.reset_stats()
            ping_tool.is_running = True
            port_scanner.is_running = True
            
            # Clear previous results
            ping_results.controls.clear()
            page.update()

            # Start operation in background thread
            def run_operation():
                try:
                    if ping_mode.value == "network":
                        ping_tool.ping_network(host)
                    elif ping_mode.value == "port_scan":
                        try:
                            port_input = port_range_input.value.strip()
                            if not port_input:
                                raise ValueError("请输入端口或端口范围")
                            
                            # 处理端口输入
                            if "-" in port_input:
                                # 端口范围
                                start_port, end_port = map(int, port_input.split("-"))
                                if start_port < 1 or end_port > 65535 or start_port > end_port:
                                    raise ValueError("端口范围无效 (1-65535)")
                            else:
                                # 单个端口
                                try:
                                    port = int(port_input)
                                    if port < 1 or port > 65535:
                                        raise ValueError("端口号无效 (1-65535)")
                                    start_port = end_port = port
                                except ValueError:
                                    raise ValueError("端口格式无效，请输入数字或范围 (例如: 80 或 80-100)")
                            
                            port_scanner.scan_ports(host, start_port, end_port)
                        except ValueError as ve:
                            show_error(str(ve))
                    else:
                        count = None if ping_mode.value == "continuous" else int(ping_count.value)
                        continuous = ping_mode.value == "continuous"
                        ping_tool.ping_host(host, count, continuous)
                except Exception as e:
                    show_error(str(e))
                finally:
                    if not ping_mode.value == "continuous":
                        stop_ping()

            # Start both operation and UI update threads
            threading.Thread(target=run_operation, daemon=True).start()
            threading.Thread(target=update_ui, daemon=True).start()

        except Exception as e:
            show_error(f"启动时出错: {str(e)}")
            stop_ping()

    def stop_ping(e=None):
        ping_tool.is_running = False
        port_scanner.is_running = False
        port_scanner.reset_stats()  # Reset port scanner stats
        start_button.disabled = False
        stop_button.disabled = True
        host_input.disabled = False
        ping_mode.disabled = False
        port_range_input.disabled = False
        if ping_mode.value == "count":
            ping_count.disabled = False
        page.update()

    # Create UI components
    host_input = ft.TextField(
        label="目标主机",
        width=400,
        value="localhost"
    )

    ping_mode = ft.Dropdown(
        label="Ping 模式",
        width=200,
        options=[
            ft.dropdown.Option("count", "普通 Ping"),
            ft.dropdown.Option("continuous", "持续 Ping"),
            ft.dropdown.Option("network", "网段 Ping"),
            ft.dropdown.Option("port_scan", "端口扫描"),
        ],
        value="count",
        on_change=on_mode_change
    )

    ping_count = ft.TextField(
        label="Ping 次数",
        width=100,
        value="4"
    )

    port_range_input = ft.TextField(
        label="端口/端口范围",
        width=200,
        value="80",
        hint_text="单端口或范围 (例如: 80 或 80-100)",
        visible=False
    )

    results_container = ft.Container(
        content=ping_results,
        border=ft.border.all(2, "grey"),
        border_radius=10,
        padding=10,
        bgcolor=ft.colors.WHITE10,
        expand=True
    )

    total_row = ft.DataRow(cells=[
        ft.DataCell(ft.Text("总数")),
        ft.DataCell(ft.Text("0")),
    ])
    success_row = ft.DataRow(cells=[
        ft.DataCell(ft.Text("成功")),
        ft.DataCell(ft.Text("0")),
    ])
    failed_row = ft.DataRow(cells=[
        ft.DataCell(ft.Text("失败")),
        ft.DataCell(ft.Text("0")),
    ])
    loss_rate_row = ft.DataRow(cells=[
        ft.DataCell(ft.Text("丢包率")),
        ft.DataCell(ft.Text("0%")),
    ])
    min_time_row = ft.DataRow(cells=[
        ft.DataCell(ft.Text("最小延迟")),
        ft.DataCell(ft.Text("-- ms")),
    ])
    max_time_row = ft.DataRow(cells=[
        ft.DataCell(ft.Text("最大延迟")),
        ft.DataCell(ft.Text("-- ms")),
    ])
    avg_time_row = ft.DataRow(cells=[
        ft.DataCell(ft.Text("平均延迟")),
        ft.DataCell(ft.Text("-- ms")),
    ])

    stats_table = ft.DataTable(
        width=250,
        border=ft.border.all(2, "grey"),
        border_radius=10,
        vertical_lines=ft.border.BorderSide(1, "grey"),
        horizontal_lines=ft.border.BorderSide(1, "grey"),
        columns=[
            ft.DataColumn(ft.Text("指标")),
            ft.DataColumn(ft.Text("数值")),
        ],
        rows=[
            total_row,
            success_row,
            failed_row,
            loss_rate_row,
            min_time_row,
            max_time_row,
            avg_time_row,
        ],
    )

    start_button = ft.ElevatedButton("开始", width=100, on_click=start_ping)
    stop_button = ft.ElevatedButton("停止", width=100, on_click=stop_ping, disabled=True)

    # Layout
    page.add(
        ft.Column([
            ft.Row([host_input]),
            ft.Row([
                ping_mode,
                ping_count,
                port_range_input
            ]),
            ft.Row([
                start_button,
                stop_button,
            ]),
            ft.Row([
                stats_table,
                results_container,
            ]),
        ])
    )
    page.update()

if __name__ == "__main__":
    ft.app(target=main)