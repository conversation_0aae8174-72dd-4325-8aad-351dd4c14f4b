
import requests
import json
import copy

# 400006.xyz    c975ddb71fcd3057de8fc2b0db01daf9
# hello123.co   2c31b5c35f2319e00186313d56c46a4c

api = 'dd52324883f4dee259cd059e6ab2d4fbae42a'
cfheaders = {
        'X-Auth-Email': '<EMAIL>',
        'X-Auth-Key': 'dd52324883f4dee259cd059e6ab2d4fbae42a',
        'Content-Type': 'application/json'
    }


#操作选择
# 0、列出域名，选择:zone_id
def zone_list():
    url = 'https://api.cloudflare.com/client/v4/zones'

    res = json.loads(requests.get(url, headers = cfheaders).text)['result']
    n = 0
    for i in res:
        n += 1
        print(n, i['name'])
    print('3、更新HomeV6')
    choice = input('对哪个进行操作：')
    if choice == '3':
        change_home_ip()
        return None
    else:
        return res[int(choice)-1]['id']
# 1、查
def get_dns_list(id):
    zone_url = f'https://api.cloudflare.com/client/v4/zones/{id}/dns_records'

    dns_res = requests.get( url = zone_url, headers = cfheaders)
    dns_res = json.loads(dns_res.text)['result']
    dns_list =[]
    n = 0
    for i in dns_res:
        dns = [i.get('name'),i.get('type'), i.get('content'),i.get('id')]
        n += 1
        print(str(n) + '.', end=' ')
        for i in range(0,3):
            print(dns[i], end= '    ')
        print()  
        dns_list.append(copy.deepcopy(dns))
                
    return dns_list
# 2、增
def creat_dns(id):
    zone_url = f'https://api.cloudflare.com/client/v4/zones/{id}/dns_records'
    pre = input('前缀：')
    ip = input('IP:')
    ip_type = input('IP类型（4/6）:')
    if ip_type == '4':
        type = 'A'
    else:
        type = 'AAAA'
    data = {
        'type' : type,
        'name' : pre,
        'content' : ip,
        'ttl' : 1,
        "proxied": False
    }
    res = requests.post( url=zone_url, headers = cfheaders, data=json.dumps(data))
    print(res.text)
# 3、改
def patch_dns(zone_id):

    dns_list = get_dns_list(zone_id)
    n = 0
    for dns in dns_list:
        n += 1
        print(str(n) + '.', end=' ')
        for i in range(0,3):
            print(dns[i], end= '    ')
        print()  
            
    choice = int(input('修改第几个dns解析(0取消)：'))
    if choice != 0:
        dns_id = dns_list[choice-1][3]
        url = f'https://api.cloudflare.com/client/v4/zones/{zone_id}/dns_records/{dns_id}'
        name = input('name：')
        ip = input('ip:')
        data = {
            'name' : name,
            'content' : ip,
                    }
        res = requests.patch(url, headers = cfheaders, data = json.dumps(data) ).text
        print(res)
        return None
    else:
        return None

    print(dns)
# 4、删
# 5、更新本机
    #获取ip
def get_myip():
    user_agent = {'user-agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}
    myip = requests.get('https://api-ipv6.ip.sb/ip', headers = user_agent).text
    return myip.strip()
def change_home_ip():
    homev6 = get_myip()
    zone_id = '2c31b5c35f2319e00186313d56c46a4c'
    dns_id = '0caf0f6e241b636f6fd7f8f58c12886f'
    url = f'https://api.cloudflare.com/client/v4/zones/{zone_id}/dns_records/{dns_id}'
    data = {
        'name' : 'home',
        'content' : home,
                }
    res = json.loads(requests.patch(url, headers = cfheaders, data = json.dumps(data) ).text)
    print(res.get('success'))
    return None



# creat_dns()
# get_dns_list()
# patch_dns()
def start():
    zone_id = zone_list()
    title = '''
    ========
    0、取消
    1、查询
    2、修改
    3、增加
    4、删除
    5、更新home
    ========
    '''
    while True:
        print(title)
        choice = input('选择：')
        if choice == '1':
            get_dns_list(zone_id)
        elif choice == '2':
            patch_dns(zone_id)
        elif choice == '3':
            creat_dns(zone_id)
        elif choice == '4':
            pass
        elif choice == '5':
            change_home_ip()
        else:
            break

start()

    