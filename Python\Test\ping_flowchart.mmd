graph TD
    A[程序启动] --> B[创建Flet窗口]
    B --> C[初始化UI组件]
    C --> D{用户点击开始按钮}
    
    D -->|是| E[创建新线程]
    E --> F[开始ping循环]
    
    F --> G[创建Popen进程]
    G --> H[清空输出框]
    
    H --> I[读取一行输出]
    I --> J{有新行输出?}
    
    J -->|是| K[添加时间戳]
    K --> L[更新UI显示]
    L --> I
    
    J -->|否| M{进程结束?}
    M -->|否| I
    M -->|是| N[检查错误输出]
    
    N --> O{有错误?}
    O -->|是| P[显示错误信息]
    O -->|否| Q[等待1秒]
    P --> Q
    
    Q --> F
    
    D -->|否| R[继续等待用户操作]
    
    subgraph 主线程
    A
    B
    C
    D
    R
    end
    
    subgraph Ping线程
    E
    F
    G
    H
    I
    J
    K
    L
    M
    N
    O
    P
    Q
    end

    style 主线程 fill:#f9f,stroke:#333,stroke-width:4px
    style Ping线程 fill:#bbf,stroke:#333,stroke-width:4px
