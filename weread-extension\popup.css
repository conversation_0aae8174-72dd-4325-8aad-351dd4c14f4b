/* 基础样式 */
:root {
    --primary: #1aad19;
    --secondary: #4caf50;
    --dark-bg: #f5f5f5;
    --card-bg: #ffffff;
    --text-primary: #333333;
    --text-secondary: #666666;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --transition: all 0.3s ease;
    --yellow-highlight: #fff8e1;
    --green-highlight: #e8f5e9;
    --blue-highlight: #e3f2fd;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
    width: 400px;
    height: 600px;
    overflow-x: hidden;
    overflow-y: auto;
}

/* 排版 */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: 1.8rem;
}

h2 {
    font-size: 1.5rem;
}

h3 {
    font-size: 1.2rem;
}

p {
    margin-bottom: 0.5rem;
}

a {
    color: var(--primary);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    opacity: 0.8;
}

/* 头部 */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: var(--card-bg);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo-text {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary);
}

.logo-icon {
    font-size: 1.2rem;
    color: var(--primary);
}

/* 控制区域 */
.controls {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.5rem;
    background-color: var(--card-bg);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.search-container {
    display: flex;
    width: 100%;
}

.search-container input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 0.25rem 0 0 0.25rem;
    font-size: 0.9rem;
}

.search-container button {
    padding: 0.5rem;
    background: var(--primary);
    color: white;
    border: none;
    border-radius: 0 0.25rem 0.25rem 0;
    cursor: pointer;
}

.filter-container {
    display: flex;
    gap: 0.5rem;
    width: 100%;
}

.filter-container select {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 0.25rem;
    font-size: 0.9rem;
    background-color: white;
}

/* 笔记容器 */
.notes-container {
    padding: 0.5rem;
    overflow-y: auto;
    max-height: 480px;
}

.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
}

.spinner {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(26, 173, 25, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s linear infinite;
    margin-bottom: 0.5rem;
}

.error-message {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--error);
    padding: 0.75rem;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    text-align: center;
}

.hidden {
    display: none;
}

.notes-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.note-card {
    background: var(--card-bg);
    border-radius: 0.25rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
    animation: fadeInUp 0.3s ease;
}

.note-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.note-header {
    padding: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-size: 0.9rem;
}

.note-book-title {
    font-weight: 600;
    color: var(--primary);
}

.note-time {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.note-content {
    padding: 0.5rem;
}

.note-text {
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 0.25rem;
}

.note-chapter {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

/* 颜色样式 */
.color-3 {
    border-left: 3px solid #ffc107;
    background-color: var(--yellow-highlight);
}

.color-4 {
    border-left: 3px solid #4caf50;
    background-color: var(--green-highlight);
}

.color-5 {
    border-left: 3px solid #2196f3;
    background-color: var(--blue-highlight);
}

/* 页脚 */
footer {
    background: var(--card-bg);
    padding: 0.75rem;
    text-align: center;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* 动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 空状态 */
.empty-message {
    text-align: center;
    padding: 2rem 0;
    color: var(--text-secondary);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
}
