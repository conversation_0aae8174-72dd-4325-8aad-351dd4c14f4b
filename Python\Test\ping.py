import flet as ft
import datetime
from ping3 import ping
import asyncio

def main(page: ft.Page):
    page.title = "Ping 工具"
    page.window_width = 600
    page.window_height = 500
    page.padding = 20

    # 创建UI组件
    host_input = ft.TextField(
        label="目标主机",
        value="*********",
        width=300
    )
    
    output_field = ft.TextField(
        label="Ping 结果",
        multiline=True,
        read_only=True,
        min_lines=15,
        max_lines=20,
        width=500
    )

    # 控制变量
    is_running = False

    def run_ping(e):
        nonlocal is_running
        is_running = True
        start_button.disabled = True
        stop_button.disabled = False
        host_input.disabled = True
        page.update()

        def ping_thread():
            while is_running:
                try:
                    # 清空输出
                    output_field.value = ""
                    page.update()

                    # 使用ping3执行ping
                    for i in range(4):  # 发送4次ping
                        if not is_running:
                            break
                        
                        delay = ping(host_input.value, timeout=2)
                        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
                        
                        if delay is None:
                            output_field.value += f"[{timestamp}] 请求超时\n"
                        elif delay is False:
                            output_field.value += f"[{timestamp}] 无法解析主机地址\n"
                        else:
                            delay_ms = round(delay * 1000, 2)
                            output_field.value += f"[{timestamp}] 来自 {host_input.value} 的回复: 时间={delay_ms}ms\n"
                        
                        page.update()
                        import time
                        time.sleep(1)  # 每次ping之间暂停1秒
                    break
                except Exception as e:
                    output_field.value += f"\n错误: {str(e)}"
                    page.update()
                    break

                if is_running:
                    time.sleep(1)  # 每组ping之间暂停1秒

        # 在新线程中运行ping
        import threading
        threading.Thread(target=ping_thread, daemon=True).start()

    def stop_ping(e):
        nonlocal is_running
        is_running = False
        start_button.disabled = False
        stop_button.disabled = True
        host_input.disabled = False
        page.update()

    # 创建按钮
    start_button = ft.ElevatedButton(
        "开始 Ping",
        on_click=run_ping
    )
    
    stop_button = ft.ElevatedButton(
        "停止 Ping",
        on_click=stop_ping,
        disabled=True
    )

    # 添加组件到页面
    page.add(
        ft.Column([
            ft.Text("Ping 工具", size=20, weight=ft.FontWeight.BOLD),
            host_input,
            ft.Row([start_button, stop_button], spacing=10),
            output_field
        ], spacing=20)
    )

ft.app(target=main)