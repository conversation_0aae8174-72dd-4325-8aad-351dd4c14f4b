# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON>o
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-22 20:49+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/settings/options_setting_card/index.rst:2
#: fb4784e85f1640319a8b4ab4d32cf2e0
msgid "options_setting_card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/options_setting_card/index.rst:8
#: 1f58b211719e41f3a0660fa8cff0dac9
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/options_setting_card/index.rst:18:<autosummary>:1
#: 40d9443538c041159b70477e9bca828e
msgid ""
":py:obj:`OptionsSettingCard "
"<qfluentwidgets.components.settings.options_setting_card.OptionsSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/options_setting_card/index.rst:23
#: ../../source/autoapi/qfluentwidgets/components/settings/options_setting_card/index.rst:18:<autosummary>:1
#: 84eb195b5d63401a91737abc8fcf2f06 c6568e97d6ae456e8bb3a163fe9c4aca
msgid "setting card with a group of options"
msgstr "选项设置卡"

#: ../../source/autoapi/qfluentwidgets/components/settings/options_setting_card/index.rst:21
#: e229abd7b4fb4fd3a5a44fad74350ce0
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.settings.expand_setting_card.ExpandSettingCard`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/options_setting_card/index.rst:31
#: f8bb06d247a9448d8fd1aa31a69b4520
msgid "select button according to the value"
msgstr "根据指定值选中按钮"

