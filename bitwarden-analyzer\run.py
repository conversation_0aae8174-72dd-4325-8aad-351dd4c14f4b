#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bitwarden 数据筛查工具启动脚本
"""

import os
import sys
import webbrowser
import time
import threading
from app import app

def open_browser():
    """延迟打开浏览器"""
    time.sleep(1.5)
    webbrowser.open('http://localhost:5000')

def main():
    print("=" * 60)
    print("🔐 Bitwarden 数据筛查工具 - Python版")
    print("=" * 60)
    print("功能特性:")
    print("  ✅ 支持状态保存和恢复")
    print("  ✅ 数据持久化存储")
    print("  ✅ 后台运行分析")
    print("  ✅ 实时进度更新")
    print("  ✅ 支持暂停和恢复")
    print("  ✅ 详细的测试报告")
    print("-" * 60)
    print("正在启动服务器...")
    
    # 在后台线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        print("✅ 服务器启动成功!")
        print("📱 访问地址: http://localhost:5000")
        print("🛑 按 Ctrl+C 停止服务器")
        print("-" * 60)
        
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
