# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON>o
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-06-01 17:56+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/date_time/calendar_picker/index.rst:2
#: 2c7a4eb494b047b7b2d1d878a5f8922d
msgid "calendar_picker"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/calendar_picker/index.rst:8
#: a61263caff454426a5c4ef23aaba94c6
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/calendar_picker/index.rst:18:<autosummary>:1
#: 73ca36eaf2804370a7b3929299e0cd0a
msgid ""
":py:obj:`CalendarPicker "
"<qfluentwidgets.components.date_time.calendar_picker.CalendarPicker>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/calendar_picker/index.rst:23
#: ../../source/autoapi/qfluentwidgets/components/date_time/calendar_picker/index.rst:18:<autosummary>:1
#: 9f8c9513514745018e2949f4c9b6c9af abb5f98b738344ab90bb1e0a3b89e2b5
msgid "Calendar picker"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/calendar_picker/index.rst:21
#: be59c5e217d34b2eb5088f7d3db90341
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QPushButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/calendar_picker/index.rst:31
#: 67e9fa08ecb8439dbd377d39c5b187a1
msgid "set the selected date"
msgstr ""

