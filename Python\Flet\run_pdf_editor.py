#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文本替换编辑工具 - 快速启动脚本
"""

import sys
import os

def main():
    print("启动PDF文本替换编辑工具...")
    
    try:
        # 导入主程序
        from pdf_text_editor import main as pdf_main
        import flet as ft
        
        print("✓ 所有模块导入成功")
        print("✓ 正在启动GUI界面...")
        
        # 启动应用
        ft.app(target=pdf_main)
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所有依赖库:")
        print("pip install flet PyPDF2 pdfplumber reportlab")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
