import flet
from flet import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>ield, icons
def main(page: Page):
    page.title = "Cloudflare DNS Editor"
    api_token_entry = TextField(caption="API Token", place_holder="Enter your API token here")
    domain_entry = TextField(caption="Domain", place_holder="Enter your Domain here")
    ip_entry = TextField(caption="IP", place_holder="Enter your IP here")
    # def update_dns(e):
    #     token = api_token_entry.value
    #     domain = domain_entry.value
    #     ip = ip_entry.value
    #     cf = Cloudflare(token)
    #     records = cf.list_dns_records(domain)
    #     for record in records:
    #         if record['type'] == 'A':
    #             cf.update_dns_record(domain, record['id'], ip)
    # confirm_button = IconButton(icons.CHECK, caption="Update DNS", on_click=update_dns)
    page.add(
        Row(
            [
                api_token_entry,
                domain_entry,
                ip_entry,
                confirm_button
            ],
            alignment="start"
        )
    )
    print("API Token: ", api_token_entry.value)
flet.app(target=main)
