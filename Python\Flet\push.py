import flet as ft
import requests

api = '65T3DFOXV0GOIHM5G6OXDLNX055ZHQ'
url = "https://api.anpush.com/push/"+api

# Wexin  95952
# Tg   19618
# payload = {
#     "title": "Test",
#     "content": "Tets",
#     "channel": "19618"
# }

headers = {
    "Content-Type": "application/x-www-form-urlencoded"
}




def main(page):
    def send_clicked(e):
        payload = {
            "title": "Test",
            "content": msg.value,
            "channel": "19618"}
        response = requests.post(url, headers=headers, data=payload)
        msg.value = ''
        page.update()
        return response.text
    msg = ft.TextField()
    send_view = ft.Row(controls=[
        msg,
        ft.TextButton('Send', on_click=send_clicked)

    ])
    page.add(send_view)
    page.update()

ft.app(target=main)