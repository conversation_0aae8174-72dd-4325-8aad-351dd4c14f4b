{"items": [{"name": "GitHub", "login": {"username": "<EMAIL>", "password": "password123", "uris": [{"uri": "https://github.com"}]}}, {"name": "Google", "login": {"username": "<EMAIL>", "password": "password456", "uris": [{"uri": "https://www.google.com"}]}}, {"name": "WPS账户", "login": {"username": "<EMAIL>", "password": "wps123", "uris": [{"uri": "account.wps.cn"}]}}, {"name": "百度（无协议）", "login": {"username": "<EMAIL>", "password": "baidu123", "uris": [{"uri": "www.baidu.com"}]}}, {"name": "本地测试页面", "login": {"username": "<EMAIL>", "password": "test123", "uris": [{"uri": "file:///h:/OneDrive%20-%20App%20By%20Cc/Test/bitwarden-analyzer/test.html"}]}}, {"name": "内网系统", "login": {"username": "admin", "password": "admin123", "uris": [{"uri": "http://192.168.1.100"}]}}, {"name": "无效网站", "login": {"username": "test", "password": "test123", "uris": [{"uri": "https://this-domain-does-not-exist-12345.com"}]}}, {"name": "重复域名测试1", "login": {"username": "<EMAIL>", "password": "different123", "uris": [{"uri": "https://example.com"}]}}, {"name": "重复域名测试2", "login": {"username": "<EMAIL>", "password": "password123", "uris": [{"uri": "https://example.com/login"}]}}, {"name": "重复密码测试", "login": {"username": "<EMAIL>", "password": "password123", "uris": [{"uri": "https://test.com"}]}}, {"name": "百度", "login": {"username": "<EMAIL>", "password": "baidu123", "uris": [{"uri": "https://baidu.com"}]}}, {"name": "微博", "login": {"username": "weibo_user", "password": "weibo456", "uris": [{"uri": "https://weibo.com"}]}}, {"name": "淘宝", "login": {"username": "taobao_user", "password": "taobao789", "uris": [{"uri": "https://taobao.com"}]}}]}