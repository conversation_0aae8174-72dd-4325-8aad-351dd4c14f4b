# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON>o
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-06-01 17:56+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/index.rst:2
#: bfce60ff792a4671acc104b1cac723a9
msgid "qfluentwidgets"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:10
#: 291c70c436524d128903d23498d82d9b
msgid "A fluent design widgets library based on PyQt5."
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:12
#: 154e90112988474aaa1b8a6804403c79
msgid ""
"Documentation is available in the docstrings and online at https://pyqt-"
"fluent-widgets.readthedocs.io."
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:15
#: 5ab9c8cfe5724670a27fec6b1b2de2fc
msgid ""
"Examples are available at https://github.com/zhiyiYo/PyQt-Fluent-"
"Widgets/tree/master/examples."
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst
#: 142a2f6f0c9f4f5d9eb61fbe5e3c168b
msgid "copyright"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:17
#: f0602bb2a34345a5a0b3495ba5478d0d
msgid "2021 by zhiyiYo."
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst
#: 013e983142694a6ebaf4252aa4a234fc
msgid "license"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:18
#: b28368144d604d2195d05b23a1c7cbd9
msgid "GPLv3, see LICENSE for more details."
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:33
#: 8e8716ea05464ab6b85ac602131c2b98
msgid "Package Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 27df5de9dd664271a399cd9bc8cc5743
msgid ":py:obj:`ColorDialog <qfluentwidgets.ColorDialog>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:224
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 2514363648a240d0b30f3a934af671cc f7b6d57315f040838b830fc56d77ff2f
msgid "Color dialog"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 575be2c98e034b1db40ce92658e85672
msgid ":py:obj:`Dialog <qfluentwidgets.Dialog>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:250
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: badac2a5c65540ba9cb28641176d451e f6283f4930b5465cab04c2967591493b
msgid "Dialog box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: f676e7cc726442fc8950d137c3864613
msgid ":py:obj:`MessageBox <qfluentwidgets.MessageBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:268
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 4a9fb14630dc40fabf3d3b344a918eb0 69de40399f07441b954339b762d8d0b1
msgid "Message box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 6c18d9f5bc1a40629931beb684e27e31
msgid ":py:obj:`FolderListDialog <qfluentwidgets.FolderListDialog>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:286
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 14a2f779110042189681144a00757265 4d4db33541bc4da694b66d5ec14f358d
msgid "Folder list dialog box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 6fa977070ea84a2e82c09a8f1d364cd5
msgid ":py:obj:`MessageDialog <qfluentwidgets.MessageDialog>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:297
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: dd7d7f806a2c4d3d87bd9ae5b8d1680b e1af63cd69714a7f814b42a2e6ece1d1
msgid "Win10 style message dialog box with a mask"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: a382e8a333bc4edf87bce989b0c327a7
msgid ":py:obj:`ExpandLayout <qfluentwidgets.ExpandLayout>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:312
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 650fc3625de149e0bc6f229b6f123399 d63c796d68d04f50b7115a61c1898514
msgid "Expand layout"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 72a3b354886d424982cc1aa82fcf2744
msgid ":py:obj:`FlowLayout <qfluentwidgets.FlowLayout>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:357
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 6bf271c83dcd4dd395df4136e342669d 97ef900f973a4c3ebf99cbc0a08b95a9
msgid "Flow layout"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 4a91357f4cff460fb305824fc1c7e5ed
msgid ":py:obj:`VBoxLayout <qfluentwidgets.VBoxLayout>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:442
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: cdaa3e4a65464def9f1434164a9e07b6 effc9054a5444250b14adce64e5931c1
msgid "Vertical box layout"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: eb482336f1324629b9d1182b83c59d6f
msgid ":py:obj:`SettingCard <qfluentwidgets.SettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:474
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 259188cdca054fd4a012f9ec9a6c8624 b624dad1ce4a4effaa8767b65c262a69
msgid "Setting card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 791c7b39eef44250a6fe5497f8eebe09
msgid ":py:obj:`SwitchSettingCard <qfluentwidgets.SwitchSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:496
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 80a70d343dc6488999170ac03f4108ad b65e1e407b9b4d7e8fddece175cbe9a0
msgid "Setting card with switch button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 6f5e6804c5964df1a2231edd4f5dbdbf
msgid ":py:obj:`RangeSettingCard <qfluentwidgets.RangeSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:515
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 3709928596a54020a2413346305d857f a654d4e718fe47f0b222691eb85622d2
msgid "Setting card with a slider"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 8998f42a69484338b09ee6f117ba7c24
msgid ":py:obj:`PushSettingCard <qfluentwidgets.PushSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:531
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 9f9f85de291b426f952f8f33f467ca1b f6d44eebd1314b20af251f9573635437
msgid "Setting card with a push button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 417aa946c067496e914948c43f518893
msgid ":py:obj:`ColorSettingCard <qfluentwidgets.ColorSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:542
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 37efc21a75f7455885206ca7e23847e1 652f071297d2418f91a3ef1db34b07f6
msgid "Setting card with color picker"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 20304153ed2f4a47a30a4d59abef92a6
msgid ":py:obj:`HyperlinkCard <qfluentwidgets.HyperlinkCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:558
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 6f2823109d3b406082207570a5b6696e d4d6444cb00c407b8965356b7d530e7a
msgid "Hyperlink card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: a935ceacf7454d08abe3656a22c2e857
msgid ":py:obj:`PrimaryPushSettingCard <qfluentwidgets.PrimaryPushSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:565
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 9bb1e0393f5345faa1ca9f25c2ce7845 f609e88df76b4a8a842a54ad9225de93
msgid "Push setting card with primary color"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 856353f033f246a691eec6fb3deeee0c
msgid ":py:obj:`ColorPickerButton <qfluentwidgets.ColorPickerButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:572
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 24a2e43f326048a8a7fcea6a7d8f6410 3020fff55ca54dfdb8064a40ec653a39
msgid "Color picker button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: fde37b4a96944b31b624f99b8aa3f103
msgid ":py:obj:`ComboBoxSettingCard <qfluentwidgets.ComboBoxSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:591
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 261f49da42f5434d8f5dc9c5529cf850 fb04c98ade72456895b20b7b40a27137
msgid "Setting card with a combo box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 93d764a5c7e6410e957966804a400761
msgid ":py:obj:`ExpandSettingCard <qfluentwidgets.ExpandSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:603
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 1459897be7a74e838b3652560b588c99 e441e2b81c8947258c48b77c41211238
msgid "Expandable setting card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 67e3dca4d9734adc86a46891c47d7ec5
msgid ":py:obj:`ExpandGroupSettingCard <qfluentwidgets.ExpandGroupSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:636
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 17f4b4b01c7145bd90c68981e222b234 d01d79d227734713924bd75bfe05c418
msgid "Expand group setting card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: bdf95a217e7f4bb6a72f7c88b8b88e35
msgid ":py:obj:`FolderListSettingCard <qfluentwidgets.FolderListSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:648
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 37485d18879840079ce624444909f5b2 f539136b634a4b33af783c645decacfc
msgid "Folder list setting card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 4f693b3b6f2540d59a1ca7999d8e41ad
msgid ":py:obj:`OptionsSettingCard <qfluentwidgets.OptionsSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:659
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 1f278c1679e74879bff8529580898877 2c11af266393450a985134bfb7d1da30
msgid "setting card with a group of options"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 326665b056e443a48a3009d17f4b051a
msgid ":py:obj:`CustomColorSettingCard <qfluentwidgets.CustomColorSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:675
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 505727bc07774f0390d1b67cd61ed7fd c9dc07acaef64e90921e906a23ca7f4f
msgid "Custom color setting card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: ec241d5fc88e4b0c8cb7a9311e0ef55c
msgid ":py:obj:`SettingCardGroup <qfluentwidgets.SettingCardGroup>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:686
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 5e677c9f054640b4b0decf85aca2790b c091c32536024e799726e879947ccdfa
msgid "Setting card group"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: e48880bcbb964ae9824222d24caba9fa
msgid ":py:obj:`DropDownPushButton <qfluentwidgets.DropDownPushButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:706
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 02a5666e76304846882d8244fcc834d9 62c84c8301e34e4e987d471a1c60a57c
msgid "Drop down push button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 5d2b861f322f4da09ad26de9420ade14
msgid ":py:obj:`DropDownToolButton <qfluentwidgets.DropDownToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:719
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 7033b9c4bffa4733859f5b09d55fa7ed de6191a692b8490c884805728f1d6e47
msgid "Drop down tool button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 101d538172b84459b0c3ea9a838d827d
msgid ":py:obj:`PrimaryPushButton <qfluentwidgets.PrimaryPushButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:732
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 19bbb1bd05524d2d93712d1f8cd44e07 ba40eec1b21546b8a528dc8fbb45bd80
msgid "Primary color push button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 1e78bfd9f054401dbf0ec5d7fb546a67
msgid ":py:obj:`PushButton <qfluentwidgets.PushButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:739
#: ../../source/autoapi/qfluentwidgets/index.rst:832
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 2e5b86d7e600495fbfdd7ef224473a85 3d885c362d684cb38e4fbc9cf13a809c
#: 42526f6670e347c79a8334fe1894f496
msgid "push button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: df608b2e1a7649f699f3fc52b4eff659
msgid ":py:obj:`RadioButton <qfluentwidgets.RadioButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:770
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 4a825e4afe7a449eb2d765930570eebd 70a39545bd2c443889fe02aa95841e64
msgid "Radio button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 67b0c93a21744179b1b05ed59b575fa6
msgid ":py:obj:`HyperlinkButton <qfluentwidgets.HyperlinkButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:777
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 7859303fbb6a45fc90af94292f64bfd1 d3186ad00d6a45f7aae260b7857f106b
msgid "Hyperlink button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 165395c8f31c46fca0fd34e8a549c522
msgid ":py:obj:`ToolButton <qfluentwidgets.ToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:794
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: f71da6b6c89b485ebad5bdaa0ca2a34f fb26514ea5d24a9986f1da20996c89e0
msgid "Tool button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 12b8f149983d4284a98731f0cc20a7fd
msgid ":py:obj:`TransparentToolButton <qfluentwidgets.TransparentToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:825
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 1f9939146d9944f7aa52f351835b7f5c dc5e3f8fa81d4c8195871e7b568e1158
msgid "Transparent background tool button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 3d35f218ce61496bac57737e718933c4
msgid ":py:obj:`ToggleButton <qfluentwidgets.ToggleButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 171fab24393b44c193085dadccb2ef87
msgid ":py:obj:`SplitWidgetBase <qfluentwidgets.SplitWidgetBase>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:839
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 6891e43c462f415f9b5ebc85f33b8da3 d1431ed1c0ab4e089b76bd1d1f627eb6
msgid "Split widget base class"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 729e15d09b4e45779195c200fb9620e7
msgid ":py:obj:`SplitPushButton <qfluentwidgets.SplitPushButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:876
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 32462acfee8649c6b869a929e7c18d03 e45c93927aa3497aa40670f86dbad76f
msgid "Split push button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 8c288f9ffcc54b83a20a66ce785d0944
msgid ":py:obj:`SplitToolButton <qfluentwidgets.SplitToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:910
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: cbb9eb261314401c8149cad7af8b5360 cee86c568bf747dda42152a9b92c09e9
msgid "Split tool button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 0b5536a53a83486b868af9e28d8a2bc3
msgid ":py:obj:`PrimaryToolButton <qfluentwidgets.PrimaryToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:934
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 7c17ba8d09c44406b1dc7d2a66f3f237 dc1aaa4f1aff430ab6937a83263c5e68
msgid "Primary color tool button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: f2ec03d6994a46c4bf47bf242370306e
msgid ":py:obj:`PrimarySplitPushButton <qfluentwidgets.PrimarySplitPushButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:941
#: ../../source/autoapi/qfluentwidgets/index.rst:948
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 598c1e31b7a54c9498a8606b3fc599aa 83bde08815bf4893b6451941522675ec
#: a3b13363ac43457db5946af3ab6eafeb fcb6184d36f6474596bdd619fdcf564b
msgid "Primary split push button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 1ae70b70957f40b3bd01d19e9c587fc8
msgid ":py:obj:`PrimarySplitToolButton <qfluentwidgets.PrimarySplitToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 615ae8ae46ff4f6d8dd92fa6caf931fd
msgid ""
":py:obj:`PrimaryDropDownPushButton "
"<qfluentwidgets.PrimaryDropDownPushButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:955
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 6411d52b730048a79a7f3c18c78ce3bc ce16d11aef3847c89caa959752ca8e1d
msgid "Primary color drop down push button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 56e4d71ed85647748e125d8f0155f1d9
msgid ""
":py:obj:`PrimaryDropDownToolButton "
"<qfluentwidgets.PrimaryDropDownToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:968
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 6bdf8198ffff457e86004aa65aa2dd00 e2845619504c4f58b015fe017abbb328
msgid "Primary drop down tool button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 6b20637b37ab4f7f9132d613001b61f6
msgid ":py:obj:`CheckBox <qfluentwidgets.CheckBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:981
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 0ee7ebd1af6d414686f4fbe4a6fe091f 8b222be1ff174b8f801210d79b4e71cc
msgid "Check box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 03cbf886337c47749c0738275ceeaf28
msgid ":py:obj:`ComboBox <qfluentwidgets.ComboBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:991
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: d810aeab91064a51a77e56e7c9d74ed9 e1d2b695e20448d2b0cb5c15d57544d0
msgid "Combo box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: d9cc87c78a614071aa7efdad4c161817
msgid ":py:obj:`EditableComboBox <qfluentwidgets.EditableComboBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1015
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 2590407a3cbc4f278083bf113783b749 ddfab4c3e9d34fa7872fad36cd523f65
msgid "Editable combo box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: d63feb2a9b6c42768509697bb2bd2b8c
msgid ":py:obj:`LineEdit <qfluentwidgets.LineEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1038
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 2d9e43052c9c4c848b8973fcf6da3a8b 9ed6af0523e74f1d98009122919f2766
msgid "Line edit"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: c181f5f5935e4d85819404176e5ad74c
msgid ":py:obj:`TextEdit <qfluentwidgets.TextEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1063
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 05e8c843707441acb075716fc03db110 e51f32f3500a44dc9e066acd318b2506
msgid "Text edit"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 989aaf2d52db49acafb7e1e669a592ae
msgid ":py:obj:`PlainTextEdit <qfluentwidgets.PlainTextEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1073
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 01c573fb7bb64a0c9dd4585b3b65bd5b 296a84164e0c42e38fa94b43be1fe6f0
msgid "Plain text edit"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: b018b845a0814ad4981ba57ae344c06d
msgid ":py:obj:`LineEditButton <qfluentwidgets.LineEditButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1083
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 3d3699b411aa4af29ae3ee56ace04934 8e0cb8ccb4dd45dab83e16efbe8bc6d3
msgid "Line edit button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: cb9adbec4d0d4e0ab2b588df353653c4
msgid ":py:obj:`SearchLineEdit <qfluentwidgets.SearchLineEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1099
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 219ae0017cc74fc5930a4d9953d20715 31dc63c691de408b901b2196b760ac4b
msgid "Search line edit"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: c4d3335e3b6b4022abd8f58f645026c7
msgid ":py:obj:`IconWidget <qfluentwidgets.IconWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1122
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: abb15b5c171c434e816ec0cd5d60c66e c10018f2dd9d492b8da83663d003906e
msgid "Icon widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 7e7d0ba0478743d7b19911094a40eb1c
msgid ":py:obj:`PixmapLabel <qfluentwidgets.PixmapLabel>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1142
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 40b193b1677c471c9ab0ff58f9cb4ee3 6859c610826241be8ff2c08567babeac
msgid "Label for high dpi pixmap"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 03cc8ff321bc4ed8be7a4b60f81cb87a
msgid ":py:obj:`ListWidget <qfluentwidgets.ListWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1158
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 2182bc8eb85a4d4d9a9f1bec5d960c99 ea8cb1d5514a44e7b726f000077727b7
msgid "List widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: d1c2560537de42abb2e2b6bf833d942d
msgid ":py:obj:`ListView <qfluentwidgets.ListView>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1171
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 51f2ff2404a847d4befe57dd619f68bf 5f306805bee044c387c88e8511cded23
msgid "List view"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 7c056f4d77d8407b9fa835b6f4e88b42
msgid ":py:obj:`ListItemDelegate <qfluentwidgets.ListItemDelegate>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1178
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 24413bafcf3f439481c7d649d94db4ee 895554a173f54ad78efcbca098cd2846
msgid "List item delegate"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: b3ad280083584331aa6714cb546292ec
msgid ":py:obj:`DWMMenu <qfluentwidgets.DWMMenu>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1185
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 53d34040f9924f0a918f9c76e39fe2f9 d1e709e02cfe4f61a4eabda9b08e2131
msgid "A menu with DWM shadow"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: b261fb53e91d4213b74c6ad7532c1124
msgid ":py:obj:`LineEditMenu <qfluentwidgets.LineEditMenu>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1195
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 2807f171289845f788c975ac36f91892 6f4b7f30283f41fb875455dc373a70e8
msgid "Line edit menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: d5221f2b32cb495a90aef1e4fad99c38
msgid ":py:obj:`RoundMenu <qfluentwidgets.RoundMenu>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1202
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 27e9a1790a6d44338a229a0c3900ac90 3aa2b38b63464913b32aa7057ac55153
msgid "Round corner menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 253ef47f9146443fb406b7226d620626
msgid ":py:obj:`MenuAnimationManager <qfluentwidgets.MenuAnimationManager>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1349
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 25114681f9f1486d8a6d8e826ba708ee d51cdc14526040569e9b7f3313d61181
msgid "Menu animation manager"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 8d60dc9132d24f679255ef99064d9af6
msgid ":py:obj:`MenuAnimationType <qfluentwidgets.MenuAnimationType>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1378
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 006789a18f5148c1a3d5086959dbf804 b937a2c00a83472eb3588bf4a5c1e726
msgid "Menu animation type"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 9366016df7134ea5a0ce10a73ed96aca
msgid ":py:obj:`InfoBar <qfluentwidgets.InfoBar>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1400
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 72bfceb5c7544272a279ece23b3c3ae5 7937eb9d86e3480baf0da36daa6ab728
msgid "Information bar"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 2e7cc22140d849a493e9e6c871a4d834
msgid ":py:obj:`InfoBarIcon <qfluentwidgets.InfoBarIcon>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1458
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: c010d247f1894e8995f22185e06da5eb ee7db73c986749b4b756394d31bd46b4
msgid "Info bar icon"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: af4ae46e0d894a8cbcbeb292e9344d0a
msgid ":py:obj:`InfoBarPosition <qfluentwidgets.InfoBarPosition>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1498
#: ../../source/autoapi/qfluentwidgets/index.rst:2037
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 4d7167f348b0400caf71dc1e758c8fa1 6addb6e9036948259af7c48ea4f2fc6c
#: d78d6d4e2ae248d7aa8e308f191ff9cc
msgid "Info bar position"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 34c5d2de293d4b8d804259887d96283f
msgid ""
":py:obj:`SingleDirectionScrollArea "
"<qfluentwidgets.SingleDirectionScrollArea>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1540
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 6c19267f23ff4e0d98bcfdae4082de11 bab8b4826028431f8224d1a5a765dbaa
msgid "Single direction scroll area"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 8e3bc77125f242b1b10bc70dc301e935 e053d26b09fd490299f34d986ee6a7d6
msgid ":py:obj:`SmoothMode <qfluentwidgets.SmoothMode>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1566
#: ../../source/autoapi/qfluentwidgets/index.rst:4577
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 804642923fe645fcbb92ab45396a3280 8bd66a0e594a4182877ae9adefc0bbb9
msgid "Smooth mode"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 586b82f785e5403ca04c58951f1e511b
msgid ":py:obj:`SmoothScrollArea <qfluentwidgets.SmoothScrollArea>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1598
#: ../../source/autoapi/qfluentwidgets/index.rst:1621
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: a9c133950c0b472a8f1335cff4c340c4 b1d46f83f9af40f28959f0aff515f34a
#: ec1bef306f2f42638328c7ce654545ea
msgid "Smooth scroll area"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 77d0efff4aee4391b0d13abf470784b9
msgid ":py:obj:`ScrollArea <qfluentwidgets.ScrollArea>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 622415a27167469c8a92bfedbe81bbe3
msgid ":py:obj:`Slider <qfluentwidgets.Slider>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1628
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 3bd8b906b8284071a2bbe714595da39d 78017211430c40c6bf9458f714415cfa
msgid "A slider can be clicked"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: b3a95bab88f94a7681d71efda6fa9d59
msgid ":py:obj:`HollowHandleStyle <qfluentwidgets.HollowHandleStyle>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1642
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 9f2ae5c0bb774b2e988137a319060c70 a0d1a93ef15949dca60ba0c450709b8b
msgid "Hollow handle style"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 60242d8b22be41bdb812baef2f5e8845
msgid ":py:obj:`SpinBox <qfluentwidgets.SpinBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1659
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 2fa065fd4eee4fdaa6b48dab46772e3c d3ab59696abf42488c9a8b19dab67f06
msgid "Spin box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: e86278031a1147739970dd30c0d8f09a
msgid ":py:obj:`DoubleSpinBox <qfluentwidgets.DoubleSpinBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1669
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 4ca7612133bd4c0fba616c0460bff2a7 573d6417bcaa480891e35d8b46d1b9c3
msgid "Double spin box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: d94367f6c23f441a9253d21bfca362d4
msgid ":py:obj:`DateEdit <qfluentwidgets.DateEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1679
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 5e7705a7a274426f949135bd0fc86244 84c08dbc0d024f90b10e576dff6ca322
msgid "Date edit"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: e8672c8749f041438568c8caa8ce19c4
msgid ":py:obj:`DateTimeEdit <qfluentwidgets.DateTimeEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1689
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 063bf0d86b144c379b83ef5eaa37fc2d d39bdf09037b433081ad342c9dbdfcc4
msgid "Date time edit"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: d2d4ea3278964c7583317439faa8d010
msgid ":py:obj:`TimeEdit <qfluentwidgets.TimeEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1699
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 3077fe3447334ab9bbb4e763d7ee6f99 5f3bc2b09bce4c8e9fdc518cbe991d34
msgid "Time edit"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: b582eb6a201e4bee9c0118dc90c87575
msgid ":py:obj:`PopUpAniStackedWidget <qfluentwidgets.PopUpAniStackedWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1709
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 78e992e6c0dc4a228bc70629009a660d 848a14b461fb4f2fa5870c3b71730d36
msgid "Stacked widget with pop up animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 141c2efa17fb4d5e8e33b64e0c9e54cf
msgid ""
":py:obj:`OpacityAniStackedWidget "
"<qfluentwidgets.OpacityAniStackedWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1784
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 421581b7175b40e9bd99c630a82969f5 ff72d3fd59c3401d86ac4bcf11cebd58
msgid "Stacked widget with fade in and fade out animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: eeb435056c26425186a66c4cdd1055ba
msgid ":py:obj:`StateToolTip <qfluentwidgets.StateToolTip>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1800
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 57503feeda1f4977ae10eebe8ed9c5f0 7019026816fc4f308317a422fae8cc92
msgid "State tooltip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 2f01597fee384ed4bfe9c77f8fc64b06
msgid ":py:obj:`SwitchButton <qfluentwidgets.SwitchButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1836
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: bb59f55aedb74ddbba71ba35224a3947 c000017718fb4eeb958f37284e5c833b
msgid "Switch button class"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 9119b0e35cf84efbbc880b2d044e1198
msgid ":py:obj:`IndicatorPosition <qfluentwidgets.IndicatorPosition>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1907
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 337bed6891a846c09364632a88501518 f6121d2364a644eab5b70492d2530fc9
msgid "Indicator position"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 31d977276ae040e290bf106950f6a4af
msgid ":py:obj:`TableView <qfluentwidgets.TableView>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1924
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 7d0db9a6d66c4583b550302286ca6a72 81d6fd8acb9d46a1a89140168a5df8d9
msgid "Table view"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: b8eb5cb7105344f4be368a2906a76df6
msgid ":py:obj:`TableWidget <qfluentwidgets.TableWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1931
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 262fa86398fe41c9b9585ec9d91eddf5 63a1fdecd9d84a9d94fab811ff2246df
msgid "Table widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: d3e5e234d90c4cd9bf5290a2ae8338de
msgid ":py:obj:`TableItemDelegate <qfluentwidgets.TableItemDelegate>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 5718098a00574fc099892b05658c9c55
msgid ":py:obj:`ToolTip <qfluentwidgets.ToolTip>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1973
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 04e1965442fe44a38d19992a32984270 774b6b7f3308407594fba84647403b27
msgid "Tool tip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 1e75f88e11f746c29181f664bb59dd5d
msgid ":py:obj:`ToolTipFilter <qfluentwidgets.ToolTipFilter>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2012
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: bc5ea3d4012249c7831592d00183699f d26a87efe39041369578d88c08ced295
msgid "Tool tip filter"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: e82ef85435db4a0695762e8be840757c
msgid ":py:obj:`ToolTipPosition <qfluentwidgets.ToolTipPosition>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 204cb6c33e0a4d7b82cbb20919012b40
msgid ":py:obj:`TreeWidget <qfluentwidgets.TreeWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2084
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 425d6935372644b5a5336ec6909498e8 55851ec2cc6f49d581a0e52dbcd44206
msgid "Tree widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: ef47cf36a23546559684b99b7dbed2e7
msgid ":py:obj:`TreeView <qfluentwidgets.TreeView>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2091
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 0f2a84e09b734efdadf77bb98c45668b 7db8350ad71140a6afa08eea4954dfe4
msgid "Tree view"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 6c6155d07fa942948aea8227c0d3f98c
msgid ":py:obj:`TreeItemDelegate <qfluentwidgets.TreeItemDelegate>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2098
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 4b27b6b4ee1f4f6699cc57084e0aa4eb 7bec9d8076a8407a9b19b9cbe2b3e0a2
msgid "Tree item delegate"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: a6e47c278f7749f39b8b9ded219c8054
msgid ":py:obj:`CycleListWidget <qfluentwidgets.CycleListWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2111
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 5ee3483e5f2e459b9930e1220dc7702a e5f44f6d30a94262bb452618f943ece7
msgid "Cycle list widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 60f2342c31f047039bf0af7d59fd74a1
msgid ""
":py:obj:`IndeterminateProgressBar "
"<qfluentwidgets.IndeterminateProgressBar>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2182
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: d7635b3fd56c4c7bb51e39dc633b7e8a f336209b662049de9d3759b52914c938
msgid "Indeterminate progress bar"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 3625d270d09142ea9db71f7a0d513bc3
msgid ":py:obj:`ProgressBar <qfluentwidgets.ProgressBar>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: fb29ae478a1240f896b1c44b28f08d96
msgid ":py:obj:`ProgressRing <qfluentwidgets.ProgressRing>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2290
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 6d4827b9d5e646d5959fcd48529eb1e7 c77f28ea3f7144e19c937021586c708b
msgid "Progress ring"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: cb32ed8107374d19be1eaf45d98075c3
msgid ":py:obj:`ScrollBar <qfluentwidgets.ScrollBar>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2300
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 53add8b4476d46ea805705c40e0108f2 e173101fa3dc406788f985cd0ce51415
msgid "Fluent scroll bar"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: fa8942673c7c4608a56bdec820e2cc6b
msgid ":py:obj:`SmoothScrollBar <qfluentwidgets.SmoothScrollBar>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2411
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 7644dcee4d124311a4fa4fd35da7f154 943e3453e3a64a30bb9c5fbfd59aa923
msgid "Smooth scroll bar"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 7e253c9aa8df4f9790fe01f3c850915d
msgid ":py:obj:`SmoothScrollDelegate <qfluentwidgets.SmoothScrollDelegate>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2453
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: c28c92f5d4fa4de69209f300c0cd7419 dbc5b3f2372845e2ad3cf4938920d4e8
msgid "Smooth scroll delegate"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: c461d7fe7aa34deaa1575165b1e949c1
msgid ":py:obj:`NavigationWidget <qfluentwidgets.NavigationWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2469
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 2078eaf39d0a4d6d98449300fa85900b c4c2e6d3b3394971aaba318a363a9b16
msgid "Navigation widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: ff7d91c8bf034bc9b7e4500c8f6ed59e
msgid ":py:obj:`NavigationPushButton <qfluentwidgets.NavigationPushButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2512
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 401f4ede08c34fd884db512ba19b7139 4ec1c839cab54f688bc25c422586f8a6
msgid "Navigation push button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 6596a4a2558e46a28bd34a27927d384c
msgid ":py:obj:`NavigationSeparator <qfluentwidgets.NavigationSeparator>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2534
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 214649ec070c4f4e8540e32eb79b5006 ae0caad13cf54d9e96ee532f017f97eb
msgid "Navigation Separator"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 1570959ffb6b4cc7b17eb9a3f5e48c9a
msgid ":py:obj:`NavigationToolButton <qfluentwidgets.NavigationToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2549
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 6095007503214d3f92ba8c3eb2fa951f 8fec73b0449344838553f555e1ed6e02
msgid "Navigation tool button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 24fdc6cc2eff4a198b913cf49dd33748
msgid ":py:obj:`NavigationTreeWidget <qfluentwidgets.NavigationTreeWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2561
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 5b4fbebaa0b642359230d3cc02ebcd0f 8c80401e840d42409dabba2a4bff5d01
msgid "Navigation tree widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: b41740606b214f43bd1b010571f6d3bf
msgid ""
":py:obj:`NavigationTreeWidgetBase "
"<qfluentwidgets.NavigationTreeWidgetBase>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2651
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 0097a9721add441c84605514672f21b5 955da200385747f9acddb63a2104e86c
msgid "Navigation tree widget base class"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 4549032c8af044eea43a18bc18ce2080
msgid ":py:obj:`NavigationPanel <qfluentwidgets.NavigationPanel>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2718
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 063689f08df2457bb41d7a21ad0f13f2 be9fc6bda7a6409887c98ca8ba7e98e1
msgid "Navigation panel"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 3dc629c897c44f648644fab799aace81
msgid ":py:obj:`NavigationItemPosition <qfluentwidgets.NavigationItemPosition>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2937
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 35f0528e568f48c0933b538258826071 7e07fc41d9eb4e6ab9ebab3ae01bb780
msgid "Navigation item position"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 8513faa0527e4bc6b91ccd8bf0d6b8d3
msgid ":py:obj:`NavigationDisplayMode <qfluentwidgets.NavigationDisplayMode>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2959
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 2732ed41e0d54963abc5645915e1d3a8 d6e49326222648b8980dc5d875695f11
msgid "Navigation display mode"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 1f26ab4a78fa4fb5a7a368bdc67b5b68
msgid ":py:obj:`NavigationInterface <qfluentwidgets.NavigationInterface>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2986
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 849ed139a98341d7914aa5a326fa9c14 bd915d0d844c4bc782bc086186558075
msgid "Navigation interface"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 9dd537b0d7ae4ae2997fe5162f45f1fb
msgid ":py:obj:`Pivot <qfluentwidgets.Pivot>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3177
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: e1cd5741db944880b0a9ca423bf0abba f3ca6eb013514a66be5e1bec4a4f8e67
msgid "Pivot"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 48037981231e45c19b00f99dcc875132
msgid ":py:obj:`PivotItem <qfluentwidgets.PivotItem>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3234
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 12f8aaab4bc04668bf05b38d6f7febb8 a03bbad774994569a25d30822a97cc34
msgid "Pivot item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 363d7b360dbb47d78136c5352e61b8c1
msgid ":py:obj:`CalendarPicker <qfluentwidgets.CalendarPicker>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3251
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 2aa4fea673fc473b82f425a194e6bf40
msgid "Calendar picker"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 1b7819a6a9f447e0af654ee81fe80cb0
msgid ":py:obj:`DatePickerBase <qfluentwidgets.DatePickerBase>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3273
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: c28e757fb6a5426fb0ced2f8e8b243b8
msgid "Date picker base class"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 363d7b360dbb47d78136c5352e61b8c1
msgid ":py:obj:`DatePicker <qfluentwidgets.DatePicker>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3308
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 9f8820d1a89e4f10abd46c4d130a86c3
msgid "Date picker"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: e849f6cd3576400a8096246811d9760b
msgid ":py:obj:`ZhDatePicker <qfluentwidgets.ZhDatePicker>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3350
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 2aa4fea673fc473b82f425a194e6bf40
msgid "Chinese date picker"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: e3aa052e1bb74ce18b3fcc8ddca96cdb
msgid ":py:obj:`PickerBase <qfluentwidgets.PickerBase>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3357
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: e85d6979693242739e7a92c8e11d51b0
msgid "Picker base class"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 28e7d0bc50c746cab6103a11868137bf
msgid ":py:obj:`PickerPanel <qfluentwidgets.PickerPanel>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3478
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 93218fbe278f46c99e8571561870568a
msgid "picker panel"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: aa83ac1af6ed4fe48b0fdc8e5acc2b94
msgid ":py:obj:`PickerColumnFormatter <qfluentwidgets.PickerColumnFormatter>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3555
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: b700c16c96eb4e4db45e75e9ed7de209
msgid "Picker column formatter"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 9bc1defc59c442c0a7a47b54eac1435b
msgid ":py:obj:`TimePicker <qfluentwidgets.TimePicker>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3572
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 7b1d63e7003b4518af777f90a37276ed
msgid "24 hours time picker"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 4a64b86f8c734d94a6558d8c31905111
msgid ":py:obj:`AMTimePicker <qfluentwidgets.AMTimePicker>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3599
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 709e4bfbb5e7480b87e489b5bf48b147
msgid "AM/PM time picker"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: feafc1747d8e40dc98a178c94093a18d
msgid ":py:obj:`TextWrap <qfluentwidgets.TextWrap>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3647
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: c64bea71bf114e51b03644a5b046f6fa
msgid "Text wrap"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 5b5b1f38c4d3408fac1183cd34cf86e4
msgid ":py:obj:`Action <qfluentwidgets.Action>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3690
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: c7d40f673376490999168fb438c5562a
msgid "Fluent action"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 566662c7b860423f8e2af75bff18e05e
msgid ":py:obj:`Icon <qfluentwidgets.Icon>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: de53a711541240b79c754efebd75484b
msgid ":py:obj:`FluentIcon <qfluentwidgets.FluentIcon>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3729
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: e6626709767f4694a756e25cb4c17cd0
msgid "Fluent icon"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 2f1acd7566ef4898bf33936cf17e04f0
msgid ":py:obj:`FluentIconBase <qfluentwidgets.FluentIconBase>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4201
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: cf1b46699b1a47d2b4786825ba574b2d
msgid "Fluent icon base class"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: b4e29a8b1f1e428fa0906cb09f58fd75
msgid ":py:obj:`ThemeColor <qfluentwidgets.ThemeColor>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4327
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: c67d17b98fea4ec4a7fef660dd6dae17
msgid "Theme color type"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: ce5b012098954e239d298c1efde1fbce
msgid ":py:obj:`FluentStyleSheet <qfluentwidgets.FluentStyleSheet>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4406
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 610b2e9e0d1e473086a31c01ea9145a1
msgid "Fluent style sheet"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 103f2fe2f95445b38dbfc0fa1e30e2cd
msgid ":py:obj:`StyleSheetBase <qfluentwidgets.StyleSheetBase>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4541
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 2730ca3b79a54c1c96e63f74d106ac7a
msgid "Style sheet base class"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 0f8439d820c44e278b72bb80671d85c2
msgid ":py:obj:`SmoothScroll <qfluentwidgets.SmoothScroll>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4562
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 107275d3584944e38933127a62545847
msgid "Scroll smoothly"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 79c0dee865484231b52fae794d33390e
msgid ":py:obj:`FluentTranslator <qfluentwidgets.FluentTranslator>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4609
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 494a889998944e6b84ccf11238a012dd
msgid "Translator of fluent widgets"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 6e3333d17d774a0db6e9b3c49d68c5ad
msgid ":py:obj:`Router <qfluentwidgets.Router>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4625
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: b1417415a6ec4c5ba7eedd19bbc025c8
msgid "Router"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 01b70941482549ba943ce22c2ba89645
msgid ":py:obj:`Theme <qfluentwidgets.Theme>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4674
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 3312b23185164b3287d268125439b0db
msgid "Theme enumeration"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 97ab319516484d1cb5ccdb88f16c38f6
msgid ":py:obj:`ConfigValidator <qfluentwidgets.ConfigValidator>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4694
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 72184a5d705d44748d080f141289a935
msgid "Config validator"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 710c2728be374868a79adfd1719e2841
msgid ":py:obj:`RangeValidator <qfluentwidgets.RangeValidator>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4711
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: c9552ff5692f4d809a5e2928e4bb6072
msgid "Range validator"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: c8098c368e41430f8e01200d0b86c28f
msgid ":py:obj:`OptionsValidator <qfluentwidgets.OptionsValidator>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4728
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 7739c756e753432b93a0fae83d840f92
msgid "Options validator"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: a151b641862d4d54b944077d3beb6946
msgid ":py:obj:`BoolValidator <qfluentwidgets.BoolValidator>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4745
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: fb81b29165464ed88fa0f9c4711fba43
msgid "Boolean validator"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 614e4b23ff1a4b518d062a76e32dc38a
msgid ":py:obj:`FolderValidator <qfluentwidgets.FolderValidator>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4752
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 5357618837784460ac421cd647dfc852
msgid "Folder validator"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 93ee86d5a12740dfbb60c469f61e0ec1
msgid ":py:obj:`FolderListValidator <qfluentwidgets.FolderListValidator>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4769
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: e9983ac211bc4b62a140eb0c28c114ed
msgid "Folder list validator"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 27e1876942e7499bb9364b08cd984813
msgid ":py:obj:`ColorValidator <qfluentwidgets.ColorValidator>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4786
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 5170fdb509f949a1a9f3f5a466ff4f8f
msgid "RGB color validator"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: a3299d7a54534f5aade2c267dde3aa50
msgid ":py:obj:`ConfigSerializer <qfluentwidgets.ConfigSerializer>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4801
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: f71032238fbe4a9eaf85e0ea9de6174c
msgid "Config serializer"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 9f669606f3544d23b7c1c61022d4393d
msgid ":py:obj:`EnumSerializer <qfluentwidgets.EnumSerializer>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4818
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: a1360a82763843d8a092d7ec1b775264
msgid "enumeration class serializer"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: a5581d60ad2a480fb7a3eca14732e94b
msgid ":py:obj:`ColorSerializer <qfluentwidgets.ColorSerializer>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4835
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 4cc84b03cf9c4c91913e3da2c3a36887
msgid "QColor serializer"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: baa6fbb64e714db18f2da4da65207339
msgid ":py:obj:`ConfigItem <qfluentwidgets.ConfigItem>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4852
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 5013fb50d457485c82f412481bbc2ce1
msgid "Config item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 0618a89da5f0452b88d72e361c8afaf1
msgid ":py:obj:`RangeConfigItem <qfluentwidgets.RangeConfigItem>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4882
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: d1da26bf82944d2186d6e1a00839ab0d
msgid "Config item of range"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: d7c0ff7d8d7b49ef8becd85e7846abfa
msgid ":py:obj:`OptionsConfigItem <qfluentwidgets.OptionsConfigItem>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4897
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: bd81ae11f3e1492e92c405394c3093ab
msgid "Config item with options"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 4d4e6ab473b14bb98d288a94b33f3cb4
msgid ":py:obj:`ColorConfigItem <qfluentwidgets.ColorConfigItem>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4910
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: fd75ba698b9e4ca5a3a17b49e817d2ea
msgid "Color config item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 3bd26e05595e4599ab891b9923502440
msgid ":py:obj:`QConfig <qfluentwidgets.QConfig>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4920
#: ../../source/autoapi/qfluentwidgets/index.rst:185:<autosummary>:1
#: 0f5dc63475c2427983f8a60b6cea42a7
msgid "Config of app"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: dff2b97b52024cbbb85ab824f9214ac3
msgid ""
":py:obj:`setFont <qfluentwidgets.setFont>`\\ \\(widget\\[\\, "
"fontSize\\]\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3624
#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 6c1d356c864b4b8a865f2387f0a98984
msgid "set the font of widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: e98152340651462d9602a2f41e6d31ed
msgid ":py:obj:`getFont <qfluentwidgets.getFont>`\\ \\(\\[fontSize\\]\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3637
#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 7716c400d78c45939fbb86d01babec8d
msgid "create font"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 9bff5946cbc54e43b3f367d8fefb15b4
msgid ""
":py:obj:`getIconColor <qfluentwidgets.getIconColor>`\\ \\(\\[theme\\, "
"reverse\\]\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3706
#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 7c72d05aa94d4d1cacf9f048aa078cb8
msgid "get the color of icon based on theme"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 76159491862a42768e5b6ab892110b35
msgid ""
":py:obj:`drawSvgIcon <qfluentwidgets.drawSvgIcon>`\\ \\(icon\\, "
"painter\\, rect\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3711
#: ../../source/autoapi/qfluentwidgets/index.rst:4232
#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 1262c9c5ca5047e19c20ae8647e412ee
msgid "draw svg icon"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 77b3775e75f447b292eddcd2fdd831c7
msgid ""
":py:obj:`drawIcon <qfluentwidgets.drawIcon>`\\ \\(icon\\, painter\\, "
"rect\\, \\*\\*attributes\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4182
#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: ecc36d5be3f24fb7b240bdd60dc22dd0
msgid "draw icon"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 775b8944ffc74856aaa541238a315c78
msgid ""
":py:obj:`writeSvg <qfluentwidgets.writeSvg>`\\ \\(iconPath\\[\\, "
"indexes\\]\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4258
#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: b039635117ef4636801616d40d51376e
msgid "write svg with specified attributes"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: d92d5ed24a2045c39f783409bd71dc4e
msgid ""
":py:obj:`setStyleSheet <qfluentwidgets.setStyleSheet>`\\ \\(widget\\, "
"file\\[\\, theme\\, register\\]\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4279
#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: b40c9ab6553d4bd3b9c88e7f65735344
msgid "set the style sheet of widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: b6042ba5afcb494c908ca6c783c6c8a6
msgid ""
":py:obj:`getStyleSheet <qfluentwidgets.getStyleSheet>`\\ \\(file\\[\\, "
"theme\\]\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4299
#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: c6550c8b44d94d1a9c9aa8d00b7389e4
msgid "get style sheet"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: caac8489154649cd931380cf36bb2ec8
msgid ":py:obj:`setTheme <qfluentwidgets.setTheme>`\\ \\(theme\\[\\, save\\]\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4312
#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 37ee756f98ed4418a3a90cc653e5e4ec
msgid "set the theme of application"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: abe856fbaec04b08b1748c5c87ad2d8a
msgid ":py:obj:`themeColor <qfluentwidgets.themeColor>`\\ \\(\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4375
#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 46430daa10404684be5280bfd72707d7
msgid "get theme color"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: d2705ead2e7a41a3ba3c6f0069151c40
msgid ""
":py:obj:`setThemeColor <qfluentwidgets.setThemeColor>`\\ \\(color\\[\\, "
"save\\]\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4380
#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 97173d8e9439489ca3aeb0f5ebd91748
msgid "set theme color"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 4d51b568ef274fb6913b2ad33439bbe8
msgid ":py:obj:`applyThemeColor <qfluentwidgets.applyThemeColor>`\\ \\(qss\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4393
#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 1e48c920ac714ba9bedf58480c2d75c5
msgid "apply theme color to style sheet"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: df8062d7d4784859933f7eeab1c92d8e
msgid ""
":py:obj:`exceptionHandler <qfluentwidgets.exceptionHandler>`\\ "
"\\(\\*default\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4662
#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 7631bd3356f941ac97ca939e0b568b59
msgid "decorator for exception handling"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 95f4c1cc5fa344d595490ba83fe6f936
msgid ":py:obj:`isDarkTheme <qfluentwidgets.isDarkTheme>`\\ \\(\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4998
#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: b6d5c1bfce484bf19d38b545f9261ece
msgid "whether the theme is dark mode"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 0092edb54d38490698f85b73ac7dad02
msgid ":py:obj:`theme <qfluentwidgets.theme>`\\ \\(\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:5003
#: ../../source/autoapi/qfluentwidgets/index.rst:207:<autosummary>:1
#: 1872901924194ab88354591800d71e1f
msgid "get theme mode"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:214:<autosummary>:1
#: 6a659e4fbebc43e58c6278357c46d12a
msgid ":py:obj:`__version__ <qfluentwidgets.__version__>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:214:<autosummary>:1
#: 5f73ac1dfaae4e1c95874d788bf7b831
msgid ":py:obj:`qrouter <qfluentwidgets.qrouter>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:214:<autosummary>:1
#: ccab10ebbf14472382475dbcc50ad299
msgid ":py:obj:`qconfig <qfluentwidgets.qconfig>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:222
#: ../../source/autoapi/qfluentwidgets/index.rst:284
#: ../../source/autoapi/qfluentwidgets/index.rst:295
#: 52783c8be3f84dc585369d51d315d28f d90401ed266a46ceb7e95f52d9d751d8
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.dialog_box.mask_dialog_base.MaskDialogBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:232
#: ../../source/autoapi/qfluentwidgets/index.rst:580
#: 60745e81986249f99c29460570d3205e
msgid "set color"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:237
#: 67bbc9ab7f684f26bceff68e2d0c0d52
msgid "update style sheet"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:242
#: 9d95a3ae6080492796b0330b66b21a67
msgid "fade in"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:248
#: be098f81b9af49ba9554678c6b3786cd
msgid "Bases: :py:obj:`qframelesswindow.FramelessDialog`, :py:obj:`Ui_MessageBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:266
#: 40aca277559c4d65a774ef88355e32ca
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.dialog_box.mask_dialog_base.MaskDialogBase`,"
" :py:obj:`Ui_MessageBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:310
#: ../../source/autoapi/qfluentwidgets/index.rst:355
#: cd42d656d9364008988f4309e789e273
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QLayout`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:337
#: ../../source/autoapi/qfluentwidgets/index.rst:405
#: e2220657f502436d854b2293f4fbb7f7
msgid "get the minimal height according to width"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:367
#: 5b6daa29bb054ff1b644c52ece4b4098
msgid "set the moving animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:370
#: ../../source/autoapi/qfluentwidgets/index.rst:860
#: ../../source/autoapi/qfluentwidgets/index.rst:1242
#: ../../source/autoapi/qfluentwidgets/index.rst:1257
#: ../../source/autoapi/qfluentwidgets/index.rst:1282
#: ../../source/autoapi/qfluentwidgets/index.rst:1317
#: ../../source/autoapi/qfluentwidgets/index.rst:1333
#: ../../source/autoapi/qfluentwidgets/index.rst:1364
#: ../../source/autoapi/qfluentwidgets/index.rst:1416
#: ../../source/autoapi/qfluentwidgets/index.rst:1485
#: ../../source/autoapi/qfluentwidgets/index.rst:1553
#: ../../source/autoapi/qfluentwidgets/index.rst:1605
#: ../../source/autoapi/qfluentwidgets/index.rst:1724
#: ../../source/autoapi/qfluentwidgets/index.rst:1740
#: ../../source/autoapi/qfluentwidgets/index.rst:1762
#: ../../source/autoapi/qfluentwidgets/index.rst:1991
#: ../../source/autoapi/qfluentwidgets/index.rst:2122
#: ../../source/autoapi/qfluentwidgets/index.rst:2253
#: ../../source/autoapi/qfluentwidgets/index.rst:2440
#: ../../source/autoapi/qfluentwidgets/index.rst:2502
#: ../../source/autoapi/qfluentwidgets/index.rst:2568
#: ../../source/autoapi/qfluentwidgets/index.rst:2593
#: ../../source/autoapi/qfluentwidgets/index.rst:2603
#: ../../source/autoapi/qfluentwidgets/index.rst:2633
#: ../../source/autoapi/qfluentwidgets/index.rst:2659
#: ../../source/autoapi/qfluentwidgets/index.rst:2670
#: ../../source/autoapi/qfluentwidgets/index.rst:2681
#: ../../source/autoapi/qfluentwidgets/index.rst:2702
#: ../../source/autoapi/qfluentwidgets/index.rst:2732
#: ../../source/autoapi/qfluentwidgets/index.rst:2763
#: ../../source/autoapi/qfluentwidgets/index.rst:2788
#: ../../source/autoapi/qfluentwidgets/index.rst:2822
#: ../../source/autoapi/qfluentwidgets/index.rst:2850
#: ../../source/autoapi/qfluentwidgets/index.rst:2860
#: ../../source/autoapi/qfluentwidgets/index.rst:2873
#: ../../source/autoapi/qfluentwidgets/index.rst:2913
#: ../../source/autoapi/qfluentwidgets/index.rst:2997
#: ../../source/autoapi/qfluentwidgets/index.rst:3028
#: ../../source/autoapi/qfluentwidgets/index.rst:3053
#: ../../source/autoapi/qfluentwidgets/index.rst:3087
#: ../../source/autoapi/qfluentwidgets/index.rst:3115
#: ../../source/autoapi/qfluentwidgets/index.rst:3125
#: ../../source/autoapi/qfluentwidgets/index.rst:3138
#: ../../source/autoapi/qfluentwidgets/index.rst:3148
#: ../../source/autoapi/qfluentwidgets/index.rst:3184
#: ../../source/autoapi/qfluentwidgets/index.rst:3200
#: ../../source/autoapi/qfluentwidgets/index.rst:3219
#: ../../source/autoapi/qfluentwidgets/index.rst:3325
#: ../../source/autoapi/qfluentwidgets/index.rst:3364
#: ../../source/autoapi/qfluentwidgets/index.rst:3434
#: ../../source/autoapi/qfluentwidgets/index.rst:3498
#: ../../source/autoapi/qfluentwidgets/index.rst:3542
#: ../../source/autoapi/qfluentwidgets/index.rst:3579
#: ../../source/autoapi/qfluentwidgets/index.rst:3611
#: ../../source/autoapi/qfluentwidgets/index.rst:3627
#: ../../source/autoapi/qfluentwidgets/index.rst:3640
#: ../../source/autoapi/qfluentwidgets/index.rst:3666
#: ../../source/autoapi/qfluentwidgets/index.rst:3714
#: ../../source/autoapi/qfluentwidgets/index.rst:4171
#: ../../source/autoapi/qfluentwidgets/index.rst:4185
#: ../../source/autoapi/qfluentwidgets/index.rst:4209
#: ../../source/autoapi/qfluentwidgets/index.rst:4222
#: ../../source/autoapi/qfluentwidgets/index.rst:4235
#: ../../source/autoapi/qfluentwidgets/index.rst:4261
#: ../../source/autoapi/qfluentwidgets/index.rst:4282
#: ../../source/autoapi/qfluentwidgets/index.rst:4302
#: ../../source/autoapi/qfluentwidgets/index.rst:4315
#: ../../source/autoapi/qfluentwidgets/index.rst:4383
#: ../../source/autoapi/qfluentwidgets/index.rst:4396
#: ../../source/autoapi/qfluentwidgets/index.rst:4641
#: ../../source/autoapi/qfluentwidgets/index.rst:4665
#: ../../source/autoapi/qfluentwidgets/index.rst:4957
#: ../../source/autoapi/qfluentwidgets/index.rst:4983
#: 737fcccd36ed475aaf7c55b4769d93a6 7b91cc857b9d4902b4b1c9fa8168a5d1
msgid "Parameters"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:372
#: ../../source/autoapi/qfluentwidgets/index.rst:1610
#: ../../source/autoapi/qfluentwidgets/index.rst:1751
#: ../../source/autoapi/qfluentwidgets/index.rst:1773
#: ../../source/autoapi/qfluentwidgets/index.rst:1992
#: ../../source/autoapi/qfluentwidgets/index.rst:2442
#: 2839689a1a0c483fa30d43456b42b4eb
msgid "duration: int"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:372
#: c15c2426d2ae4351b848699ba7921d97
msgid "the duration of animation in milliseconds"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:374
#: 29e69383457148d0940d93c5f2ea6066
msgid "ease: QEasingCurve"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:375
#: 53199843fb1142bb8d0f07621b28877a
msgid "the easing curve of animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:389
#: ../../source/autoapi/qfluentwidgets/index.rst:466
#: 3089f90df50e4e908b6e10d83ec7c29a
msgid "remove all widgets from layout"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:394
#: 8a3b0d7958554810a8d656c0454ca997
msgid "remove all widgets from layout and delete them"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:419
#: 2f4806fe06f64617bb0a76a35993cfbf
msgid "set vertical spacing between widgets"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:424
#: bae771b8602e4cc98e2e9b24231d7230
msgid "get vertical spacing between widgets"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:429
#: 11d64ac53a564011ae05ebf330efa420
msgid "set horizontal spacing between widgets"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:434
#: d6084fb939194924a6a419ad6378f626
msgid "get horizontal spacing between widgets"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:440
#: 2ab9c283cb30457caac2aed0787eff2e
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QVBoxLayout`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:446
#: cbe6c9e102374bcf8daaab5942e4934e
msgid "add widgets to layout"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:451
#: 6950527323d54ebe99064ebf7f280b42
msgid "add widget to layout"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:456
#: f103be8fa2404f02b83487d5d269ee27
msgid "remove widget from layout but not delete it"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:461
#: 63c1cdf160434ecd9771e31d820953c5
msgid "remove widget from layout and delete it"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:472
#: ../../source/autoapi/qfluentwidgets/index.rst:601
#: ../../source/autoapi/qfluentwidgets/index.rst:1398
#: ../../source/autoapi/qfluentwidgets/index.rst:1971
#: ../../source/autoapi/qfluentwidgets/index.rst:2716
#: e8f96e40b1bf4bdfac12aaef7590b935
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QFrame`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:478
#: f40c839e40804ba5bcdba9f92c61733b
msgid "set the title of card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:483
#: 6df9e8b15815400ebdb1497e15179e83
msgid "set the content of card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:488
#: ../../source/autoapi/qfluentwidgets/index.rst:504
#: ../../source/autoapi/qfluentwidgets/index.rst:523
#: ../../source/autoapi/qfluentwidgets/index.rst:550
#: ../../source/autoapi/qfluentwidgets/index.rst:595
#: ../../source/autoapi/qfluentwidgets/index.rst:628
#: ../../source/autoapi/qfluentwidgets/index.rst:4954
#: 6e7e72c12bc243b9904a714f7bc5d850
msgid "set the value of config item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:494
#: ../../source/autoapi/qfluentwidgets/index.rst:513
#: ../../source/autoapi/qfluentwidgets/index.rst:529
#: ../../source/autoapi/qfluentwidgets/index.rst:540
#: ../../source/autoapi/qfluentwidgets/index.rst:556
#: ../../source/autoapi/qfluentwidgets/index.rst:589
#: 727d115b14714e9294966c28026c803a f855bd5d91e447258a006c0360448087
msgid "Bases: :py:obj:`SettingCard`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:563
#: 6e092153847e4c568ccefb4589cc4def
msgid "Bases: :py:obj:`PushSettingCard`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:570
#: ../../source/autoapi/qfluentwidgets/index.rst:792
#: ../../source/autoapi/qfluentwidgets/index.rst:1081
#: 5daa03556d1340769448e4997f63a36a
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QToolButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:607
#: f3d0d323b6bb49cdb9ba905191ef8739
msgid "add widget to tail"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:612
#: dccd1d0000f8449d8cb4c128962d11c5
msgid "set the expand status of card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:617
#: 246ec8f7721b407ebc98733a94c0eb5a
msgid "toggle expand status"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:634
#: 6a820f4928884b76b8c6d74c9c1ef5e0
msgid "Bases: :py:obj:`ExpandSettingCard`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:640
#: 0337157768a14775b5258bee3eed82b6
msgid "add widget to group"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:646
#: ../../source/autoapi/qfluentwidgets/index.rst:657
#: 1df5c116a4eb46ac8f986fdcdd16704a 9f730c1a54ff49db809da3f35c8048fe
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.settings.expand_setting_card.ExpandSettingCard`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:667
#: 98259910aa794d59aa64976ea0cb720c
msgid "select button according to the value"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:673
#: a3d638640ec443f0b865146de7c19af1
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.settings.expand_setting_card.ExpandGroupSettingCard`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:684
#: ../../source/autoapi/qfluentwidgets/index.rst:837
#: ../../source/autoapi/qfluentwidgets/index.rst:1120
#: ../../source/autoapi/qfluentwidgets/index.rst:1200
#: ../../source/autoapi/qfluentwidgets/index.rst:1798
#: ../../source/autoapi/qfluentwidgets/index.rst:1834
#: ../../source/autoapi/qfluentwidgets/index.rst:2298
#: ../../source/autoapi/qfluentwidgets/index.rst:2467
#: ../../source/autoapi/qfluentwidgets/index.rst:2984
#: ../../source/autoapi/qfluentwidgets/index.rst:3175
#: ../../source/autoapi/qfluentwidgets/index.rst:3476
#: 3c19834bfba24f29b497068acb38f7fa
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:690
#: 104168e994c345d0ad6818e6345709b9
msgid "add setting card to group"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:695
#: ba7eeeff683447f39d502b4b9d54b674
msgid "add setting cards to group"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:704
#: 96072e40eb5f4abcb93581224268f0eb
msgid "Bases: :py:obj:`DropDownButtonBase`, :py:obj:`PushButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:717
#: 4861cce663ef43a0afc39bb4c8c00285
msgid "Bases: :py:obj:`DropDownButtonBase`, :py:obj:`ToolButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:730
#: ../../source/autoapi/qfluentwidgets/index.rst:830
#: 94f3e51157de4d14b78f9e1cda9cd270
msgid "Bases: :py:obj:`PushButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:737
#: ../../source/autoapi/qfluentwidgets/index.rst:775
#: ../../source/autoapi/qfluentwidgets/index.rst:3232
#: ../../source/autoapi/qfluentwidgets/index.rst:3249
#: ../../source/autoapi/qfluentwidgets/index.rst:3355
#: 0744cf1f64eb458a87692eab8eaf3d3c
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QPushButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:768
#: caf09999b16443baa46f0867d3088b33
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QRadioButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:823
#: ../../source/autoapi/qfluentwidgets/index.rst:932
#: 0a4b277249284e67872ceae9cebb52b4
msgid "Bases: :py:obj:`ToolButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:847
#: d38e573dd40d4377975e0ea291c94e03
msgid "set the widget on left side"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:852
#: 97e7c757ef164845b447355d20cec3dc
msgid "set drop dow button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:857
#: 2bbf121bf4f1460086f86bc238763a98
msgid "set the widget pops up when drop down button is clicked"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:862
#: cc5e573238874e2fa56d8251666f5faa
msgid "flyout: QWidget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:862
#: 77729f39cb964d46a9c1a12d6390cc18
msgid ""
"the widget pops up when drop down button is clicked. It should contain "
"the `exec` method, whose first parameter type is `QPoint`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:868
#: bcfb16e9799e4f5d8e8dfd75093b4088
msgid "show flyout"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:874
#: ../../source/autoapi/qfluentwidgets/index.rst:908
#: ac25a649b89c4b628fd82880d9cd01b6 ffa8d4fa5f73464295713b86b12ba136
msgid "Bases: :py:obj:`SplitWidgetBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:939
#: 6f5fa3f4c79646f68dcefc6638a408c0
msgid "Bases: :py:obj:`SplitPushButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:946
#: 86701865e41d4f399c58419a99485b6e
msgid "Bases: :py:obj:`SplitToolButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:953
#: 82050a10db304e4bb53b521a7839f464
msgid "Bases: :py:obj:`PrimaryDropDownButtonBase`, :py:obj:`PrimaryPushButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:966
#: f9b4ae84a7e5489990877ee45c2dd5eb
msgid "Bases: :py:obj:`PrimaryDropDownButtonBase`, :py:obj:`PrimaryToolButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:979
#: 9f48cfaa8c9745f4b68b7c8e168db3f6
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QCheckBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:989
#: 6bc91de226ad4356aaf0d2afe1101941
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QPushButton`, :py:obj:`ComboBoxBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1013
#: 2fc295a1b18b41c3b873758a651fe6d0
msgid ""
"Bases: :py:obj:`qfluentwidgets.components.widgets.line_edit.LineEdit`, "
":py:obj:`ComboBoxBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1030
#: d5eba9e93f724bc5b2b0de7e2e1567ca
msgid "Clears the combobox, removing all items."
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1036
#: 4769c0f8cf954b7a95a30cfd31dc4088
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QLineEdit`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1061
#: ce62844eb5db4445a4118dcfa7f8da4d
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTextEdit`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1071
#: 227ca23995b24c7182e661aa703abeae
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QPlainTextEdit`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1097
#: e71f63dc0a5141c19b0a6644569a239b
msgid "Bases: :py:obj:`LineEdit`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1111
#: 680cc46be1f34fbca7a06adab7f18362
msgid "emit search signal"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1140
#: bc031aa58d61452cbf39b4c3efc738fc
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QLabel`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1156
#: 5a1a76097eab4033a0eccdcabdafaa1d
msgid "Bases: :py:obj:`ListBase`, :py:obj:`PyQt5.QtWidgets.QListWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1169
#: 46b737db46ee45888cf0f9f4d34ffeaf
msgid "Bases: :py:obj:`ListBase`, :py:obj:`PyQt5.QtWidgets.QListView`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1176
#: b03bda416f774c48a5f79c2ead616203
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.widgets.table_view.TableItemDelegate`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1183
#: 0e314a82224a4646badd0166a547c521
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QMenu`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1193
#: 31905ee88c8f44d9b0f05b949450b6e0
msgid "Bases: :py:obj:`EditMenu`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1210
#: 5d1f23a5b2204bf2a2cf51f40a67d2c4
msgid "set the height of menu item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1215
#: ../../source/autoapi/qfluentwidgets/index.rst:3490
#: af108b85e22145e2b073b406fb4a5391
msgid "add shadow to dialog"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1229
#: b94cc97e3ca8447aa744e1a6fc0949e4
msgid "clear all actions"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1234
#: 2a8785a8eec94012947ce8d92714bda9
msgid "set the icon of menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1239
#: b07ed3ed55a0414588ce5425d87bffb8
msgid "add action to menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1243
#: 914d8ccdcbf54ceebc5cac07d6e3a6ce
msgid "action: QAction"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1244
#: b0367f37bd0043e0905882d72b67fe1a
msgid "menu action"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1249
#: e1404afc9d404b8cb9ca90115ed3274c
msgid "inserts action to menu, before the action before"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1254
#: f391fe8f1f52498798475bbd8c053043
msgid "add actions to menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1258
#: 4501a71853a64827b33f1524796dc625
msgid "actions: Iterable[QAction]"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1259
#: 5e85efd8e6ad424497a860bd5d8025db
msgid "menu actions"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1264
#: 163364176c28468badccf2d47bc47041
msgid "inserts the actions actions to menu, before the action before"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1269
#: ab35e2eece20434fb4c9eaede867c13b
msgid "remove action from menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1274
#: f91ce568130243d3a3f38660b2932255
msgid "set the default action"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1279
#: 23c6d197d83e4efd933121a76eed5727
msgid "add sub menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1283
#: 55cb0541803f489280d203ba53eafaa1
msgid "menu: RoundMenu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1284
#: ea952af3ff954164b1d8a41a760b7863
msgid "sub round menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1289
#: 04cb756a2aa24a35a3463f4ed25a5b8e
msgid "insert menu before action `before`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1294
#: d2ff9629aa094932bfe48e617f5a6e12
msgid "add seperator to menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1314
#: ../../source/autoapi/qfluentwidgets/index.rst:1330
#: 0c3df2f36d724dc7b15928d89e94792f
msgid "show menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1319
#: ../../source/autoapi/qfluentwidgets/index.rst:1335
#: ../../source/autoapi/qfluentwidgets/index.rst:3544
#: 5d445f5c136f4d99985bbcf18ecda40d
msgid "pos: QPoint"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1319
#: ../../source/autoapi/qfluentwidgets/index.rst:1335
#: ../../source/autoapi/qfluentwidgets/index.rst:3544
#: 781de325172f4d54a034be708a5e0c40
msgid "pop-up position"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1322
#: ../../source/autoapi/qfluentwidgets/index.rst:1338
#: ../../source/autoapi/qfluentwidgets/index.rst:3546
#: 62f8d5e38f0846b0bbad26800238195f
msgid "ani: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1322
#: ../../source/autoapi/qfluentwidgets/index.rst:1338
#: ../../source/autoapi/qfluentwidgets/index.rst:3547
#: ********************************
msgid "Whether to show pop-up animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1324
#: ../../source/autoapi/qfluentwidgets/index.rst:1340
#: b687134f928442119e7caddb14e74d91
msgid "aniType: MenuAnimationType"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1325
#: ../../source/autoapi/qfluentwidgets/index.rst:1341
#: af7cb17b66ce4eae9c1393a28b2052f6
msgid "menu animation type"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1347
#: ../../source/autoapi/qfluentwidgets/index.rst:2010
#: ../../source/autoapi/qfluentwidgets/index.rst:2451
#: ../../source/autoapi/qfluentwidgets/index.rst:3553
#: ../../source/autoapi/qfluentwidgets/index.rst:4623
#: ../../source/autoapi/qfluentwidgets/index.rst:4850
#: ../../source/autoapi/qfluentwidgets/index.rst:4918
#: 49fd60505fb64202aaab93458f14337e
msgid "Bases: :py:obj:`PyQt5.QtCore.QObject`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1361
#: 967637508ed14235a1f97ebd1d8727d0
msgid "register menu animation manager"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1365
#: ce05ff34e6864b159d97ee5a79bd6933
msgid "name: Any"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1366
#: 9c12ebd86f1e4ab9878879a586dfe24a
msgid "the name of manager, it should be unique"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1376
#: ../../source/autoapi/qfluentwidgets/index.rst:1496
#: ../../source/autoapi/qfluentwidgets/index.rst:1564
#: ../../source/autoapi/qfluentwidgets/index.rst:1905
#: ../../source/autoapi/qfluentwidgets/index.rst:2035
#: ../../source/autoapi/qfluentwidgets/index.rst:2935
#: ../../source/autoapi/qfluentwidgets/index.rst:2957
#: ../../source/autoapi/qfluentwidgets/index.rst:4325
#: ../../source/autoapi/qfluentwidgets/index.rst:4575
#: ../../source/autoapi/qfluentwidgets/index.rst:4672
#: 47900d95464b4705be70607f023dfa87 82c694864b1146e1816814e3c5c08e6d
msgid "Bases: :py:obj:`enum.Enum`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1408
#: 3e4a95ec9cb5403bb6b84f41ab9826a8
msgid "add widget to info bar"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1413
#: ../../source/autoapi/qfluentwidgets/index.rst:2250
#: 076fd376c2364fcca40042ed914ec7f8
msgid "set the custom background color"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1417
#: ../../source/autoapi/qfluentwidgets/index.rst:2254
#: df8fcb3895a545fd8d15ba6bf1a42d96
msgid "light, dark: str | Qt.GlobalColor | QColor"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1418
#: ../../source/autoapi/qfluentwidgets/index.rst:2255
#: b450e37dd04e45879e00c658bcea9981
msgid "background color in light/dark theme mode"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1456
#: e19acfc059374deeb9132a5f6fd8b3ca
msgid ""
"Bases: :py:obj:`qfluentwidgets.common.icon.FluentIconBase`, "
":py:obj:`enum.Enum`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1482
#: ../../source/autoapi/qfluentwidgets/index.rst:4168
#: ../../source/autoapi/qfluentwidgets/index.rst:4206
#: 4b2fa33e97954273af571e5b4fd8d2e8
msgid "get the path of icon"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1489
#: ../../source/autoapi/qfluentwidgets/index.rst:4175
#: ../../source/autoapi/qfluentwidgets/index.rst:4213
#: ../../source/autoapi/qfluentwidgets/index.rst:4226
#: ../../source/autoapi/qfluentwidgets/index.rst:4246
#: ../../source/autoapi/qfluentwidgets/index.rst:4290
#: ../../source/autoapi/qfluentwidgets/index.rst:4306
#: ../../source/autoapi/qfluentwidgets/index.rst:4317
#: 608e016b1de143178f8af5e94ae094d6
msgid "theme: Theme"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1487
#: ../../source/autoapi/qfluentwidgets/index.rst:4173
#: ../../source/autoapi/qfluentwidgets/index.rst:4211
#: ../../source/autoapi/qfluentwidgets/index.rst:4224
#: ../../source/autoapi/qfluentwidgets/index.rst:4243
#: 18241cb228064905bdaf05da742f0f60
msgid ""
"the theme of icon * `Theme.Light`: black icon * `Theme.DARK`: white icon "
"* `Theme.AUTO`: icon color depends on `config.theme`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1538
#: ../../source/autoapi/qfluentwidgets/index.rst:1596
#: ../../source/autoapi/qfluentwidgets/index.rst:1619
#: 851d68219920444e94fac9c91bb50015
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QScrollArea`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1550
#: ../../source/autoapi/qfluentwidgets/index.rst:4566
#: 96585b0437184394af2ee364e5894f5a
msgid "set smooth mode"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1554
#: 0af22d4faf654824814eb222dc488aae
msgid "mode: SmoothMode"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1555
#: 13b75839689b40179910699cd2c3e036
msgid "smooth scroll mode"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1602
#: ../../source/autoapi/qfluentwidgets/index.rst:2437
#: 7d5f10933e014bd285cb06f5937255d1
msgid "set scroll animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1607
#: 09c874999ccd4235a6a13444da0acd40
msgid "orient: Orient"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1607
#: e335a7f16e2d4b1c8b63beb823daac50
msgid "scroll orientation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1610
#: ../../source/autoapi/qfluentwidgets/index.rst:2442
#: 740656be6d714bddaf368c72a2175634
msgid "scroll duration"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1612
#: ../../source/autoapi/qfluentwidgets/index.rst:2444
#: 8f94a5958d094c7e8e15ac565ff97579
msgid "easing: QEasingCurve"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1613
#: ../../source/autoapi/qfluentwidgets/index.rst:2445
#: 9132295b2191401c9d044db83031d39c
msgid "animation type"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1626
#: a6d3fbb6481244b085a98a868a1fe750
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QSlider`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1640
#: 5a1d84b7bb1c45de81ef90c96f14262e
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QProxyStyle`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1646
#: aed9905e68a0407d970bb2e171791caa
msgid "get the rectangular area occupied by the sub control"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1651
#: 018587f585084a6b9f64411735585cd3
msgid "draw sub control"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1657
#: 7045b2f5dabb4898bae4ca70e7cebfe9
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QSpinBox`, :py:obj:`Ui_SpinBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1667
#: 6a11db1842fe4c89bbf6a75a806b5c0b
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QDoubleSpinBox`, :py:obj:`Ui_SpinBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1677
#: 559e41b6df5d43b6a33022c8183ef232
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QDateEdit`, :py:obj:`Ui_SpinBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1687
#: 0e3cb867e1ca4aafaa7d010f9c038e5a
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QDateTimeEdit`, :py:obj:`Ui_SpinBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1697
#: 4a4c75b024e843bda41eb70323682f17
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTimeEdit`, :py:obj:`Ui_SpinBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1707
#: ../../source/autoapi/qfluentwidgets/index.rst:1782
#: 84af33db88ba4894a91fca21b79a5e43
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QStackedWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1721
#: de4aa3a761a54b37b83b4d17c03e605e
msgid "add widget to window"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1726
#: ../../source/autoapi/qfluentwidgets/index.rst:1764
#: 729fb082bfce46699cb0b24c21d47881
msgid "widget:"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1726
#: 69938738e1434190888c265f95214bad
msgid "widget to be added"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1729
#: f6e0086ff6c242f8aea536191d5baacd
msgid "deltaX: int"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1729
#: 635f9275472c487096e473a4f25ef1a5
msgid "the x-axis offset from the beginning to the end of animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1731
#: beb25899a84044659e14ba23ff70acae
msgid "deltaY: int"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1732
#: f46699a5c74945df8fedfe4a4b832206
msgid "the y-axis offset from the beginning to the end of animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1737
#: 61306c23c2074665a636be22e01839a0
msgid "set current window to display"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1742
#: ../../source/autoapi/qfluentwidgets/index.rst:2790
#: ../../source/autoapi/qfluentwidgets/index.rst:2824
#: ../../source/autoapi/qfluentwidgets/index.rst:2862
#: ../../source/autoapi/qfluentwidgets/index.rst:3055
#: ../../source/autoapi/qfluentwidgets/index.rst:3089
#: ../../source/autoapi/qfluentwidgets/index.rst:3127
#: ../../source/autoapi/qfluentwidgets/index.rst:3202
#: ../../source/autoapi/qfluentwidgets/index.rst:3436
#: 785347333abd41349c9a937057d09c8e
msgid "index: int"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1742
#: 1273592f462f4de991374b57eba316a4
msgid "the index of widget to display"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1745
#: ../../source/autoapi/qfluentwidgets/index.rst:1767
#: f0633e7c776d4874891fb1a26bad58f2
msgid "isNeedPopOut: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1745
#: ../../source/autoapi/qfluentwidgets/index.rst:1767
#: 77d123630fd54265a2782224847c2479
msgid "need pop up animation or not"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1748
#: ../../source/autoapi/qfluentwidgets/index.rst:1770
#: 049b16438b4b45188a71e8048d004302
msgid "showNextWidgetDirectly: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1748
#: ../../source/autoapi/qfluentwidgets/index.rst:1770
#: a005c4a1f8d343a6a0f7e8bc58bc8499
msgid "whether to show next widget directly when animation started"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1751
#: ../../source/autoapi/qfluentwidgets/index.rst:1773
#: d3bd1853e2c843abac8286dc75e6b59f
msgid "animation duration"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1753
#: ../../source/autoapi/qfluentwidgets/index.rst:1775
#: ac377c4cd810482ba6db51040eceb4a7
msgid "easingCurve: QEasingCurve"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1754
#: ../../source/autoapi/qfluentwidgets/index.rst:1776
#: bb74707e1af8494784247ffb5b7940cf
msgid "the interpolation mode of animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1759
#: 15689238fdc04a819b9a649566f0effe
msgid "set currect widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1764
#: 4efaaf2bf2ad482b91ee841358928362
msgid "the widget to be displayed"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1808
#: ********************************
msgid "set the title of tooltip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1813
#: 24910f531284413c89ceb9a0534659f9
msgid "set the content of tooltip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1818
#: 64970760e21e405a9dba78732b4a8f4e
msgid "set the state of tooltip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1823
#: caa10818334842bc97286a3c02d2758e
msgid "get suitable position in main window"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1828
#: b870b57008d74073a1c5dc822e1a322a
msgid "paint state tooltip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1870
#: d3b9eff0c03a4bf6b96fdcba3633f142
msgid "set checked state"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1875
#: b3af8a82597c4b189bb77a4fa61d453a
msgid "toggle checked state"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1922
#: cc3e605551f441a4bc74224be37e182a
msgid "Bases: :py:obj:`TableBase`, :py:obj:`PyQt5.QtWidgets.QTableView`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1929
#: bab37012ec0a4699a2efd3cc1ef71adb
msgid "Bases: :py:obj:`TableBase`, :py:obj:`PyQt5.QtWidgets.QTableWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1942
#: ../../source/autoapi/qfluentwidgets/index.rst:2096
#: 3b621bd68e924660959da5323f213499
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QStyledItemDelegate`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1980
#: a65ac6b76f3a43f7a8abf7ec5624ecea
msgid "set text on tooltip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1988
#: 12869cb297534bb18c8fe2663c6bd9c6
msgid "set tooltip duration in milliseconds"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:1993
#: ab5f82e12b3c44cc88e88aeefc41e107
msgid ""
"display duration in milliseconds, if `duration <= 0`, tooltip won't "
"disappear automatically"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2004
#: 950f78429eb44f99b1c8fb0d7b0f8240
msgid "adjust the position of tooltip relative to widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2019
#: b7a2f2fee0e44ae3a373099008f54e4e
msgid "hide tool tip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2024
#: 733ce7cba8a94e63abe88e6c78132ff0
msgid "show tool tip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2029
#: a2b101a8e3cf4f15b8408a5b4ac1d309
msgid "set the delay of tool tip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2082
#: 42abf2d896a648f697ebde906fbc01af
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTreeWidget`, :py:obj:`TreeViewBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2089
#: 30c887b7e0c1477d9a94251a9bd7649f
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTreeView`, :py:obj:`TreeViewBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2109
#: 8aab6c2f3c0345b58a87878441c129fc
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QListWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2119
#: 7eb946ea45564b0c83c94f403d1441c5
msgid "set items in the list"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2124
#: ../../source/autoapi/qfluentwidgets/index.rst:3500
#: 4bd64c1a94f7411686bdc2976ed021d6
msgid "items: Iterable[Any]"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2124
#: ../../source/autoapi/qfluentwidgets/index.rst:3500
#: 59565922dc98416e9ead53f3e69be7f7
msgid "the items to be added"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2127
#: ed235fdf09324095b968a1a43f3d05cf
msgid "itemSize: QSize"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2127
#: c1a79a49fd72435caaf597c51dae980d
msgid "the size of item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2129
#: ../../source/autoapi/qfluentwidgets/index.rst:3375
#: ../../source/autoapi/qfluentwidgets/index.rst:3447
#: ../../source/autoapi/qfluentwidgets/index.rst:3505
#: af980b01213e4f9fa4cff55d37719427
msgid "align: Qt.AlignmentFlag"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2130
#: ../../source/autoapi/qfluentwidgets/index.rst:3506
#: 35e53a3c56ef4adc83f91b3cb5ff5e5d
msgid "the text alignment of item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2135
#: 350ea93e7f0549729b461735e01d647e
msgid "set the selected item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2140
#: 992b72e5c9284d5e8de594c8b2d4f4a5
msgid "scroll to item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2148
#: 5f38431937c142d8a16929d7ef400f89
msgid "scroll down an item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2153
#: c49dfe914d734f8184c5cd6684a99646
msgid "scroll up an item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2180
#: ../../source/autoapi/qfluentwidgets/index.rst:2226
#: 15889804032849d8955a35b729868330 64eb0cc0643842448b61a1547104737d
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QProgressBar`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2288
#: 6e7ac478029048378a7f37b4e52a0375
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.widgets.progress_bar.ProgressBar`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2369
#: 9e4dcb719c7344b6b302876daebc77b0
msgid "expand scroll bar"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2374
#: e2e83936682b44bf959ca78cc730449c
msgid "collapse scroll bar"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2400
#: 2ec866cc44484d16b91f3b8835a1e7b4
msgid "whether to force the scrollbar to be hidden"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2409
#: 4bd299fb098645729946115448f7dc5e
msgid "Bases: :py:obj:`ScrollBar`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2418
#: c33952d7e1c14e9fbdb3d651f25a9548
msgid "scroll the specified distance"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2423
#: 8b1ccdc922c24452b478a7302982b4ed
msgid "scroll to the specified position"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2494
#: ../../source/autoapi/qfluentwidgets/index.rst:2538
#: ../../source/autoapi/qfluentwidgets/index.rst:2553
#: ../../source/autoapi/qfluentwidgets/index.rst:2643
#: 29dcd6175d4f4f85b4f8a54e1fa51385
msgid "set whether the widget is compacted"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2499
#: ../../source/autoapi/qfluentwidgets/index.rst:2630
#: cdf45513645a44cb9f3204276f2e6d59
msgid "set whether the button is selected"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2503
#: ../../source/autoapi/qfluentwidgets/index.rst:2634
#: c27d5127f3694d38938bd6ec9b6d6dc7
msgid "isSelected: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2504
#: ../../source/autoapi/qfluentwidgets/index.rst:2635
#: 0a2570fed7184d728515574a6e365dee
msgid "whether the button is selected"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2510
#: ../../source/autoapi/qfluentwidgets/index.rst:2532
#: ../../source/autoapi/qfluentwidgets/index.rst:2649
#: ******************************** 29d20f1a17984a2091ec39ca8a6a9477
msgid "Bases: :py:obj:`NavigationWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2547
#: 728fe9c1eeb641b3b109f12cedf7a046
msgid "Bases: :py:obj:`NavigationPushButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2559
#: f64cb1a0508146d1b3f40e89468cd26b
msgid "Bases: :py:obj:`NavigationTreeWidgetBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2565
#: ../../source/autoapi/qfluentwidgets/index.rst:2656
#: 5e07d5b7a7224c15b0ef0f64d0c5aa8a
msgid "add child"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2569
#: ../../source/autoapi/qfluentwidgets/index.rst:2594
#: ../../source/autoapi/qfluentwidgets/index.rst:2604
#: ../../source/autoapi/qfluentwidgets/index.rst:2660
#: ../../source/autoapi/qfluentwidgets/index.rst:2671
#: ../../source/autoapi/qfluentwidgets/index.rst:2682
#: 3888400d285d4f789c7905eac4cad36d
msgid "child: NavigationTreeWidgetBase"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2570
#: ../../source/autoapi/qfluentwidgets/index.rst:2595
#: ../../source/autoapi/qfluentwidgets/index.rst:2605
#: ../../source/autoapi/qfluentwidgets/index.rst:2661
#: ../../source/autoapi/qfluentwidgets/index.rst:2672
#: ../../source/autoapi/qfluentwidgets/index.rst:2683
#: 70d1788f2e3d45ef9d54f0873f02dc61
msgid "child item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2590
#: ../../source/autoapi/qfluentwidgets/index.rst:2667
#: 27782010f9974c108173fa1cbcd63e1f
msgid "insert child"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2600
#: ../../source/autoapi/qfluentwidgets/index.rst:2678
#: fdd705cda8184e858960edde9fd5b243
msgid "remove child"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2610
#: ../../source/autoapi/qfluentwidgets/index.rst:2710
#: b25e98bbfc0849979119fd66f7ce7c02
msgid "return child items"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2615
#: ../../source/autoapi/qfluentwidgets/index.rst:2699
#: 13e3765c5f2a45f28853c2b9d0c449fd
msgid "set the expanded status"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2620
#: ../../source/autoapi/qfluentwidgets/index.rst:2688
#: 18a8f3421248457193abad16e89f0445
msgid "is root node"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2625
#: ../../source/autoapi/qfluentwidgets/index.rst:2693
#: 6b538ba860654caea842089d90b02927
msgid "is leaf node"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2703
#: 5cdd5dfc5ef94083be1e340970904f88
msgid "isExpanded: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2704
#: 5e1e5e5c1b704fb1b7d3b617af9b3043
msgid "whether to expand node"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2729
#: ../../source/autoapi/qfluentwidgets/index.rst:2994
#: bdd4821a100640ffb66a5f18ca13df4d
msgid "add navigation item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2734
#: ../../source/autoapi/qfluentwidgets/index.rst:2765
#: ../../source/autoapi/qfluentwidgets/index.rst:2793
#: ../../source/autoapi/qfluentwidgets/index.rst:2827
#: ../../source/autoapi/qfluentwidgets/index.rst:2874
#: ../../source/autoapi/qfluentwidgets/index.rst:2914
#: ../../source/autoapi/qfluentwidgets/index.rst:3186
#: ../../source/autoapi/qfluentwidgets/index.rst:3205
#: ../../source/autoapi/qfluentwidgets/index.rst:3220
#: ../../source/autoapi/qfluentwidgets/index.rst:4645
#: cd211c448a3c4ffe814fedbd4135ed44
msgid "routeKey: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2734
#: ../../source/autoapi/qfluentwidgets/index.rst:2765
#: ../../source/autoapi/qfluentwidgets/index.rst:2793
#: ../../source/autoapi/qfluentwidgets/index.rst:2827
#: ../../source/autoapi/qfluentwidgets/index.rst:2875
#: ../../source/autoapi/qfluentwidgets/index.rst:2915
#: ../../source/autoapi/qfluentwidgets/index.rst:2999
#: ../../source/autoapi/qfluentwidgets/index.rst:3030
#: ../../source/autoapi/qfluentwidgets/index.rst:3058
#: ../../source/autoapi/qfluentwidgets/index.rst:3092
#: ../../source/autoapi/qfluentwidgets/index.rst:3140
#: ../../source/autoapi/qfluentwidgets/index.rst:3150
#: ../../source/autoapi/qfluentwidgets/index.rst:3186
#: ../../source/autoapi/qfluentwidgets/index.rst:3205
#: ../../source/autoapi/qfluentwidgets/index.rst:3221
#: a63fb591236545188a672efcdcbf6cde
msgid "the unique name of item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2737
#: ../../source/autoapi/qfluentwidgets/index.rst:2796
#: ../../source/autoapi/qfluentwidgets/index.rst:3002
#: ../../source/autoapi/qfluentwidgets/index.rst:3061
#: 4ffae0364dd84800b2713e228049c964
msgid "icon: str | QIcon | FluentIconBase"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2737
#: ../../source/autoapi/qfluentwidgets/index.rst:2796
#: ../../source/autoapi/qfluentwidgets/index.rst:3002
#: ../../source/autoapi/qfluentwidgets/index.rst:3061
#: 8cf07d50c5134ea3b764411258037f9e
msgid "the icon of navigation item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2740
#: ../../source/autoapi/qfluentwidgets/index.rst:2799
#: ../../source/autoapi/qfluentwidgets/index.rst:3005
#: ../../source/autoapi/qfluentwidgets/index.rst:3064
#: ../../source/autoapi/qfluentwidgets/index.rst:3189
#: ../../source/autoapi/qfluentwidgets/index.rst:3208
#: ../../source/autoapi/qfluentwidgets/index.rst:3668
#: 076b1af15084449189558c9f281d90d3
msgid "text: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2740
#: ../../source/autoapi/qfluentwidgets/index.rst:2799
#: ../../source/autoapi/qfluentwidgets/index.rst:3005
#: ../../source/autoapi/qfluentwidgets/index.rst:3064
#: ../../source/autoapi/qfluentwidgets/index.rst:3189
#: ../../source/autoapi/qfluentwidgets/index.rst:3208
#: a7913a97d0dd43229ca637166b7042fc
msgid "the text of navigation item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2743
#: ../../source/autoapi/qfluentwidgets/index.rst:2771
#: ../../source/autoapi/qfluentwidgets/index.rst:2802
#: ../../source/autoapi/qfluentwidgets/index.rst:2833
#: ../../source/autoapi/qfluentwidgets/index.rst:3008
#: ../../source/autoapi/qfluentwidgets/index.rst:3036
#: ../../source/autoapi/qfluentwidgets/index.rst:3067
#: ../../source/autoapi/qfluentwidgets/index.rst:3098
#: ../../source/autoapi/qfluentwidgets/index.rst:3191
#: ../../source/autoapi/qfluentwidgets/index.rst:3210
#: 901a4c5321b1461f9d4cf74ec185e3f8
msgid "onClick: callable"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2743
#: ../../source/autoapi/qfluentwidgets/index.rst:2771
#: ../../source/autoapi/qfluentwidgets/index.rst:2802
#: ../../source/autoapi/qfluentwidgets/index.rst:2833
#: ../../source/autoapi/qfluentwidgets/index.rst:3008
#: ../../source/autoapi/qfluentwidgets/index.rst:3036
#: ../../source/autoapi/qfluentwidgets/index.rst:3067
#: ../../source/autoapi/qfluentwidgets/index.rst:3098
#: ../../source/autoapi/qfluentwidgets/index.rst:3192
#: ../../source/autoapi/qfluentwidgets/index.rst:3211
#: 2fb9229c583c44b3a46305d4d3ac5d26
msgid "the slot connected to item clicked signal"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2746
#: ../../source/autoapi/qfluentwidgets/index.rst:2774
#: ../../source/autoapi/qfluentwidgets/index.rst:2805
#: ../../source/autoapi/qfluentwidgets/index.rst:2836
#: ../../source/autoapi/qfluentwidgets/index.rst:3014
#: ../../source/autoapi/qfluentwidgets/index.rst:3039
#: ../../source/autoapi/qfluentwidgets/index.rst:3073
#: ../../source/autoapi/qfluentwidgets/index.rst:3101
#: 1e5d20918cce461d8f73ad96803ad177
msgid "position: NavigationItemPosition"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2746
#: ../../source/autoapi/qfluentwidgets/index.rst:2774
#: ../../source/autoapi/qfluentwidgets/index.rst:2805
#: ../../source/autoapi/qfluentwidgets/index.rst:2836
#: ../../source/autoapi/qfluentwidgets/index.rst:3014
#: ce0263ee0e474e0aa5f6f945ed0b170e
msgid "where the button is added"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2749
#: ../../source/autoapi/qfluentwidgets/index.rst:2808
#: ../../source/autoapi/qfluentwidgets/index.rst:3011
#: ../../source/autoapi/qfluentwidgets/index.rst:3070
#: ea6ba983299242ba9146af56c3f4ef1a
msgid "selectable: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2749
#: ../../source/autoapi/qfluentwidgets/index.rst:2808
#: ../../source/autoapi/qfluentwidgets/index.rst:3011
#: ../../source/autoapi/qfluentwidgets/index.rst:3070
#: 8302c3e170ff41f1bc7fb9ffb3aeeace
msgid "whether the item is selectable"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2752
#: ../../source/autoapi/qfluentwidgets/index.rst:2777
#: ../../source/autoapi/qfluentwidgets/index.rst:2811
#: ../../source/autoapi/qfluentwidgets/index.rst:2839
#: ../../source/autoapi/qfluentwidgets/index.rst:3017
#: ../../source/autoapi/qfluentwidgets/index.rst:3042
#: ../../source/autoapi/qfluentwidgets/index.rst:3076
#: ../../source/autoapi/qfluentwidgets/index.rst:3104
#: 981ab78f7c6242a1aa950c3858a88ced
msgid "tooltip: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2752
#: ../../source/autoapi/qfluentwidgets/index.rst:2811
#: ../../source/autoapi/qfluentwidgets/index.rst:3017
#: ../../source/autoapi/qfluentwidgets/index.rst:3076
#: 58ec4abe5b674538b2968df61f0533ca
msgid "the tooltip of item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2754
#: ../../source/autoapi/qfluentwidgets/index.rst:2779
#: ../../source/autoapi/qfluentwidgets/index.rst:2813
#: ../../source/autoapi/qfluentwidgets/index.rst:2841
#: ../../source/autoapi/qfluentwidgets/index.rst:3019
#: ../../source/autoapi/qfluentwidgets/index.rst:3044
#: ../../source/autoapi/qfluentwidgets/index.rst:3078
#: ../../source/autoapi/qfluentwidgets/index.rst:3106
#: 4a9f7a15df7b4f83934f32f8ec99cb44
msgid "parentRouteKey: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2755
#: c45866880ad64f52a5a6352617c71d1a
msgid ""
"the route key of parent item, the parent widget should be "
"`NavigationTreeWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2760
#: ../../source/autoapi/qfluentwidgets/index.rst:3025
#: 0f3e1cc2383743b5b39456807ad7e872
msgid "add custom widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2768
#: ../../source/autoapi/qfluentwidgets/index.rst:2830
#: ../../source/autoapi/qfluentwidgets/index.rst:3033
#: ../../source/autoapi/qfluentwidgets/index.rst:3095
#: a1e82430406640fdaeec7ff0fb181524
msgid "widget: NavigationWidget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2768
#: ../../source/autoapi/qfluentwidgets/index.rst:2830
#: ../../source/autoapi/qfluentwidgets/index.rst:3033
#: ../../source/autoapi/qfluentwidgets/index.rst:3095
#: c1bd656815fa4f6bbeb4df0dc6ec2fbe
msgid "the custom widget to be added"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2777
#: ../../source/autoapi/qfluentwidgets/index.rst:2839
#: ../../source/autoapi/qfluentwidgets/index.rst:3042
#: ../../source/autoapi/qfluentwidgets/index.rst:3104
#: e017e50c8fb64b90a803eba5a8d92ab2
msgid "the tooltip of widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2780
#: ../../source/autoapi/qfluentwidgets/index.rst:2814
#: ../../source/autoapi/qfluentwidgets/index.rst:2842
#: 694c8b9889c740acafa1851d3c30c3aa
msgid ""
"the route key of parent item, the parent item should be "
"`NavigationTreeWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2785
#: a0faca73a5b74afa8d31ab88b21751ab
msgid "insert navigation tree item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2790
#: 98c9a0651e9c40be8553f68cc14b92da
msgid "the insert position of parent widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2819
#: ../../source/autoapi/qfluentwidgets/index.rst:3084
#: 53fe760976d34a9d9aaf72d6bfe0d5f6
msgid "insert custom widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2824
#: ../../source/autoapi/qfluentwidgets/index.rst:2862
#: ../../source/autoapi/qfluentwidgets/index.rst:3055
#: ../../source/autoapi/qfluentwidgets/index.rst:3089
#: ../../source/autoapi/qfluentwidgets/index.rst:3127
#: ../../source/autoapi/qfluentwidgets/index.rst:3202
#: 00ebd780155841b78d521adf1e2cddf1
msgid "insert position"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2847
#: ../../source/autoapi/qfluentwidgets/index.rst:2857
#: ../../source/autoapi/qfluentwidgets/index.rst:3112
#: ../../source/autoapi/qfluentwidgets/index.rst:3122
#: 4eabf23543574fe481e1931601207426
msgid "add separator"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2851
#: ../../source/autoapi/qfluentwidgets/index.rst:2864
#: ../../source/autoapi/qfluentwidgets/index.rst:3116
#: ../../source/autoapi/qfluentwidgets/index.rst:3129
#: 0d5499c6fdc5428686ca99bdc617a2ed
msgid "position: NavigationPostion"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2852
#: ../../source/autoapi/qfluentwidgets/index.rst:2865
#: ../../source/autoapi/qfluentwidgets/index.rst:3117
#: ../../source/autoapi/qfluentwidgets/index.rst:3130
#: 28ebfd231d274c3382701e556d82b263
msgid "where to add the separator"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2870
#: ../../source/autoapi/qfluentwidgets/index.rst:3135
#: f4dbeeb7a1a74386b760de49fb03dc35
msgid "remove widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2880
#: f6eff4a5a1ad471b8c0a83e008094c7c
msgid "set whether the menu button is visible"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2885
#: a297e872bf7a4946934de40c0376490b
msgid "set whether the return button is visible"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2890
#: ../../source/autoapi/qfluentwidgets/index.rst:3160
#: b8855ffc3754468ea8ca1e9e28d6d85b
msgid "set the maximum width"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2895
#: 1cb432185b374fd38ec14604380134e7
msgid "expand navigation panel"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2900
#: 61ad5db13ad9485dadbe2832a430654c
msgid "collapse navigation panel"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2905
#: 4b25847348d1436a9f0c61e2f3ac6f68
msgid "toggle navigation panel"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2910
#: ../../source/autoapi/qfluentwidgets/index.rst:3145
#: ../../source/autoapi/qfluentwidgets/index.rst:3216
#: 0deaaafa8f4844008f33a9cf515fd399
msgid "set current selected item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2929
#: ../../source/autoapi/qfluentwidgets/index.rst:3155
#: 7f9c2c5d732f42e99e7d835da3d33c38
msgid "set the routing key to use when the navigation history is empty"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:2999
#: ../../source/autoapi/qfluentwidgets/index.rst:3030
#: ../../source/autoapi/qfluentwidgets/index.rst:3058
#: ../../source/autoapi/qfluentwidgets/index.rst:3092
#: ../../source/autoapi/qfluentwidgets/index.rst:3139
#: c539bc820f0749c1b7fdb12ea576a750
msgid "routKey: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3020
#: ../../source/autoapi/qfluentwidgets/index.rst:3045
#: ../../source/autoapi/qfluentwidgets/index.rst:3079
#: ../../source/autoapi/qfluentwidgets/index.rst:3107
#: f3122dff2ba142c5a03e9741b9a958be
msgid ""
"the route key of parent item, the parent item should be "
"`NavigationTreeWidgetBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3039
#: ../../source/autoapi/qfluentwidgets/index.rst:3101
#: 127271f0cc854a13ba4ff58a53254855
msgid "where the widget is added"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3050
#: 73bdb67a441547429a98e490aa0be9d5
msgid "insert navigation item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3073
#: 7d5bde1753ab48888525df742ba571b1
msgid "where the item is added"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3149
#: ../../source/autoapi/qfluentwidgets/index.rst:3366
#: ../../source/autoapi/qfluentwidgets/index.rst:3439
#: 21ca83fa9f5d4c788067584fbf30c015
msgid "name: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3181
#: 6825286eb7bb4ac18ae8656954c51fe2
msgid "add item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3197
#: dcabc04da7484866988ee7d9695177c1
msgid "insert item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3226
#: 576e22da240b4c4fafababb69e5f0c83
msgid "set the pixel font size of items"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3259
#: 350ea93e7f0549729b461735e01d647e
msgid "set the selected date"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3271
#: 078abe38e7234fed905203f746322776
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.date_time.picker_base.PickerBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3282
#: ../../source/autoapi/qfluentwidgets/index.rst:3342
#: aea2895cf1e24265ac1c8a9655b66989
msgid "set current date"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3306
#: 2750c9d376294e189052f36cbda74bbe
msgid "Bases: :py:obj:`DatePickerBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3322
#: 015f280ccd75439ebfac0e2c77c9a805
msgid "set the format of date"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3326
#: b344f278747640ec9d51930eddb0c1d0
msgid "format: int"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3327
#: 1425633c8e2745c2964af5d361e3aaeb
msgid ""
"the format of date, could be `DatePicker.MM_DD_YYYY` or "
"`DatePicker.YYYY_MM_DD`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3332
#: ../../source/autoapi/qfluentwidgets/index.rst:3470
#: ../../source/autoapi/qfluentwidgets/index.rst:3591
#: ../../source/autoapi/qfluentwidgets/index.rst:3618
#: 1e5c5b8708fe40cc9cd874fe46c382aa
msgid "initial value of panel"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3337
#: 0a49110a4b62474c857251458fd52634
msgid "set whether the month column is tight"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3348
#: 8b97779825454cf68e9a5abb41458b0f
msgid "Bases: :py:obj:`DatePicker`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3361
#: 7222c4b4eb694651b9199b328308f181
msgid "add column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3366
#: ../../source/autoapi/qfluentwidgets/index.rst:3439
#: 4d3b30598b35419e948163def800f3fb
msgid "the name of column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3369
#: ../../source/autoapi/qfluentwidgets/index.rst:3442
#: cb616f27c98a46ab9e4b25335c9e9742
msgid "items: Iterable"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3369
#: ../../source/autoapi/qfluentwidgets/index.rst:3442
#: 4fe025faea794b94b6a618be26373c40
msgid "the items of column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3372
#: ../../source/autoapi/qfluentwidgets/index.rst:3445
#: ../../source/autoapi/qfluentwidgets/index.rst:3503
#: ../../source/autoapi/qfluentwidgets/index.rst:3671
#: 9421cd24f8e24e93ab5e032ea6c1b474
msgid "width: int"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3372
#: ../../source/autoapi/qfluentwidgets/index.rst:3445
#: dce6c42651d7425cb690e7a87c020fd3
msgid "the width of column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3375
#: ../../source/autoapi/qfluentwidgets/index.rst:3448
#: d4ec50f644e34fba8e5464f0abcae879
msgid "the text alignment of button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3377
#: 3aa95fd8fb0e4bd881c4afd9b4a61096
msgid "formatter: PickerColumnFormatter"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3378
#: e3054db5332a49c997e17f8c789758af
msgid "the formatter of column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3383
#: ../../source/autoapi/qfluentwidgets/index.rst:3398
#: 196c043290544b3bb9294b67e03252a8
msgid "set the text alignment of specified column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3388
#: 0aa72a75934e40d2a83f7a1582be1bbe
msgid "set the width of specified column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3393
#: 9e90d7336ae74b99a94afbbc3cf8bc4b
msgid "make the specified column to be tight"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3421
#: ../../source/autoapi/qfluentwidgets/index.rst:3559
#: 4d4c76e11ca749d5b62122d2cef887ff
msgid "convert original value to formatted value"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3426
#: 04658bfdf05f4e538767c0434841c5fd
msgid "convert formatted value to origin value"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3431
#: 6421de13849a4b8ca197ae5e1026b38e
msgid "set column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3436
#: 6591003641ae4d76add050285ba39f22
msgid "the index of column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3453
#: 2b969bfe06a949ab9d4a09f3286ae4e2
msgid "clear columns"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3495
#: 36ea67209c91414b85b23d54cbf4a4c6
msgid "add one column to view"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3503
#: 3065e0e416174ff5a3109fba62d847b7
msgid "the width of item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3514
#: 9b400cccbf5c432c84e8c2679afecac2
msgid "return the value of columns"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3519
#: e7a9f391ad404b61909ef7641c7daeb4
msgid "set the value of columns"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3524
#: 7cd4cc06cf664a02af009db142632482
msgid "return the value of specified column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3529
#: a7dea374f65c461b844320291d83efff
msgid "set the value of specified column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3534
#: 6106e9e714254462b35f282d9df745a7
msgid "return the list widget of specified column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3539
#: 480b3a0a3c634f19882571ef88cd9273
msgid "show panel"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3564
#: 987740d53a114fe2b28e736971bc33bc
msgid "convert formatted value to original value"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3570
#: ../../source/autoapi/qfluentwidgets/index.rst:3597
#: 5bb5f59caa9645ff95878c8b2c88bd4f
msgid "Bases: :py:obj:`TimePickerBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3576
#: ../../source/autoapi/qfluentwidgets/index.rst:3608
#: fc8e5a1d6de1414da7d76f61ef039418
msgid "set current time"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3580
#: ../../source/autoapi/qfluentwidgets/index.rst:3612
#: 186d22a6572a40a580da5b047f5e309f
msgid "time: QTime"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3581
#: ../../source/autoapi/qfluentwidgets/index.rst:3613
#: c8463a467ff542b98be1ac11964ff135
msgid "current time"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3586
#: ../../source/autoapi/qfluentwidgets/index.rst:3603
#: 085d8cafa30c48118e3c8b1c940d9ea7
msgid "set the visibility of seconds column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3629
#: ../../source/autoapi/qfluentwidgets/index.rst:4284
#: f9bafbe5a1fe4fd991fa020156480580
msgid "widget: QWidget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3629
#: ece250cfd27744789de2159b1ea52add
msgid "the widget to set font"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3631
#: ../../source/autoapi/qfluentwidgets/index.rst:3641
#: 69e3887cb48b4f2fbbf0aad1072e14e9
msgid "fontSize: int"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3632
#: ../../source/autoapi/qfluentwidgets/index.rst:3642
#: 9d39e1e4873e476bbfa2bc013c4049d7
msgid "font pixel size"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3657
#: 31e488f1c47c4ecfa4aa1c8654da7326
msgid "Return the screen column width for a char"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3663
#: 4ae1c4fe0fa54926a4ce7a3ce7ffdf60
msgid "Wrap according to string length"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3668
#: c8168c4ee34245778096f56ec97295cd
msgid "the text to be wrapped"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3671
#: 01ed42cffd2e4a318dcd724efbf9ee7f
msgid "the maximum length of a single line, the length of Chinese characters is 2"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3674
#: ef161960f7a145e4b2177caec5d42793
msgid "once: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3674
#: f3268d89fde54994a8daadcf4808d058
msgid "whether to wrap only once"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3677
#: ../../source/autoapi/qfluentwidgets/index.rst:4272
#: 5036e910457d467ebee400cef35deb9e
msgid "Returns"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3679
#: 1045d6be0d0e42269e74d4fc3295b55b
msgid "wrap_text: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3679
#: d25e69a9464240e08897dba2f2f48c77
msgid "text after auto word wrap process"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3681
#: 782db12ac87146a7b9abc26ad09ba40b
msgid "is_wrapped: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3682
#: d816fde7d2b6457ab0a0a9816172104b
msgid "whether a line break occurs in the text"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3688
#: ff46391d801c453d90adcd5291962c69
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QAction`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3701
#: b047407bf55545438783e4f7766e736e
msgid "Bases: :py:obj:`PyQt5.QtGui.QIcon`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3716
#: ece3c4627939483a8261a1b772baf3b5
msgid "icon: str | bytes | QByteArray"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3716
#: 5744e7ba0410462695365147b0342e39
msgid "the path or code of svg icon"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3719
#: ../../source/autoapi/qfluentwidgets/index.rst:4190
#: ../../source/autoapi/qfluentwidgets/index.rst:4237
#: a26b14654a9b4019bc15782689b8993b
msgid "painter: QPainter"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3719
#: ../../source/autoapi/qfluentwidgets/index.rst:4190
#: ../../source/autoapi/qfluentwidgets/index.rst:4237
#: 291d4a4b961a430ab85b9b85b90dad43
msgid "painter"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3721
#: ../../source/autoapi/qfluentwidgets/index.rst:4193
#: ../../source/autoapi/qfluentwidgets/index.rst:4240
#: 3ea70682557b4ea5addf20915eb7e1b0
msgid "rect: QRect | QRectF"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3722
#: ../../source/autoapi/qfluentwidgets/index.rst:4193
#: ../../source/autoapi/qfluentwidgets/index.rst:4240
#: e2773e18f5fd4b8ab32f422dbadf2ca9
msgid "the rect to render icon"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:3727
#: 85551212128d47ffafb6266009e376b1
msgid "Bases: :py:obj:`FluentIconBase`, :py:obj:`enum.Enum`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4187
#: da660c2a7e52467495593f7bd9be2646
msgid "icon: str | QIcon | FluentIconBaseBase"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4187
#: 96143520e44e489394452d9290fe9985
msgid "the icon to be drawn"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4195
#: 6f3c6d644a8147f883dd314f48ba541b
msgid "**attribute:"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4196
#: f8ac1da3974a4791977a7719a88fffcf
msgid "the attribute of svg icon"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4219
#: fc8cf9d271ac4f3d8a99b6b03555abaf
msgid "create an fluent icon"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4249
#: ../../source/autoapi/qfluentwidgets/index.rst:4266
#: b4141e6d20384f58bbae6f78d84fe84f
msgid "indexes: List[int]"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4249
#: e47f07ce65654b6897c1d586d18c5012
msgid "the svg path to be modified"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4251
#: ../../source/autoapi/qfluentwidgets/index.rst:4269
#: f60d982f52684b3988c6369685e3bf4d
msgid "**attributes:"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4252
#: 5f2db377b6aa499eab985b9322e8f50d
msgid "the attributes of modified path"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4263
#: 7ff5db87ecb943b9adf82d2b6ef339ec
msgid "iconPath: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4263
#: 37fedb2078bc42bbb5b68e24f14ca39b
msgid "svg icon path"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4266
#: a7c7e15150604a04b8d1aeb5caa74c1d
msgid "the path to be filled"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4269
#: caac86ca4e5948228cddcb4f47f81832
msgid "the attributes of path"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4273
#: 5d0479fe1a1949ee87f61618617bfbf5
msgid "svg: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4274
#: 281166c4f01f4648b508031b35b676bc
msgid "svg code"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4284
#: 49346f0a87ce454eaccace97611b7801
msgid "the widget to set style sheet"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4287
#: ../../source/autoapi/qfluentwidgets/index.rst:4304
#: 0962d4c2064046968f0cd8c1f781af3b
msgid "file: str | StyleSheetBase"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4287
#: ../../source/autoapi/qfluentwidgets/index.rst:4304
#: 2e95cbd743894bdd93cdce71024b6731
msgid "qss file"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4290
#: ../../source/autoapi/qfluentwidgets/index.rst:4307
#: c63ce95a0d964d7d9864bb6bf6680e44
msgid "the theme of style sheet"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4293
#: f1df5353281d4d87ad106b64418a8324
msgid "register: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4293
#: fbe94c689d5b4667b918d1de1b5014f8
msgid ""
"whether to register the widget to the style manager. If `register=True`, "
"the style of the widget will be updated automatically when the theme "
"changes"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4317
#: bc8a53bf2ba44128b2c3d2886edc815c
msgid "theme mode"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4319
#: ../../source/autoapi/qfluentwidgets/index.rst:4387
#: ../../source/autoapi/qfluentwidgets/index.rst:4964
#: 5f00bcd77f8a4383a7d8f9ea50b3ecd1
msgid "save: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4320
#: ../../source/autoapi/qfluentwidgets/index.rst:4965
#: 960060b9ab7944f9ba3c20c8d24c936c
msgid "whether to save the change to config file"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4366
#: 3cec5f05b0194c42ad269804871a744f
msgid "The name of the Enum member."
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4385
#: 7a0bfe490e0f4eb8bd13e227902ba290
msgid "color: QColor | Qt.GlobalColor | str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4385
#: e739f3ba89224f5b86286c8bdbdb928c
msgid "theme color"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4388
#: 7abc275f2f7f410084a277e34cd36d9f
msgid "whether to save to change to config file"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4398
#: 071f05c6932f4d458b8583bc01f10118
msgid "qss: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4398
#: a0db00da40f7444ba10b17726f5bba10
msgid ""
"the style sheet string to apply theme color, the substituted variable "
"should be equal to the value of `ThemeColor` and starts width `--`, i.e "
"`--ThemeColorPrimary`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4404
#: 820a23f1ce77459789f07370e629ac27
msgid "Bases: :py:obj:`StyleSheetBase`, :py:obj:`enum.Enum`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4535
#: ../../source/autoapi/qfluentwidgets/index.rst:4546
#: 65861b16ca7041f9bc9a9f882ccf16f9
msgid "get the path of style sheet"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4551
#: 160b02038c6a471cb91f7d42728f43d0
msgid "get the content of style sheet"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4556
#: f7ad839d077343ef811530de4e38f46c
msgid "apply style sheet to widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4607
#: b479a44d2aec402b9b3b83b7530c8f6d
msgid "Bases: :py:obj:`PyQt5.QtCore.QTranslator`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4613
#: 7e6042a56e584d4bb21c173b0f4db264
msgid "load translation file"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4633
#: 7cb052efa21b41198659b20fb6446c3d
msgid "set the default route key of stacked widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4638
#: 2a0f2b29409c41af94752b2c6e815526
msgid "push history"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4643
#: 71cbfc1ebd05436282785a7b7fd6d152
msgid "stacked: QStackedWidget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4643
#: d01eceae4437460b82901d9ebe6ed34c
msgid "stacked widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4646
#: fa4dedb98d5842a88ee892d5f87b2322
msgid "route key of sub insterface, it should be the object name of sub interface"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4651
#: df34aa7fbfd24a4d8c0dbbeaec589277
msgid "pop history"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4656
#: 8a6dce00c62e45c39fd69ecc501e86c7
msgid "remove history"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4666
#: 3eb3ceeac0014b0d88d7a87d9fc40565
msgid "*default:"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4667
#: c6f6f6796d7d4d97b0d5f6b723bf0ffc
msgid "the default value returned when an exception occurs"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4698
#: ../../source/autoapi/qfluentwidgets/index.rst:4715
#: ../../source/autoapi/qfluentwidgets/index.rst:4732
#: ../../source/autoapi/qfluentwidgets/index.rst:4756
#: ../../source/autoapi/qfluentwidgets/index.rst:4773
#: ../../source/autoapi/qfluentwidgets/index.rst:4790
#: 98ff9a791b93450fb59753c8b2143f72
msgid "Verify whether the value is legal"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4703
#: ../../source/autoapi/qfluentwidgets/index.rst:4720
#: ../../source/autoapi/qfluentwidgets/index.rst:4737
#: ../../source/autoapi/qfluentwidgets/index.rst:4761
#: ../../source/autoapi/qfluentwidgets/index.rst:4778
#: ../../source/autoapi/qfluentwidgets/index.rst:4795
#: 3e2123e8f7b34bd58714709033493bb2
msgid "correct illegal value"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4709
#: ../../source/autoapi/qfluentwidgets/index.rst:4726
#: ../../source/autoapi/qfluentwidgets/index.rst:4750
#: ../../source/autoapi/qfluentwidgets/index.rst:4767
#: ../../source/autoapi/qfluentwidgets/index.rst:4784
#: 62c23b3809ee4d89802735b21c5ff66a
msgid "Bases: :py:obj:`ConfigValidator`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4743
#: e393c95bd9cf4a7db66687e9760702c1
msgid "Bases: :py:obj:`OptionsValidator`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4805
#: ../../source/autoapi/qfluentwidgets/index.rst:4822
#: ../../source/autoapi/qfluentwidgets/index.rst:4839
#: 2fea4a6ac0e94a978cbb71cde3d348f7
msgid "serialize config value"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4810
#: ../../source/autoapi/qfluentwidgets/index.rst:4827
#: ../../source/autoapi/qfluentwidgets/index.rst:4844
#: 161dcd2c8cdd4d15990e9236a43ec475
msgid "deserialize config from config file's value"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4816
#: ../../source/autoapi/qfluentwidgets/index.rst:4833
#: 6e278de24de74d3baeccf7c5d77f31cf
msgid "Bases: :py:obj:`ConfigSerializer`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4856
#: ../../source/autoapi/qfluentwidgets/index.rst:4949
#: 0779a9a8c55d4c8aa65c2403a60b0e49
msgid "get the value of config item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4861
#: c735b46ba79742a88dc58d8813d77ead
msgid "get the config key separated by `.`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4880
#: ../../source/autoapi/qfluentwidgets/index.rst:4895
#: ../../source/autoapi/qfluentwidgets/index.rst:4908
#: 2eb0c2f358f948658fbfbff58ed1a3ec a72fcdc9e99747b3a7f8048ac9dabc4d
msgid "Bases: :py:obj:`ConfigItem`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4886
#: a2a59920c96e4e5696e0263b3c4b3a99
msgid "get the available range of config"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4924
#: a728293ed22546938a3c5ba559250959
msgid "get theme mode, can be `Theme.Light` or `Theme.Dark`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4959
#: 3109488249844c9fb888f9c5dfb4b766
msgid "item: ConfigItem"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4959
#: 1d501ab19962420b821fcbeac3db967c
msgid "config item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4962
#: aced5441dd4647319a7f99feee83619d
msgid "value:"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4962
#: 3d9e3ee28a57478fb9916c7574862586
msgid "the new value of config item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4970
#: ab64d19143834b80a2346895181312be
msgid "convert config items to `dict`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4975
#: 6712ea55c60240e0be194e58907b8907
msgid "save config"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4980
#: 31bfa01d110f459c9a1c91533caaf40a
msgid "load config"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4985
#: 71fea78324be470f855f4f4a4461b196
msgid "file: str or Path"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4985
#: 9e8036b5180b45ad86da019ff37803d5
msgid "the path of json config file"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4987
#: 52564503190d4945825995b4779675c6
msgid "config: Config"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/index.rst:4988
#: e48c04d1773542c1950ffbc9fcdc57e8
msgid "config object to be initialized"
msgstr ""

#~ msgid "get style sheet from `qfluentwidgets` embedded qss file"
#~ msgstr ""

#~ msgid "A scroll area which can scroll smoothly"
#~ msgstr ""

#~ msgid "Bases: :py:obj:`PyQt5.QtWidgets.QScrollBar`"
#~ msgstr ""

#~ msgid "global position of widget"
#~ msgstr ""

#~ msgid "size: QSize"
#~ msgstr ""

#~ msgid "size of widget"
#~ msgstr ""

#~ msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTreeWidget`"
#~ msgstr ""

#~ msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTreeView`"
#~ msgstr ""

#~ msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTableView`, :py:obj:`TableBase`"
#~ msgstr ""

#~ msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTableWidget`, :py:obj:`TableBase`"
#~ msgstr ""

