<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP 待办工具演示 - 完整工作流程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0;
            min-height: 80vh;
        }

        .demo-panel {
            padding: 30px;
            border-right: 1px solid #dee2e6;
        }

        .code-panel {
            padding: 30px;
            background: #f8f9fa;
        }

        .step-container {
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .step-header {
            background: #3498db;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .step-header:hover {
            background: #2980b9;
        }

        .step-header.active {
            background: #27ae60;
        }

        .step-content {
            padding: 20px;
            background: white;
            display: none;
        }

        .step-content.active {
            display: block;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        .btn.success {
            background: #27ae60;
        }

        .btn.success:hover {
            background: #229954;
        }

        .output-area {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            min-height: 300px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-y: auto;
            margin-bottom: 20px;
        }

        .code-display {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
            margin-bottom: 20px;
        }

        .status-bar {
            background: #34495e;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-connected {
            background: #27ae60;
        }

        .status-disconnected {
            background: #e74c3c;
        }

        .status-processing {
            background: #f39c12;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .todo-list {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .todo-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }

        .todo-item:last-child {
            border-bottom: none;
        }

        .todo-item.completed {
            text-decoration: line-through;
            opacity: 0.6;
        }

        .message-flow {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .message-type {
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            margin-right: 15px;
            min-width: 80px;
            text-align: center;
        }

        .message-type.request {
            background: #e74c3c;
        }

        .message-type.response {
            background: #27ae60;
        }

        .message-type.notification {
            background: #f39c12;
        }

        .arrow {
            font-size: 20px;
            color: #3498db;
            margin: 0 10px;
        }

        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .demo-panel {
                border-right: none;
                border-bottom: 1px solid #dee2e6;
            }
        }

        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }

        .json-key {
            color: #e74c3c;
        }

        .json-string {
            color: #27ae60;
        }

        .json-number {
            color: #3498db;
        }

        .json-boolean {
            color: #9b59b6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📝 MCP 待办工具演示</h1>
            <p>通过待办事项管理展示 Model Context Protocol 完整工作流程</p>
        </div>

        <div class="main-content">
            <!-- 演示面板 -->
            <div class="demo-panel">
                <h2>🎮 交互演示</h2>
                
                <div class="status-bar">
                    <div>
                        <span class="status-indicator" id="connection-status"></span>
                        <span id="connection-text">未连接</span>
                    </div>
                    <div>
                        <span>协议版本: 2024-11-05</span>
                    </div>
                </div>

                <div class="controls">
                    <button class="btn" onclick="runStep(1)" id="step1-btn">1. 建立连接</button>
                    <button class="btn" onclick="runStep(2)" id="step2-btn" disabled>2. 列出工具</button>
                    <button class="btn" onclick="runStep(3)" id="step3-btn" disabled>3. 获取待办</button>
                    <button class="btn" onclick="runStep(4)" id="step4-btn" disabled>4. 添加待办</button>
                    <button class="btn" onclick="runStep(5)" id="step5-btn" disabled>5. 完成待办</button>
                    <button class="btn" onclick="runStep(6)" id="step6-btn" disabled>6. 断开连接</button>
                    <button class="btn" onclick="resetDemo()" style="background: #e74c3c;">重置演示</button>
                </div>

                <div class="output-area" id="demo-output">
点击 "1. 建立连接" 开始MCP待办工具演示...

这个演示将展示：
• MCP客户端与服务器的连接建立
• 工具发现和能力协商
• 待办事项的增删改查操作
• 完整的JSON-RPC消息交换
• 连接的优雅断开
                </div>

                <div class="todo-list" id="todo-display">
                    <h3>📋 当前待办事项</h3>
                    <div id="todo-items">
                        <p style="color: #666; text-align: center; padding: 20px;">
                            暂无待办事项，请先建立MCP连接
                        </p>
                    </div>
                </div>
            </div>

            <!-- 代码面板 -->
            <div class="code-panel">
                <h2>💻 代码与消息</h2>

                <!-- 步骤1: 连接建立 -->
                <div class="step-container" id="step1-container">
                    <div class="step-header" onclick="toggleStep(1)">
                        <span>步骤1: 建立MCP连接</span>
                        <span id="step1-status">⏳</span>
                    </div>
                    <div class="step-content" id="step1-content">
                        <h4>📤 客户端发送初始化请求</h4>
                        <div class="message-flow">
                            <span class="message-type request">REQUEST</span>
                            <span class="arrow">→</span>
                            <span>initialize</span>
                        </div>
                        <div class="code-display" id="step1-request">
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "initialize",
  "params": {
    "protocolVersion": "2024-11-05",
    "capabilities": {
      "roots": {
        "listChanged": true
      },
      "sampling": {}
    },
    "clientInfo": {
      "name": "Todo MCP Client",
      "version": "1.0.0"
    }
  }
}
                        </div>

                        <h4>📥 服务器响应初始化</h4>
                        <div class="message-flow">
                            <span class="message-type response">RESPONSE</span>
                            <span class="arrow">←</span>
                            <span>initialize result</span>
                        </div>
                        <div class="code-display" id="step1-response">
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "protocolVersion": "2024-11-05",
    "capabilities": {
      "tools": {
        "listChanged": true
      },
      "resources": {
        "subscribe": true,
        "listChanged": true
      }
    },
    "serverInfo": {
      "name": "todo-server",
      "version": "1.0.0"
    }
  }
}
                        </div>

                        <h4>📢 客户端发送初始化完成通知</h4>
                        <div class="message-flow">
                            <span class="message-type notification">NOTIFY</span>
                            <span class="arrow">→</span>
                            <span>initialized</span>
                        </div>
                        <div class="code-display">
{
  "jsonrpc": "2.0",
  "method": "initialized"
}
                        </div>
                    </div>
                </div>

                <!-- 步骤2: 工具发现 -->
                <div class="step-container" id="step2-container">
                    <div class="step-header" onclick="toggleStep(2)">
                        <span>步骤2: 发现可用工具</span>
                        <span id="step2-status">⏳</span>
                    </div>
                    <div class="step-content" id="step2-content">
                        <h4>📤 请求工具列表</h4>
                        <div class="message-flow">
                            <span class="message-type request">REQUEST</span>
                            <span class="arrow">→</span>
                            <span>tools/list</span>
                        </div>
                        <div class="code-display">
{
  "jsonrpc": "2.0",
  "id": 2,
  "method": "tools/list"
}
                        </div>

                        <h4>📥 服务器返回可用工具</h4>
                        <div class="message-flow">
                            <span class="message-type response">RESPONSE</span>
                            <span class="arrow">←</span>
                            <span>tools list</span>
                        </div>
                        <div class="code-display">
{
  "jsonrpc": "2.0",
  "id": 2,
  "result": {
    "tools": [
      {
        "name": "list_todos",
        "description": "获取所有待办事项列表",
        "inputSchema": {
          "type": "object",
          "properties": {},
          "required": []
        }
      },
      {
        "name": "add_todo",
        "description": "添加新的待办事项",
        "inputSchema": {
          "type": "object",
          "properties": {
            "title": {
              "type": "string",
              "description": "待办事项标题"
            },
            "description": {
              "type": "string",
              "description": "详细描述"
            }
          },
          "required": ["title"]
        }
      },
      {
        "name": "complete_todo",
        "description": "标记待办事项为已完成",
        "inputSchema": {
          "type": "object",
          "properties": {
            "id": {
              "type": "number",
              "description": "待办事项ID"
            }
          },
          "required": ["id"]
        }
      }
    ]
  }
}
                        </div>
                    </div>
                </div>

                <!-- 步骤3: 获取待办事项 -->
                <div class="step-container" id="step3-container">
                    <div class="step-header" onclick="toggleStep(3)">
                        <span>步骤3: 获取待办事项</span>
                        <span id="step3-status">⏳</span>
                    </div>
                    <div class="step-content" id="step3-content">
                        <h4>📤 调用 list_todos 工具</h4>
                        <div class="message-flow">
                            <span class="message-type request">REQUEST</span>
                            <span class="arrow">→</span>
                            <span>tools/call</span>
                        </div>
                        <div class="code-display">
{
  "jsonrpc": "2.0",
  "id": 3,
  "method": "tools/call",
  "params": {
    "name": "list_todos",
    "arguments": {}
  }
}
                        </div>

                        <h4>📥 返回待办事项列表</h4>
                        <div class="message-flow">
                            <span class="message-type response">RESPONSE</span>
                            <span class="arrow">←</span>
                            <span>tool result</span>
                        </div>
                        <div class="code-display">
{
  "jsonrpc": "2.0",
  "id": 3,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "当前待办事项列表：\n1. 学习MCP协议 (进行中)\n2. 完成项目文档 (待完成)\n3. 准备会议材料 (待完成)"
      }
    ]
  }
}
                        </div>
                    </div>
                </div>

                <!-- 步骤4: 添加待办事项 -->
                <div class="step-container" id="step4-container">
                    <div class="step-header" onclick="toggleStep(4)">
                        <span>步骤4: 添加新待办事项</span>
                        <span id="step4-status">⏳</span>
                    </div>
                    <div class="step-content" id="step4-content">
                        <h4>📤 调用 add_todo 工具</h4>
                        <div class="message-flow">
                            <span class="message-type request">REQUEST</span>
                            <span class="arrow">→</span>
                            <span>tools/call</span>
                        </div>
                        <div class="code-display">
{
  "jsonrpc": "2.0",
  "id": 4,
  "method": "tools/call",
  "params": {
    "name": "add_todo",
    "arguments": {
      "title": "测试MCP工具调用",
      "description": "验证MCP协议的工具调用功能是否正常工作"
    }
  }
}
                        </div>

                        <h4>📥 返回添加结果</h4>
                        <div class="message-flow">
                            <span class="message-type response">RESPONSE</span>
                            <span class="arrow">←</span>
                            <span>tool result</span>
                        </div>
                        <div class="code-display">
{
  "jsonrpc": "2.0",
  "id": 4,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "✅ 成功添加待办事项：\nID: 4\n标题: 测试MCP工具调用\n描述: 验证MCP协议的工具调用功能是否正常工作\n状态: 待完成"
      }
    ]
  }
}
                        </div>
                    </div>
                </div>

                <!-- 步骤5: 完成待办事项 -->
                <div class="step-container" id="step5-container">
                    <div class="step-header" onclick="toggleStep(5)">
                        <span>步骤5: 完成待办事项</span>
                        <span id="step5-status">⏳</span>
                    </div>
                    <div class="step-content" id="step5-content">
                        <h4>📤 调用 complete_todo 工具</h4>
                        <div class="message-flow">
                            <span class="message-type request">REQUEST</span>
                            <span class="arrow">→</span>
                            <span>tools/call</span>
                        </div>
                        <div class="code-display">
{
  "jsonrpc": "2.0",
  "id": 5,
  "method": "tools/call",
  "params": {
    "name": "complete_todo",
    "arguments": {
      "id": 1
    }
  }
}
                        </div>

                        <h4>📥 返回完成结果</h4>
                        <div class="message-flow">
                            <span class="message-type response">RESPONSE</span>
                            <span class="arrow">←</span>
                            <span>tool result</span>
                        </div>
                        <div class="code-display">
{
  "jsonrpc": "2.0",
  "id": 5,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "✅ 待办事项已完成：\nID: 1 - 学习MCP协议\n状态: 已完成 ✓"
      }
    ]
  }
}
                        </div>
                    </div>
                </div>

                <!-- 步骤6: 断开连接 -->
                <div class="step-container" id="step6-container">
                    <div class="step-header" onclick="toggleStep(6)">
                        <span>步骤6: 断开MCP连接</span>
                        <span id="step6-status">⏳</span>
                    </div>
                    <div class="step-content" id="step6-content">
                        <h4>🔌 优雅断开连接</h4>
                        <div class="message-flow">
                            <span class="message-type notification">NOTIFY</span>
                            <span class="arrow">→</span>
                            <span>connection close</span>
                        </div>
                        <div class="code-display">
// 客户端主动关闭连接
client.close();

// 或者服务器端关闭
server.close();

// 清理资源和状态
connectionStatus = "disconnected";
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局状态
        let currentStep = 0;
        let isConnected = false;
        let todos = [
            { id: 1, title: "学习MCP协议", description: "深入理解Model Context Protocol", completed: false },
            { id: 2, title: "完成项目文档", description: "编写技术文档和用户手册", completed: false },
            { id: 3, title: "准备会议材料", description: "整理下周项目评审的材料", completed: false }
        ];

        // 运行指定步骤
        function runStep(step) {
            if (step <= currentStep + 1) {
                currentStep = step;

                // 更新步骤状态
                updateStepStatus(step, 'processing');

                // 执行对应的步骤逻辑
                switch(step) {
                    case 1:
                        executeStep1();
                        break;
                    case 2:
                        executeStep2();
                        break;
                    case 3:
                        executeStep3();
                        break;
                    case 4:
                        executeStep4();
                        break;
                    case 5:
                        executeStep5();
                        break;
                    case 6:
                        executeStep6();
                        break;
                }
            }
        }

        // 步骤1: 建立连接
        function executeStep1() {
            const output = document.getElementById('demo-output');
            output.textContent = '';

            appendOutput('🔄 开始建立MCP连接...\n\n');
            appendOutput('📤 发送初始化请求到待办服务器\n');
            appendOutput('   → 协商协议版本: 2024-11-05\n');
            appendOutput('   → 声明客户端能力\n\n');

            setTimeout(() => {
                appendOutput('📥 收到服务器响应\n');
                appendOutput('   ✓ 协议版本匹配\n');
                appendOutput('   ✓ 服务器支持工具调用\n');
                appendOutput('   ✓ 服务器支持资源订阅\n\n');

                setTimeout(() => {
                    appendOutput('📢 发送初始化完成通知\n\n');
                    appendOutput('✅ MCP连接建立成功！\n');
                    appendOutput('   服务器: todo-server v1.0.0\n');
                    appendOutput('   状态: 已连接\n\n');

                    // 更新连接状态
                    isConnected = true;
                    updateConnectionStatus(true);
                    updateStepStatus(1, 'completed');
                    enableNextStep(2);

                    // 展开步骤1的代码
                    toggleStep(1);
                }, 1000);
            }, 1500);
        }

        // 步骤2: 列出工具
        function executeStep2() {
            appendOutput('\n🔍 发现可用工具...\n\n');
            appendOutput('📤 请求工具列表\n');

            setTimeout(() => {
                appendOutput('📥 收到工具列表响应\n');
                appendOutput('   发现 3 个可用工具:\n');
                appendOutput('   • list_todos - 获取待办事项列表\n');
                appendOutput('   • add_todo - 添加新待办事项\n');
                appendOutput('   • complete_todo - 完成待办事项\n\n');
                appendOutput('✅ 工具发现完成！\n\n');

                updateStepStatus(2, 'completed');
                enableNextStep(3);
                toggleStep(2);
            }, 1200);
        }

        // 步骤3: 获取待办事项
        function executeStep3() {
            appendOutput('\n📋 获取当前待办事项...\n\n');
            appendOutput('📤 调用 list_todos 工具\n');

            setTimeout(() => {
                appendOutput('📥 收到待办事项列表\n');
                appendOutput('   当前有 3 个待办事项:\n');
                todos.forEach(todo => {
                    const status = todo.completed ? '✓' : '○';
                    appendOutput(`   ${status} ${todo.id}. ${todo.title}\n`);
                });
                appendOutput('\n✅ 待办事项获取完成！\n\n');

                updateTodoDisplay();
                updateStepStatus(3, 'completed');
                enableNextStep(4);
                toggleStep(3);
            }, 1000);
        }

        // 步骤4: 添加待办事项
        function executeStep4() {
            appendOutput('\n➕ 添加新的待办事项...\n\n');
            appendOutput('📤 调用 add_todo 工具\n');
            appendOutput('   标题: "测试MCP工具调用"\n');
            appendOutput('   描述: "验证MCP协议的工具调用功能"\n\n');

            setTimeout(() => {
                appendOutput('📥 收到添加结果\n');
                appendOutput('   ✅ 新待办事项已创建\n');
                appendOutput('   ID: 4\n');
                appendOutput('   状态: 待完成\n\n');

                // 添加新的待办事项
                todos.push({
                    id: 4,
                    title: "测试MCP工具调用",
                    description: "验证MCP协议的工具调用功能是否正常工作",
                    completed: false
                });

                updateTodoDisplay();
                updateStepStatus(4, 'completed');
                enableNextStep(5);
                toggleStep(4);
            }, 1200);
        }

        // 步骤5: 完成待办事项
        function executeStep5() {
            appendOutput('\n✅ 完成待办事项...\n\n');
            appendOutput('📤 调用 complete_todo 工具\n');
            appendOutput('   目标ID: 1 (学习MCP协议)\n\n');

            setTimeout(() => {
                appendOutput('📥 收到完成结果\n');
                appendOutput('   ✅ 待办事项已标记为完成\n');
                appendOutput('   ID: 1 - 学习MCP协议\n');
                appendOutput('   状态: 已完成 ✓\n\n');

                // 更新待办事项状态
                const todo = todos.find(t => t.id === 1);
                if (todo) {
                    todo.completed = true;
                }

                updateTodoDisplay();
                updateStepStatus(5, 'completed');
                enableNextStep(6);
                toggleStep(5);
            }, 1000);
        }

        // 步骤6: 断开连接
        function executeStep6() {
            appendOutput('\n🔌 断开MCP连接...\n\n');
            appendOutput('📤 发送连接关闭信号\n');
            appendOutput('   → 清理客户端资源\n');
            appendOutput('   → 关闭传输通道\n\n');

            setTimeout(() => {
                appendOutput('✅ MCP连接已安全断开\n');
                appendOutput('   状态: 未连接\n');
                appendOutput('   资源: 已清理\n\n');
                appendOutput('🎉 MCP待办工具演示完成！\n\n');
                appendOutput('总结:\n');
                appendOutput('• 成功建立MCP连接\n');
                appendOutput('• 发现并使用了3个工具\n');
                appendOutput('• 完成了待办事项的增删改查\n');
                appendOutput('• 体验了完整的MCP工作流程\n');

                // 更新连接状态
                isConnected = false;
                updateConnectionStatus(false);
                updateStepStatus(6, 'completed');
                toggleStep(6);
            }, 1500);
        }

        // 更新步骤状态
        function updateStepStatus(step, status) {
            const statusEl = document.getElementById(`step${step}-status`);
            const headerEl = document.querySelector(`#step${step}-container .step-header`);

            switch(status) {
                case 'processing':
                    statusEl.textContent = '🔄';
                    headerEl.style.background = '#f39c12';
                    break;
                case 'completed':
                    statusEl.textContent = '✅';
                    headerEl.classList.add('active');
                    break;
                default:
                    statusEl.textContent = '⏳';
            }
        }

        // 启用下一步按钮
        function enableNextStep(step) {
            const btn = document.getElementById(`step${step}-btn`);
            if (btn) {
                btn.disabled = false;
                btn.classList.add('success');
            }
        }

        // 更新连接状态
        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connection-status');
            const textEl = document.getElementById('connection-text');

            if (connected) {
                statusEl.className = 'status-indicator status-connected';
                textEl.textContent = '已连接';
            } else {
                statusEl.className = 'status-indicator status-disconnected';
                textEl.textContent = '未连接';
            }
        }

        // 更新待办事项显示
        function updateTodoDisplay() {
            const container = document.getElementById('todo-items');
            container.innerHTML = '';

            if (todos.length === 0) {
                container.innerHTML = '<p style="color: #666; text-align: center; padding: 20px;">暂无待办事项</p>';
                return;
            }

            todos.forEach(todo => {
                const item = document.createElement('div');
                item.className = `todo-item ${todo.completed ? 'completed' : ''}`;
                item.innerHTML = `
                    <div>
                        <strong>${todo.title}</strong>
                        <br>
                        <small style="color: #666;">${todo.description}</small>
                    </div>
                    <div>
                        <span style="color: ${todo.completed ? '#27ae60' : '#f39c12'};">
                            ${todo.completed ? '✅ 已完成' : '⏳ 待完成'}
                        </span>
                    </div>
                `;
                container.appendChild(item);
            });
        }

        // 切换步骤显示
        function toggleStep(step) {
            const content = document.getElementById(`step${step}-content`);
            const isVisible = content.style.display === 'block';

            // 隐藏所有步骤内容
            for (let i = 1; i <= 6; i++) {
                const stepContent = document.getElementById(`step${i}-content`);
                if (stepContent) {
                    stepContent.style.display = 'none';
                }
            }

            // 显示当前步骤（如果之前是隐藏的）
            if (!isVisible) {
                content.style.display = 'block';
                content.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }
        }

        // 重置演示
        function resetDemo() {
            currentStep = 0;
            isConnected = false;

            // 重置待办事项
            todos = [
                { id: 1, title: "学习MCP协议", description: "深入理解Model Context Protocol", completed: false },
                { id: 2, title: "完成项目文档", description: "编写技术文档和用户手册", completed: false },
                { id: 3, title: "准备会议材料", description: "整理下周项目评审的材料", completed: false }
            ];

            // 重置UI状态
            updateConnectionStatus(false);
            updateTodoDisplay();

            // 重置按钮状态
            for (let i = 1; i <= 6; i++) {
                const btn = document.getElementById(`step${i}-btn`);
                if (btn) {
                    btn.disabled = i > 1;
                    btn.classList.remove('success');
                }

                // 重置步骤状态
                updateStepStatus(i, 'waiting');

                // 隐藏步骤内容
                const content = document.getElementById(`step${i}-content`);
                if (content) {
                    content.style.display = 'none';
                }

                // 重置步骤头部样式
                const header = document.querySelector(`#step${i}-container .step-header`);
                if (header) {
                    header.classList.remove('active');
                    header.style.background = '#3498db';
                }
            }

            // 重置输出
            const output = document.getElementById('demo-output');
            output.textContent = `点击 "1. 建立连接" 开始MCP待办工具演示...

这个演示将展示：
• MCP客户端与服务器的连接建立
• 工具发现和能力协商
• 待办事项的增删改查操作
• 完整的JSON-RPC消息交换
• 连接的优雅断开`;
        }

        // 添加输出文本
        function appendOutput(text) {
            const output = document.getElementById('demo-output');
            output.textContent += text;
            output.scrollTop = output.scrollHeight;
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化连接状态
            updateConnectionStatus(false);
            updateTodoDisplay();

            // 禁用除第一步外的所有按钮
            for (let i = 2; i <= 6; i++) {
                const btn = document.getElementById(`step${i}-btn`);
                if (btn) {
                    btn.disabled = true;
                }
            }

            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key >= '1' && e.key <= '6') {
                    e.preventDefault();
                    const step = parseInt(e.key);
                    if (step <= currentStep + 1) {
                        runStep(step);
                    }
                }

                if (e.ctrlKey && e.key === 'r') {
                    e.preventDefault();
                    resetDemo();
                }
            });

            // 添加提示信息
            console.log('MCP待办工具演示已加载');
            console.log('快捷键: Ctrl**** 执行步骤, Ctrl+R 重置演示');
        });
    </script>
</body>
</html>
