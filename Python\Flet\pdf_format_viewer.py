#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF格式查看器 - 对比原始提取和优化后的格式
帮助用户了解PDF文本提取后的格式变化
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import warnings
from pathlib import Path

# 抑制字体相关警告
warnings.filterwarnings("ignore", message=".*FontBBox.*")
warnings.filterwarnings("ignore", message=".*font descriptor.*")

try:
    import PyPDF2
    import pdfplumber
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    print("请安装: pip install PyPDF2 pdfplumber")


class PDFFormatViewer:
    def __init__(self, root):
        self.root = root
        self.root.title("PDF格式查看器 - 对比原始与优化格式")
        self.root.geometry("1400x800")
        self.root.minsize(1000, 600)
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面控件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="PDF格式查看器 - 了解文本提取后的格式变化", 
            font=('Arial', 16, 'bold')
        )
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="📁 选择PDF文件", padding="10")
        file_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        self.select_button = ttk.Button(file_frame, text="选择PDF文件", command=self.select_file)
        self.select_button.grid(row=0, column=0, padx=(0, 10))
        
        self.file_path_var = tk.StringVar(value="未选择文件")
        self.file_path_label = ttk.Label(file_frame, textvariable=self.file_path_var, foreground="gray")
        self.file_path_label.grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        # 对比显示区域
        compare_frame = ttk.Frame(main_frame)
        compare_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        compare_frame.columnconfigure(0, weight=1)
        compare_frame.columnconfigure(1, weight=1)
        compare_frame.rowconfigure(0, weight=1)
        
        # 左侧：原始格式
        original_frame = ttk.LabelFrame(compare_frame, text="📄 原始提取格式", padding="10")
        original_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        original_frame.columnconfigure(0, weight=1)
        original_frame.rowconfigure(0, weight=1)
        
        self.original_text = scrolledtext.ScrolledText(
            original_frame,
            wrap=tk.WORD,
            width=50,
            height=30,
            font=('Consolas', 9),
            state=tk.DISABLED
        )
        self.original_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 右侧：优化格式
        optimized_frame = ttk.LabelFrame(compare_frame, text="✨ 优化后格式", padding="10")
        optimized_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        optimized_frame.columnconfigure(0, weight=1)
        optimized_frame.rowconfigure(0, weight=1)
        
        self.optimized_text = scrolledtext.ScrolledText(
            optimized_frame,
            wrap=tk.WORD,
            width=50,
            height=30,
            font=('Consolas', 9),
            state=tk.DISABLED
        )
        self.optimized_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        status_frame = ttk.Frame(main_frame, relief=tk.SUNKEN, borderwidth=1)
        status_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(1, weight=1)
        
        ttk.Label(status_frame, text="状态:", font=('Arial', 10)).grid(row=0, column=0, padx=(5, 0))
        self.status_var = tk.StringVar(value="请选择PDF文件进行格式对比")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var, font=('Arial', 10))
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(5, 0))
        
    def update_status(self, message, color="black"):
        """更新状态信息"""
        self.status_var.set(message)
        self.status_label.configure(foreground=color)
        self.root.update()
        
    def extract_original_text(self, pdf_path):
        """提取原始文本（不进行任何优化）"""
        try:
            text = ""
            
            # 使用pdfplumber提取原始文本
            try:
                with pdfplumber.open(pdf_path) as pdf:
                    for page_num, page in enumerate(pdf.pages):
                        try:
                            page_text = page.extract_text()
                            if page_text:
                                if page_num > 0:
                                    text += f"\n{'='*30} 第 {page_num + 1} 页 {'='*30}\n"
                                text += page_text + "\n"
                        except Exception:
                            continue
                            
                if text.strip():
                    return text
                    
            except Exception:
                pass
            
            # 备用方案：使用PyPDF2
            try:
                with open(pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page_num, page in enumerate(pdf_reader.pages):
                        try:
                            page_text = page.extract_text()
                            if page_text:
                                if page_num > 0:
                                    text += f"\n{'='*30} 第 {page_num + 1} 页 {'='*30}\n"
                                text += page_text + "\n"
                        except Exception:
                            continue
                            
                if text.strip():
                    return text
                    
            except Exception:
                pass
            
            return "无法提取文本"
            
        except Exception as e:
            return f"文本提取失败: {str(e)}"
    
    def extract_optimized_text(self, pdf_path):
        """提取优化后的文本"""
        import re
        
        try:
            # 先获取原始文本
            original_text = self.extract_original_text(pdf_path)
            
            if "无法提取文本" in original_text or "提取失败" in original_text:
                return original_text
            
            # 应用格式优化
            text = original_text
            
            # 1. 处理多余的空行
            text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)
            
            # 2. 处理行末的连字符（英文单词被分割）
            text = re.sub(r'-\s*\n\s*([a-z])', r'\1', text)
            
            # 3. 处理中文标点后的换行
            text = re.sub(r'([。！？；：])\s*\n\s*([^\n=])', r'\1\n\2', text)
            
            # 4. 处理英文句号后的换行
            text = re.sub(r'([.!?])\s*\n\s*([A-Z])', r'\1 \2', text)
            
            # 5. 处理段落内的不必要换行（中文）
            text = re.sub(r'([^。！？；：\n=])\s*\n\s*([^第\n\d=])', r'\1\2', text)
            
            # 6. 保持列表格式
            text = re.sub(r'\n(\s*[•·▪▫◦‣⁃]\s*)', r'\n\1', text)
            text = re.sub(r'\n(\s*\d+[.)]\s*)', r'\n\1', text)
            text = re.sub(r'\n(\s*[a-zA-Z][.)]\s*)', r'\n\1', text)
            
            # 7. 处理表格分隔符
            text = re.sub(r'\s*\|\s*', ' | ', text)
            
            # 8. 清理首尾空白
            text = text.strip()
            
            return text
            
        except Exception as e:
            return f"格式优化失败: {str(e)}"
    
    def select_file(self):
        """选择PDF文件"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择PDF文件进行格式对比",
                filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
            )
            
            if file_path:
                self.file_path_var.set(f"已选择: {Path(file_path).name}")
                self.file_path_label.configure(foreground="black")
                
                # 提取原始文本
                self.update_status("正在提取原始格式文本...", "blue")
                original_text = self.extract_original_text(file_path)
                
                # 提取优化文本
                self.update_status("正在生成优化格式文本...", "blue")
                optimized_text = self.extract_optimized_text(file_path)
                
                # 显示对比结果
                self.original_text.configure(state=tk.NORMAL)
                self.original_text.delete(1.0, tk.END)
                self.original_text.insert(1.0, original_text)
                self.original_text.configure(state=tk.DISABLED)
                
                self.optimized_text.configure(state=tk.NORMAL)
                self.optimized_text.delete(1.0, tk.END)
                self.optimized_text.insert(1.0, optimized_text)
                self.optimized_text.configure(state=tk.DISABLED)
                
                # 统计信息
                original_lines = len(original_text.split('\n'))
                optimized_lines = len(optimized_text.split('\n'))
                original_chars = len(original_text)
                optimized_chars = len(optimized_text)
                
                self.update_status(
                    f"对比完成 | 原始: {original_lines}行/{original_chars}字符 | "
                    f"优化: {optimized_lines}行/{optimized_chars}字符", 
                    "green"
                )
                
        except Exception as e:
            self.update_status(f"错误: {str(e)}", "red")
            messagebox.showerror("错误", f"文件处理失败:\n{str(e)}")


def main():
    root = tk.Tk()
    app = PDFFormatViewer(root)
    root.mainloop()


if __name__ == "__main__":
    main()
