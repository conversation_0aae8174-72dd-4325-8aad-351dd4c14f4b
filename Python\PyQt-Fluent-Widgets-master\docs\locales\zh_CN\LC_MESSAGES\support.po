# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, zhiyiYo
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-10 23:18+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/support.md:1 a0ff5dd170ba4e9bb5c40cbfc62084a9
msgid "Support"
msgstr "支持"

#: ../../source/support.md:2 7029ec01aff64aab84d568beaa7b18ab
msgid ""
"If this project helps you a lot and you want to support the development "
"and maintenance of this project, feel free to sponsor me via 爱发电 or ko-"
"fi. Your support is highly appreciated 🥰"
msgstr "个人开发不易，如果这个组件库帮助了您，可以考虑在 [爱发电](https://afdian.net/a/zhiyiYo) 或者 [ko-fi](https://ko-fi.com/zhiyiYo) 上请作者喝一瓶快乐水。您的支持就是作者继续开发和维护项目的动力 🥰"

#: ../../source/support.md:4 4a96b52a6da745bb8a874850790eb6d4
msgid "Sponsors"
msgstr "致谢"

