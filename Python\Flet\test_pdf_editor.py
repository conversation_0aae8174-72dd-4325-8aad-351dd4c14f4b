#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文本替换编辑工具测试脚本
用于验证修复后的功能
"""

import flet as ft
import sys
import os

def test_imports():
    """测试所有必要的导入"""
    print("测试导入...")
    
    try:
        import PyPDF2
        print("✓ PyPDF2 导入成功")
    except ImportError:
        print("✗ PyPDF2 导入失败")
        return False
    
    try:
        import pdfplumber
        print("✓ pdfplumber 导入成功")
    except ImportError:
        print("✗ pdfplumber 导入失败")
        return False
    
    try:
        from reportlab.pdfgen import canvas
        print("✓ reportlab 导入成功")
    except ImportError:
        print("✗ reportlab 导入失败")
        return False
    
    print("✓ 所有依赖导入成功")
    return True

def test_flet_colors():
    """测试flet颜色常量"""
    print("\n测试flet颜色...")
    
    try:
        colors = [
            ft.Colors.RED,
            ft.Colors.GREEN,
            ft.Colors.BLUE,
            ft.Colors.GREY_600,
            ft.Colors.GREY_100,
            ft.Colors.WHITE
        ]
        print("✓ flet颜色常量测试成功")
        return True
    except AttributeError as e:
        print(f"✗ flet颜色常量测试失败: {e}")
        return False

def create_test_pdf():
    """创建一个测试PDF文件"""
    print("\n创建测试PDF...")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        test_pdf_path = "test_document.pdf"
        c = canvas.Canvas(test_pdf_path, pagesize=letter)
        
        # 添加测试内容
        c.drawString(100, 750, "这是一个测试PDF文档")
        c.drawString(100, 730, "包含一些中文文本用于测试替换功能")
        c.drawString(100, 710, "Hello World - English text for testing")
        c.drawString(100, 690, "原始文本：需要被替换的内容")
        c.drawString(100, 670, "更多测试内容...")
        
        c.save()
        print(f"✓ 测试PDF创建成功: {test_pdf_path}")
        return test_pdf_path
    except Exception as e:
        print(f"✗ 测试PDF创建失败: {e}")
        return None

def test_pdf_text_extraction(pdf_path):
    """测试PDF文本提取"""
    print(f"\n测试PDF文本提取: {pdf_path}")
    
    if not pdf_path or not os.path.exists(pdf_path):
        print("✗ PDF文件不存在")
        return False
    
    try:
        import pdfplumber
        
        with pdfplumber.open(pdf_path) as pdf:
            text = ""
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        
        print(f"✓ PDF文本提取成功，提取了 {len(text)} 个字符")
        print(f"提取的文本预览: {text[:100]}...")
        return True
    except Exception as e:
        print(f"✗ PDF文本提取失败: {e}")
        return False

def simple_gui_test():
    """简单的GUI测试"""
    print("\n启动简单GUI测试...")
    
    def main(page: ft.Page):
        page.title = "PDF编辑工具测试"
        page.window_width = 600
        page.window_height = 400
        
        def close_test(e):
            print("✓ GUI测试完成")
            page.window_close()
        
        page.add(
            ft.Column([
                ft.Text("PDF文本替换编辑工具测试", size=20, weight=ft.FontWeight.BOLD),
                ft.Text("如果您能看到这个界面，说明flet GUI正常工作", color=ft.Colors.GREEN),
                ft.ElevatedButton("关闭测试", on_click=close_test),
                ft.Container(
                    content=ft.Text("状态: GUI测试正常", color=ft.Colors.BLUE),
                    bgcolor=ft.Colors.GREY_100,
                    padding=10,
                    border_radius=5
                )
            ], 
            spacing=20,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER
            ),
        )
    
    try:
        ft.app(target=main)
        return True
    except Exception as e:
        print(f"✗ GUI测试失败: {e}")
        return False

def main():
    print("=" * 50)
    print("PDF文本替换编辑工具 - 功能测试")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 依赖库测试失败，请安装缺少的库")
        return
    
    # 测试flet颜色
    if not test_flet_colors():
        print("\n❌ flet颜色测试失败")
        return
    
    # 创建测试PDF
    test_pdf = create_test_pdf()
    
    # 测试PDF处理
    if test_pdf:
        test_pdf_text_extraction(test_pdf)
    
    print("\n" + "=" * 50)
    print("基础功能测试完成")
    print("=" * 50)
    
    # GUI测试
    gui_test = input("\n是否进行GUI测试? (y/n): ").lower().strip()
    if gui_test in ['y', 'yes', '是']:
        simple_gui_test()
    
    # 清理测试文件
    if test_pdf and os.path.exists(test_pdf):
        try:
            os.remove(test_pdf)
            print(f"✓ 清理测试文件: {test_pdf}")
        except:
            print(f"⚠ 无法删除测试文件: {test_pdf}")
    
    print("\n✅ 测试完成！现在可以运行主程序: python pdf_text_editor.py")

if __name__ == "__main__":
    main()
