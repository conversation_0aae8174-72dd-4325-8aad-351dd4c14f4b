<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
</head>
<body>
    <h1>数据解析调试</h1>
    <button onclick="testCSVParsing()">测试CSV解析</button>
    <button onclick="testBitwardenCSV()">测试Bitwarden CSV</button>
    <button onclick="testJSONParsing()">测试JSON解析</button>
    <button onclick="testTXTParsing()">测试TXT解析</button>
    
    <div id="output" style="margin-top: 20px; padding: 20px; background: #f5f5f5; border-radius: 8px;">
        <h3>解析结果:</h3>
        <pre id="result"></pre>
    </div>

    <script>
        // 简化的解析器类用于测试
        class TestParser {
            constructor() {
                this.data = [];
            }

            parseCSV(text) {
                this.data = [];
                const lines = text.split('\n').filter(line => line.trim());
                const headers = lines[0].split('\t').map(h => h.trim().toLowerCase()); // 支持制表符分隔

                // 如果不是制表符分隔，尝试逗号分隔
                if (headers.length === 1) {
                    headers[0] = lines[0];
                    const commaHeaders = lines[0].split(',').map(h => h.trim().toLowerCase());
                    if (commaHeaders.length > 1) {
                        headers.splice(0, 1, ...commaHeaders);
                    }
                }

                console.log('CSV Headers:', headers);

                for (let i = 1; i < lines.length; i++) {
                    let values;
                    // 根据header的分隔符来分割数据
                    if (headers.length > 1 && lines[0].includes('\t')) {
                        values = lines[i].split('\t');
                    } else {
                        values = lines[i].split(',');
                    }

                    const item = {};

                    headers.forEach((header, index) => {
                        item[header] = values[index] ? values[index].trim() : '';
                    });

                    // 支持多种字段名格式
                    const url = item.url || item.uri || item.website || item.login_uri || item['login uri'] || '';
                    const name = item.name || item.title || item.login_name || '未命名';
                    const username = item.username || item.user || item.login || item.login_username || item['login username'] || '';
                    const password = item.password || item.login_password || item['login password'] || '';

                    console.log('解析项目:', { name, url, username });

                    this.data.push({
                        name: name,
                        url: url,
                        username: username,
                        password: password,
                        domain: this.extractDomain(url)
                    });
                }
            }

            parseJSON(text) {
                this.data = [];
                const jsonData = JSON.parse(text);
                
                if (jsonData.items && Array.isArray(jsonData.items)) {
                    jsonData.items.forEach(item => {
                        if (item.login && item.login.uris && Array.isArray(item.login.uris)) {
                            item.login.uris.forEach(uri => {
                                const url = uri.uri || uri.url || '';
                                this.data.push({
                                    name: item.name || '未命名',
                                    url: url,
                                    username: item.login.username || '',
                                    password: item.login.password || '',
                                    domain: this.extractDomain(url)
                                });
                            });
                        }
                    });
                }
            }

            parseTXT(text) {
                this.data = [];
                const lines = text.split('\n').filter(line => line.trim());
                
                lines.forEach(line => {
                    line = line.trim();
                    if (line) {
                        const urlMatch = line.match(/https?:\/\/[^\s]+/);
                        if (urlMatch) {
                            this.data.push({
                                name: '从文本提取',
                                url: urlMatch[0],
                                username: '',
                                password: '',
                                domain: this.extractDomain(urlMatch[0])
                            });
                        } else {
                            const domainMatch = line.match(/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(:\d+)?$/) || 
                                              line.match(/^(\d+\.){3}\d+(:\d+)?$/) ||
                                              line.match(/^localhost(:\d+)?$/i);
                            if (domainMatch) {
                                this.data.push({
                                    name: '从文本提取',
                                    url: line,
                                    username: '',
                                    password: '',
                                    domain: this.extractDomain(line)
                                });
                            }
                        }
                    }
                });
            }

            extractDomain(url) {
                try {
                    if (!url || !url.includes('://')) {
                        return url;
                    }
                    const urlObj = new URL(url);
                    return urlObj.hostname;
                } catch {
                    return url;
                }
            }
        }

        const parser = new TestParser();

        function testCSVParsing() {
            const csvData = `name,url,username,password
WPS账户,account.wps.cn,<EMAIL>,wps123
GitHub,https://github.com,<EMAIL>,password123`;

            parser.parseCSV(csvData);
            document.getElementById('result').textContent = JSON.stringify(parser.data, null, 2);
        }

        function testBitwardenCSV() {
            const bitwardenData = `folder	favorite	type	name	notes	fields	reprompt	login_uri	login_username	login_password
		login	::1			0	http://[::1]:2243	!@#$	!@#$@@$#
		login	GitHub			0	https://github.com	<EMAIL>	password123
		login	WPS账户			0	https://account.wps.cn	<EMAIL>	wps123`;

            parser.parseCSV(bitwardenData);
            document.getElementById('result').textContent = JSON.stringify(parser.data, null, 2);
        }

        function testJSONParsing() {
            const jsonData = `{
  "items": [
    {
      "name": "WPS账户",
      "login": {
        "username": "<EMAIL>",
        "password": "wps123",
        "uris": [
          {
            "uri": "account.wps.cn"
          }
        ]
      }
    }
  ]
}`;
            
            parser.parseJSON(jsonData);
            document.getElementById('result').textContent = JSON.stringify(parser.data, null, 2);
        }

        function testTXTParsing() {
            const txtData = `account.wps.cn
https://github.com
www.baidu.com
***********`;
            
            parser.parseTXT(txtData);
            document.getElementById('result').textContent = JSON.stringify(parser.data, null, 2);
        }
    </script>
</body>
</html>
