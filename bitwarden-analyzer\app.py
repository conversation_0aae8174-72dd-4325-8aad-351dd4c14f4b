#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bitwarden 数据筛查工具 - Python 后端
支持状态保存和恢复功能
"""

import os
import json
import csv
import time
import sqlite3
import re
import ipaddress
from datetime import datetime
from urllib.parse import urlparse
from flask import Flask, request, jsonify, render_template, send_from_directory
from werkzeug.utils import secure_filename
import threading
import uuid
import requests

# 可选导入CORS
try:
    from flask_cors import CORS
    CORS_AVAILABLE = True
except ImportError:
    CORS_AVAILABLE = False
    print("警告: Flask-CORS 未安装，跨域功能将不可用")
# from concurrent.futures import ThreadPoolExecutor  # 暂时不使用

app = Flask(__name__)

# 可选启用CORS
if CORS_AVAILABLE:
    CORS(app)
    print("✅ CORS已启用")
else:
    print("⚠️ CORS未启用（Flask-CORS未安装）")

# 配置
DATABASE_FILE = 'bitwarden_analysis.db'
UPLOAD_FOLDER = 'uploads'
RESULTS_FOLDER = 'results'

# 确保文件夹存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(RESULTS_FOLDER, exist_ok=True)

class BitwardenAnalyzer:
    def __init__(self):
        self.init_database()
        self.running_tasks = {}
        
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()

        # 创建任务表（如果不存在）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tasks (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                status TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                total_items INTEGER DEFAULT 0,
                tested_items INTEGER DEFAULT 0,
                success_count INTEGER DEFAULT 0,
                failed_count INTEGER DEFAULT 0,
                skipped_count INTEGER DEFAULT 0,
                internal_count INTEGER DEFAULT 0,
                invalid_count INTEGER DEFAULT 0,
                current_testing_name TEXT,
                current_testing_url TEXT,
                current_testing_status TEXT,
                settings TEXT,
                data_file TEXT
            )
        ''')
        
        # 创建结果表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id TEXT NOT NULL,
                name TEXT,
                url TEXT,
                username TEXT,
                domain TEXT,
                status TEXT,
                status_code INTEGER,
                title TEXT,
                message TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (task_id) REFERENCES tasks (id)
            )
        ''')
        
        # 创建重复项表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS duplicates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id TEXT NOT NULL,
                type TEXT NOT NULL,
                key_value TEXT NOT NULL,
                items TEXT NOT NULL,
                FOREIGN KEY (task_id) REFERENCES tasks (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def create_task(self, name, data_file, settings=None):
        """创建新任务"""
        task_id = str(uuid.uuid4())
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO tasks (id, name, status, settings, data_file)
            VALUES (?, ?, ?, ?, ?)
        ''', (task_id, name, 'created', json.dumps(settings or {}), data_file))
        
        conn.commit()
        conn.close()
        
        return task_id
    
    def get_task(self, task_id):
        """获取任务信息"""
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM tasks WHERE id = ?', (task_id,))
        task = cursor.fetchone()
        
        if task:
            columns = [desc[0] for desc in cursor.description]
            task_dict = dict(zip(columns, task))
            task_dict['settings'] = json.loads(task_dict['settings'] or '{}')
        else:
            task_dict = None
            
        conn.close()
        return task_dict
    
    def get_all_tasks(self):
        """获取所有任务"""
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM tasks ORDER BY created_at DESC')
        tasks = cursor.fetchall()
        
        columns = [desc[0] for desc in cursor.description]
        task_list = []
        for task in tasks:
            task_dict = dict(zip(columns, task))
            task_dict['settings'] = json.loads(task_dict['settings'] or '{}')
            task_list.append(task_dict)
            
        conn.close()
        return task_list
    
    def update_task_status(self, task_id, status, **kwargs):
        """更新任务状态"""
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()

        update_fields = ['status = ?', 'updated_at = CURRENT_TIMESTAMP']
        values = [status]

        for key, value in kwargs.items():
            if key in ['total_items', 'tested_items', 'success_count', 'failed_count',
                      'skipped_count', 'internal_count', 'invalid_count',
                      'current_testing_name', 'current_testing_url', 'current_testing_status']:
                update_fields.append(f'{key} = ?')
                values.append(value)

        values.append(task_id)

        cursor.execute(f'''
            UPDATE tasks SET {', '.join(update_fields)}
            WHERE id = ?
        ''', values)

        conn.commit()
        conn.close()

    def update_current_testing(self, task_id, name, url, status):
        """更新当前测试状态"""
        self.update_task_status(task_id, 'running',
                               current_testing_name=name,
                               current_testing_url=url,
                               current_testing_status=status)
    
    def save_result(self, task_id, result):
        """保存测试结果"""
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO results (task_id, name, url, username, domain, status, 
                                status_code, title, message)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            task_id,
            result.get('name'),
            result.get('url'),
            result.get('username'),
            result.get('domain'),
            result.get('status'),
            result.get('status_code'),
            result.get('title'),
            result.get('message')
        ))
        
        conn.commit()
        conn.close()
    
    def get_results(self, task_id):
        """获取任务结果"""
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM results WHERE task_id = ? ORDER BY id', (task_id,))
        results = cursor.fetchall()
        
        columns = [desc[0] for desc in cursor.description]
        result_list = [dict(zip(columns, result)) for result in results]
        
        conn.close()
        return result_list
    
    def parse_data_file(self, file_path):
        """解析数据文件"""
        data = []
        
        try:
            if file_path.endswith('.json'):
                data = self.parse_json_file(file_path)
            elif file_path.endswith('.csv'):
                data = self.parse_csv_file(file_path)
            elif file_path.endswith('.txt'):
                data = self.parse_txt_file(file_path)
        except Exception as e:
            print(f"解析文件失败: {e}")
            
        return data
    
    def parse_json_file(self, file_path):
        """解析JSON文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        data = []
        if 'items' in json_data and isinstance(json_data['items'], list):
            for item in json_data['items']:
                if item.get('login') and item['login'].get('uris'):
                    for uri in item['login']['uris']:
                        url = uri.get('uri') or uri.get('url') or ''
                        data.append({
                            'name': item.get('name', '未命名'),
                            'url': url,
                            'username': item['login'].get('username', ''),
                            'password': item['login'].get('password', ''),
                            'domain': self.extract_domain(url)
                        })
        
        return data
    
    def parse_csv_file(self, file_path):
        """解析CSV文件"""
        data = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            # 检测分隔符
            first_line = f.readline()
            f.seek(0)
            
            delimiter = '\t' if '\t' in first_line else ','
            reader = csv.DictReader(f, delimiter=delimiter)
            
            for row in reader:
                # 支持多种字段名格式
                url = (row.get('url') or row.get('uri') or row.get('website') or 
                      row.get('login_uri') or row.get('login uri') or '').strip()
                name = (row.get('name') or row.get('title') or row.get('login_name') or '未命名').strip()
                username = (row.get('username') or row.get('user') or row.get('login') or 
                           row.get('login_username') or row.get('login username') or '').strip()
                password = (row.get('password') or row.get('login_password') or 
                           row.get('login password') or '').strip()
                
                if url:  # 只添加有URL的项目
                    data.append({
                        'name': name,
                        'url': url,
                        'username': username,
                        'password': password,
                        'domain': self.extract_domain(url)
                    })
        
        return data
    
    def parse_txt_file(self, file_path):
        """解析TXT文件"""
        data = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:
                    data.append({
                        'name': '从文本提取',
                        'url': line,
                        'username': '',
                        'password': '',
                        'domain': self.extract_domain(line)
                    })
        
        return data
    
    def extract_domain(self, url):
        """提取域名"""
        try:
            if not url or '://' not in url:
                return url
            parsed = urlparse(url)
            return parsed.hostname or url
        except:
            return url

    def is_internal_url(self, url):
        """检查是否为内网地址"""
        try:
            if not url:
                return False

            # 清理URL
            url = url.strip()
            if not url.startswith(('http://', 'https://')):
                if re.match(r'^(\d+\.){3}\d+', url) or 'localhost' in url.lower():
                    url = 'http://' + url
                else:
                    url = 'https://' + url

            parsed = urlparse(url)
            hostname = parsed.hostname

            if not hostname:
                return False

            # 检查IPv4内网地址
            try:
                ip = ipaddress.ip_address(hostname)
                return ip.is_private or ip.is_loopback or ip.is_link_local
            except ValueError:
                pass

            # 检查特殊域名
            hostname_lower = hostname.lower()
            internal_patterns = [
                'localhost',
                '.local',
                '::1',
                'fe80:',
                'fc00:',
                'fd00:'
            ]

            return any(pattern in hostname_lower for pattern in internal_patterns)

        except Exception as e:
            print(f"检查内网地址失败: {e}")
            return False

    def test_url(self, url, timeout=5):
        """测试URL可访问性"""
        try:
            print(f"测试URL: {url}")

            # 清理URL
            url = url.strip()
            if not url.startswith(('http://', 'https://')):
                if re.match(r'^(\d+\.){3}\d+', url) or 'localhost' in url.lower():
                    url = 'http://' + url
                else:
                    url = 'https://' + url

            print(f"清理后URL: {url}")

            # 设置请求头，模拟浏览器
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            # 先尝试HEAD请求
            try:
                print("尝试HEAD请求...")
                response = requests.head(url, timeout=timeout, allow_redirects=True, headers=headers)
                status_code = response.status_code
                print(f"HEAD请求成功，状态码: {status_code}")

                # 如果HEAD成功，尝试GET获取标题
                try:
                    print("尝试GET请求获取标题...")
                    get_response = requests.get(url, timeout=timeout, allow_redirects=True, headers=headers)
                    title_match = re.search(r'<title[^>]*>([^<]+)</title>', get_response.text, re.IGNORECASE)
                    title = title_match.group(1).strip() if title_match else '无标题'
                    print(f"获取到标题: {title}")
                except Exception as e:
                    print(f"获取标题失败: {e}")
                    title = '无法获取标题'

                return {
                    'status': 'success',
                    'status_code': status_code,
                    'title': title,
                    'message': f'链接可访问 ({status_code})'
                }

            except requests.exceptions.RequestException as e:
                print(f"HEAD请求失败: {e}, 尝试GET请求...")
                # HEAD失败，尝试GET
                response = requests.get(url, timeout=timeout, allow_redirects=True, headers=headers)
                status_code = response.status_code
                print(f"GET请求成功，状态码: {status_code}")

                title_match = re.search(r'<title[^>]*>([^<]+)</title>', response.text, re.IGNORECASE)
                title = title_match.group(1).strip() if title_match else '无标题'
                print(f"获取到标题: {title}")

                return {
                    'status': 'success',
                    'status_code': status_code,
                    'title': title,
                    'message': f'链接可访问 ({status_code})'
                }

        except requests.exceptions.Timeout:
            print("请求超时")
            return {
                'status': 'failed',
                'status_code': None,
                'title': None,
                'message': '请求超时'
            }
        except requests.exceptions.ConnectionError as e:
            print(f"连接失败: {e}")
            return {
                'status': 'failed',
                'status_code': None,
                'title': None,
                'message': '连接失败'
            }
        except Exception as e:
            print(f"测试失败: {e}")
            return {
                'status': 'failed',
                'status_code': None,
                'title': None,
                'message': f'测试失败: {str(e)}'
            }

    def test_url_with_interrupt(self, url, task_id, timeout=3):
        """支持中断的URL测试"""
        # 简化实现，避免线程问题
        try:
            # 在测试前检查是否有手动操作
            if task_id in self.running_tasks and self.running_tasks[task_id].get('manual_action'):
                return {
                    'status': 'interrupted',
                    'status_code': None,
                    'title': None,
                    'message': '测试被中断'
                }

            # 直接调用原始测试方法，使用更短的超时
            return self.test_url(url, timeout)

        except Exception as e:
            print(f"URL测试异常: {e}")
            return {
                'status': 'failed',
                'status_code': None,
                'title': None,
                'message': f'测试失败: {str(e)}'
            }

    def analyze_task(self, task_id):
        """分析任务"""
        try:
            print(f"开始分析任务: {task_id}")
            task = self.get_task(task_id)
            if not task:
                print(f"任务不存在: {task_id}")
                return

            print(f"任务数据文件: {task['data_file']}")
            # 解析数据文件
            data = self.parse_data_file(task['data_file'])
            if not data:
                print(f"数据解析失败: {task['data_file']}")
                self.update_task_status(task_id, 'failed')
                return

            print(f"解析到 {len(data)} 条数据")
            # 更新任务状态
            self.update_task_status(task_id, 'running', total_items=len(data))

            settings = task.get('settings', {})
            skip_internal = settings.get('skipInternalUrls', False)

            stats = {
                'tested': 0,
                'success': 0,
                'failed': 0,
                'skipped': 0,
                'internal': 0,
                'invalid': 0
            }

            # 存储当前任务的运行状态
            self.running_tasks[task_id] = {
                'data': data,
                'current_index': 0,
                'stats': stats,
                'settings': settings
            }

            print(f"开始处理 {len(data)} 个项目")

            # 批量处理内网地址以提高速度
            batch_size = 10
            batch_count = 0

            # 逐个处理项目
            for item in data:
                try:
                    # 检查任务是否被暂停或停止
                    current_task = self.get_task(task_id)
                    if current_task['status'] in ['paused', 'stopped']:
                        break

                    # 检查是否有手动操作（在开始测试前）
                    if task_id in self.running_tasks:
                        manual_action = self.running_tasks[task_id].get('manual_action')
                        if manual_action:
                            # 处理手动操作
                            if manual_action == 'skip':
                                result = {
                                    **item,
                                    'status': 'skipped',
                                    'status_code': None,
                                    'title': None,
                                    'message': '用户手动跳过'
                                }
                                stats['skipped'] += 1
                            elif manual_action == 'invalid':
                                result = {
                                    **item,
                                    'status': 'invalid',
                                    'status_code': None,
                                    'title': None,
                                    'message': '用户标记为无效'
                                }
                                stats['invalid'] += 1
                            elif manual_action == 'internal':
                                result = {
                                    **item,
                                    'status': 'internal',
                                    'status_code': None,
                                    'title': None,
                                    'message': '用户标记为内网'
                                }
                                stats['internal'] += 1

                            # 清除手动操作标记
                            self.running_tasks[task_id]['manual_action'] = None

                            # 保存结果并继续下一个
                            self.save_result(task_id, result)
                            stats['tested'] += 1
                            self.update_progress(task_id, stats)
                            continue

                    # 更新当前测试状态
                    self.update_current_testing(task_id, item.get('name', '未命名'),
                                              item.get('url', ''), '准备测试...')

                    url = item.get('url', '').strip()
                    if not url:
                        result = {
                            **item,
                            'status': 'invalid',
                            'status_code': None,
                            'title': None,
                            'message': '无URL地址'
                        }
                        stats['invalid'] += 1
                    elif skip_internal and self.is_internal_url(url):
                        result = {
                            **item,
                            'status': 'skipped',
                            'status_code': None,
                            'title': None,
                            'message': '跳过内网地址（用户设置）'
                        }
                        stats['skipped'] += 1
                        batch_count += 1
                    elif self.is_internal_url(url):
                        result = {
                            **item,
                            'status': 'internal',
                            'status_code': None,
                            'title': None,
                            'message': '检测到内网地址'
                        }
                        stats['internal'] += 1
                        batch_count += 1
                    else:
                        # 更新测试状态
                        self.update_current_testing(task_id, item.get('name', '未命名'),
                                                  url, '正在测试...')

                        # 测试URL
                        test_result = self.test_url_with_interrupt(url, task_id)

                        # 检查是否被手动操作中断
                        if task_id in self.running_tasks and self.running_tasks[task_id].get('manual_action'):
                            manual_action = self.running_tasks[task_id]['manual_action']
                            self.running_tasks[task_id]['manual_action'] = None

                            if manual_action == 'skip':
                                result = {
                                    **item,
                                    'status': 'skipped',
                                    'status_code': None,
                                    'title': None,
                                    'message': '用户手动跳过'
                                }
                                stats['skipped'] += 1
                            elif manual_action == 'invalid':
                                result = {
                                    **item,
                                    'status': 'invalid',
                                    'status_code': None,
                                    'title': None,
                                    'message': '用户标记为无效'
                                }
                                stats['invalid'] += 1
                            elif manual_action == 'internal':
                                result = {
                                    **item,
                                    'status': 'internal',
                                    'status_code': None,
                                    'title': None,
                                    'message': '用户标记为内网'
                                }
                                stats['internal'] += 1
                        else:
                            # 正常完成测试
                            result = {**item, **test_result}

                            if test_result['status'] == 'success':
                                stats['success'] += 1
                            else:
                                stats['failed'] += 1

                    # 保存结果
                    self.save_result(task_id, result)
                    stats['tested'] += 1

                    # 批量更新进度（每10个或遇到需要测试的URL时更新）
                    if batch_count >= batch_size or result.get('status') not in ['skipped', 'internal']:
                        self.update_progress(task_id, stats)
                        batch_count = 0

                    # 智能延迟
                    if result.get('status') in ['skipped', 'internal']:
                        # 内网跳过几乎无延迟
                        time.sleep(0.01)
                    else:
                        # 实际测试需要延迟
                        time.sleep(0.3)

                except Exception as e:
                    print(f"处理项目时出错: {e}")
                    # 记录错误但继续处理
                    result = {
                        **item,
                        'status': 'failed',
                        'status_code': None,
                        'title': None,
                        'message': f'处理失败: {str(e)}'
                    }
                    self.save_result(task_id, result)
                    stats['tested'] += 1
                    stats['failed'] += 1
                    continue

            # 最终更新进度
            self.update_progress(task_id, stats)

            # 清理运行状态
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]

            # 完成分析
            current_task = self.get_task(task_id)
            final_status = 'completed' if current_task['status'] != 'paused' else 'paused'
            self.update_task_status(
                task_id, final_status,
                tested_items=stats['tested'],
                success_count=stats['success'],
                failed_count=stats['failed'],
                skipped_count=stats['skipped'],
                internal_count=stats['internal'],
                invalid_count=stats['invalid'],
                current_testing_name='',
                current_testing_url='',
                current_testing_status=''
            )

        except Exception as e:
            print(f"分析任务失败: {e}")
            import traceback
            traceback.print_exc()
            self.update_task_status(task_id, 'failed')
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]

    def update_progress(self, task_id, stats):
        """更新进度"""
        self.update_task_status(
            task_id, 'running',
            tested_items=stats['tested'],
            success_count=stats['success'],
            failed_count=stats['failed'],
            skipped_count=stats['skipped'],
            internal_count=stats['internal'],
            invalid_count=stats['invalid']
        )

# 创建分析器实例
analyzer = BitwardenAnalyzer()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/tasks', methods=['GET'])
def get_tasks():
    """获取所有任务"""
    tasks = analyzer.get_all_tasks()
    return jsonify(tasks)

@app.route('/api/tasks', methods=['POST'])
def create_task():
    """创建新任务"""
    data = request.get_json()
    name = data.get('name', f'分析任务_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
    settings = data.get('settings', {})
    
    # 这里应该处理文件上传，暂时返回任务ID
    task_id = analyzer.create_task(name, '', settings)
    
    return jsonify({'task_id': task_id, 'status': 'created'})

@app.route('/api/tasks/<task_id>', methods=['GET'])
def get_task(task_id):
    """获取任务详情"""
    task = analyzer.get_task(task_id)
    if not task:
        return jsonify({'error': '任务不存在'}), 404

    # 获取结果
    results = analyzer.get_results(task_id)
    task['results'] = results

    return jsonify(task)

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """上传文件并开始分析"""
    try:
        print("收到文件上传请求")

        if 'file' not in request.files:
            print("错误: 没有文件")
            return jsonify({'error': '没有文件'}), 400

        file = request.files['file']
        task_id = request.form.get('task_id')

        print(f"文件名: {file.filename}, 任务ID: {task_id}")

        if file.filename == '':
            print("错误: 没有选择文件")
            return jsonify({'error': '没有选择文件'}), 400

        if not task_id:
            print("错误: 没有任务ID")
            return jsonify({'error': '没有任务ID'}), 400

        # 保存文件
        filename = secure_filename(file.filename)
        file_path = os.path.join(UPLOAD_FOLDER, f"{task_id}_{filename}")
        print(f"保存文件到: {file_path}")
        file.save(file_path)

        # 更新任务的数据文件路径
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute('UPDATE tasks SET data_file = ? WHERE id = ?', (file_path, task_id))
        conn.commit()
        conn.close()
        print(f"数据库已更新，文件路径: {file_path}")

        # 在后台线程中开始分析
        print("启动分析线程")
        thread = threading.Thread(target=analyzer.analyze_task, args=(task_id,))
        thread.daemon = True
        thread.start()

        return jsonify({'status': 'success', 'message': '文件上传成功，分析已开始'})

    except Exception as e:
        print(f"上传文件时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

@app.route('/api/tasks/<task_id>/pause', methods=['POST'])
def pause_task(task_id):
    """暂停任务"""
    analyzer.update_task_status(task_id, 'paused')
    return jsonify({'status': 'success', 'message': '任务已暂停'})

@app.route('/api/tasks/<task_id>/resume', methods=['POST'])
def resume_task(task_id):
    """恢复任务"""
    # 在后台线程中恢复分析
    thread = threading.Thread(target=analyzer.analyze_task, args=(task_id,))
    thread.daemon = True
    thread.start()

    return jsonify({'status': 'success', 'message': '任务已恢复'})

@app.route('/api/tasks/<task_id>/skip', methods=['POST'])
def skip_current(task_id):
    """跳过当前项目"""
    if task_id in analyzer.running_tasks:
        analyzer.running_tasks[task_id]['manual_action'] = 'skip'
        return jsonify({'status': 'success', 'message': '已跳过当前项目'})
    return jsonify({'error': '任务未运行'}), 400

@app.route('/api/tasks/<task_id>/mark_invalid', methods=['POST'])
def mark_invalid(task_id):
    """标记当前项目为无效"""
    if task_id in analyzer.running_tasks:
        analyzer.running_tasks[task_id]['manual_action'] = 'invalid'
        return jsonify({'status': 'success', 'message': '已标记为无效'})
    return jsonify({'error': '任务未运行'}), 400

@app.route('/api/tasks/<task_id>/mark_internal', methods=['POST'])
def mark_internal(task_id):
    """标记当前项目为内网"""
    if task_id in analyzer.running_tasks:
        analyzer.running_tasks[task_id]['manual_action'] = 'internal'
        return jsonify({'status': 'success', 'message': '已标记为内网'})
    return jsonify({'error': '任务未运行'}), 400

@app.route('/api/tasks/<task_id>', methods=['DELETE'])
def delete_task(task_id):
    """删除任务"""
    try:
        # 如果任务正在运行，先停止它
        if task_id in analyzer.running_tasks:
            del analyzer.running_tasks[task_id]

        # 删除数据库记录
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()

        # 删除结果
        cursor.execute('DELETE FROM results WHERE task_id = ?', (task_id,))
        # 删除重复项
        cursor.execute('DELETE FROM duplicates WHERE task_id = ?', (task_id,))
        # 删除任务
        cursor.execute('DELETE FROM tasks WHERE id = ?', (task_id,))

        conn.commit()
        conn.close()

        return jsonify({'status': 'success', 'message': '任务已删除'})
    except Exception as e:
        return jsonify({'error': f'删除失败: {str(e)}'}), 500

@app.route('/api/tasks/<task_id>/export', methods=['GET'])
def export_task(task_id):
    """导出任务结果"""
    task = analyzer.get_task(task_id)
    if not task:
        return jsonify({'error': '任务不存在'}), 404

    results = analyzer.get_results(task_id)

    export_data = {
        'task_info': task,
        'results': results,
        'export_time': datetime.now().isoformat(),
        'summary': {
            'total_items': task['total_items'],
            'tested_items': task['tested_items'],
            'success_count': task['success_count'],
            'failed_count': task['failed_count'],
            'skipped_count': task['skipped_count'],
            'internal_count': task['internal_count'],
            'invalid_count': task['invalid_count']
        }
    }

    # 保存到文件
    export_filename = f"export_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    export_path = os.path.join(RESULTS_FOLDER, export_filename)

    with open(export_path, 'w', encoding='utf-8') as f:
        json.dump(export_data, f, ensure_ascii=False, indent=2)

    return send_from_directory(RESULTS_FOLDER, export_filename, as_attachment=True)

@app.route('/static/<path:filename>')
def static_files(filename):
    """提供静态文件"""
    return send_from_directory('static', filename)

if __name__ == '__main__':
    print("启动 Bitwarden 数据筛查工具...")
    print("访问地址: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
