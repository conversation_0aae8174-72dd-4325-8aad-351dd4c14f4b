from pywebio.input import *
from pywebio.output import *
from pywebio.pin import *
import time


# info = input_group('Input Group Test', [input('age', name='age'), input('name', name='name')])

# put_text(info)
# put_text(type(info))

# from functools import partial
# def row_action(choice, id):
#     put_text("You click %s button with id: %s" % (choice, id))
# put_buttons(['edit', 'delete'], onclick=partial(row_action, id=1))
# def edit():
#     put_text("You click edit button")
# def delete():
#     put_text("You click delete button")
# put_buttons(['edit', 'delete'], onclick=[edit, delete])

put_input('id')
with use_scope('res_output', clear=True):
    put_button('btn', onclick=lambda:put_text(pin.id))

