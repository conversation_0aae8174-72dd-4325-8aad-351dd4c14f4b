# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON>o
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-22 20:49+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:2
#: beab64af64674b42aacf83ba05f9d6f6
msgid "expand_setting_card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:8
#: 880f71f9d85c43bd806a21982733a8f7
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:21:<autosummary>:1
#: 8fd69d5f9bdb46119b554deb6a2b114f
msgid ""
":py:obj:`ExpandButton "
"<qfluentwidgets.components.settings.expand_setting_card.ExpandButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:26
#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:21:<autosummary>:1
#: 34345cbbc8d14690947517069fba6481 3574c5c1292348f2b8e96b448214cd94
msgid "Expand button"
msgstr "拓展按钮"

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:21:<autosummary>:1
#: 3d7493f1303e4ec6b9a80f4cefb779a4
msgid ""
":py:obj:`ExpandSettingCard "
"<qfluentwidgets.components.settings.expand_setting_card.ExpandSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:64
#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:21:<autosummary>:1
#: 073498207cd44b67b4ab5a6d8ea40628 673001a3415847089b70326ad9c6a1ce
msgid "Expandable setting card"
msgstr "手风琴设置卡"

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:21:<autosummary>:1
#: 95c1736c542f476ca2376b4ccc85d014
msgid ""
":py:obj:`GroupSeparator "
"<qfluentwidgets.components.settings.expand_setting_card.GroupSeparator>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:97
#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:21:<autosummary>:1
#: c1d318b1218e486fb91217011b97a4a4 fc0e8cfbfd094b148277cdc8bc17d399
msgid "group separator"
msgstr "组分隔符"

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:21:<autosummary>:1
#: cb6f9bdd3a3f4ad0924d977de7ec9ff9
msgid ""
":py:obj:`ExpandGroupSettingCard "
"<qfluentwidgets.components.settings.expand_setting_card.ExpandGroupSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:107
#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:21:<autosummary>:1
#: 6f9a4bd581fb4a9db3dd1f0c72be2757 d3c4a0edc8a9414da457d7a73307a616
msgid "Expand group setting card"
msgstr "分组手风琴设置卡"

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:24
#: f3d42a48bcb8427cba3bba6c96ba6b42
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QAbstractButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:62
#: 00c1c2ca48d94325a7a5e50a3c7e3e43
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QFrame`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:68
#: d7b6c8af93f74527815e253734dde1b4
msgid "add widget to tail"
msgstr "添加小部件到末尾"

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:73
#: 587062895e724b3eb194a66003784231
msgid "set the expand status of card"
msgstr "设置卡片的展开状态"

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:78
#: 033388c0282841e3aaaf107e83838d1c
msgid "toggle expand status"
msgstr "切换卡片的展开状态"

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:89
#: 11a37a106dfe4fcc90aec2bc63297ea0
msgid "set the value of config item"
msgstr "设置配置项的值"

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:95
#: 9b75de2c44c74497822b6974ee0c761f
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:105
#: 2f1d5ad400cc40558dd8f8173ee2d179
msgid "Bases: :py:obj:`ExpandSettingCard`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/settings/expand_setting_card/index.rst:111
#: 3fc877ca1c904afba52a10621c8ed6cc
msgid "add widget to group"
msgstr "添加小部件到组中"

