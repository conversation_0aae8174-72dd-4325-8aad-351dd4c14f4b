#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI修复 - 验证filedialog参数问题已解决
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import warnings

# 抑制字体相关警告
warnings.filterwarnings("ignore", message=".*FontBBox.*")
warnings.filterwarnings("ignore", message=".*font descriptor.*")

def test_file_dialogs():
    """测试文件对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    print("=== 测试文件对话框 ===")
    
    try:
        # 测试打开文件对话框
        print("1. 测试打开文件对话框...")
        file_path = filedialog.askopenfilename(
            title="测试选择PDF文件",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        
        if file_path:
            print(f"✅ 选择的文件: {file_path}")
        else:
            print("⚠️ 未选择文件")
        
        # 测试保存文件对话框
        print("2. 测试保存文件对话框...")
        save_path = filedialog.asksaveasfilename(
            title="测试保存PDF文件",
            defaultextension=".pdf",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")],
            initialfile="test_document.pdf"  # 使用正确的参数名
        )
        
        if save_path:
            print(f"✅ 保存路径: {save_path}")
        else:
            print("⚠️ 未选择保存路径")
        
        print("✅ 文件对话框测试完成，没有参数错误")
        
    except Exception as e:
        print(f"❌ 文件对话框测试失败: {e}")
        return False
    
    finally:
        root.destroy()
    
    return True

def test_imports():
    """测试导入"""
    print("\n=== 测试模块导入 ===")
    
    try:
        print("1. 测试tkinter...")
        import tkinter as tk
        from tkinter import ttk, filedialog, messagebox, scrolledtext
        print("✅ tkinter - OK")
        
        print("2. 测试PDF处理库...")
        try:
            import PyPDF2
            print("✅ PyPDF2 - OK")
        except ImportError:
            print("❌ PyPDF2 - 未安装")
        
        try:
            import pdfplumber
            print("✅ pdfplumber - OK")
        except ImportError:
            print("❌ pdfplumber - 未安装")
        
        try:
            import reportlab
            print("✅ reportlab - OK")
        except ImportError:
            print("❌ reportlab - 未安装")
        
        print("3. 测试CustomTkinter...")
        try:
            import customtkinter
            print("✅ customtkinter - OK")
        except ImportError:
            print("⚠️ customtkinter - 未安装 (可选)")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def test_gui_syntax():
    """测试GUI代码语法"""
    print("\n=== 测试GUI代码语法 ===")
    
    try:
        print("1. 测试tkinter版本语法...")
        import pdf_editor_tkinter
        print("✅ pdf_editor_tkinter - 语法正确")
        
        print("2. 测试CustomTkinter版本语法...")
        try:
            import pdf_editor_customtkinter
            print("✅ pdf_editor_customtkinter - 语法正确")
        except ImportError as e:
            if "customtkinter" in str(e):
                print("⚠️ pdf_editor_customtkinter - CustomTkinter未安装")
            else:
                print(f"❌ pdf_editor_customtkinter - 语法错误: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 语法测试失败: {e}")
        return False

def main():
    print("PDF编辑工具GUI修复测试")
    print("=" * 40)
    
    # 测试导入
    import_ok = test_imports()
    
    # 测试语法
    syntax_ok = test_gui_syntax()
    
    # 测试文件对话框
    dialog_ok = test_file_dialogs()
    
    print("\n" + "=" * 40)
    print("测试结果总结:")
    print(f"导入测试: {'✅ 通过' if import_ok else '❌ 失败'}")
    print(f"语法测试: {'✅ 通过' if syntax_ok else '❌ 失败'}")
    print(f"对话框测试: {'✅ 通过' if dialog_ok else '❌ 失败'}")
    
    if import_ok and syntax_ok and dialog_ok:
        print("\n🎉 所有测试通过！GUI工具可以正常使用")
        print("\n推荐运行:")
        print("python install_and_run_gui.py")
    else:
        print("\n⚠️ 部分测试失败，请检查依赖库安装")
        print("运行: pip install PyPDF2 pdfplumber reportlab")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n测试已取消")
    except Exception as e:
        print(f"\n测试出错: {e}")
    
    input("\n按回车键退出...")
