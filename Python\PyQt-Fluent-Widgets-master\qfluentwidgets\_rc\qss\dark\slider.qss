QSlider:horizontal {
    min-height: 24px;
}

QSlider::groove:horizontal {
    height: 4px;
    background-color: rgb(158, 159, 159);
    border-radius: 2px;
}

QSlider::sub-page:horizontal {
    background: --ThemeColorPrimary;
    height: 4px;
    border-radius: 2px;
}

QSlider::handle:horizontal {
    border: 1px solid rgb(58, 58, 58);
    width: 21px;
    min-height: 22px;
    margin: -9px 0;
    border-radius: 11px;
    background-color: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
        stop:0 --ThemeColorPrimary,
        stop:0.5 --ThemeColorPrimary,
        stop:0.6 rgb(69, 69, 69),
        stop:1 rgb(69, 69, 69));
}

QSlider::handle:horizontal:hover {
    background-color: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
        stop:0 --ThemeColorPrimary,
        stop:0.6 --ThemeColorPrimary,
        stop:0.7 rgb(69, 69, 69),
        stop:1 rgb(69, 69, 69));
}

QSlider::handle:horizontal:pressed {
    background-color: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
        stop:0 --ThemeColorPrimary,
        stop:0.4 --ThemeColorPrimary,
        stop:0.5 rgb(69, 69, 69),
        stop:1 rgb(69, 69, 69));
}


QSlider:vertical {
    min-width: 24px;
}

QSlider::groove:vertical {
    width: 4px;
    background-color: rgb(158, 159, 159);
    border-radius: 2px;
}

QSlider::add-page:vertical {
    background: --ThemeColorPrimary;
    width: 4px;
    border-radius: 2px;
}

QSlider::handle:vertical {
    border: 1px solid rgb(58, 58, 58);
    height: 20px;
    min-width: 22px;
    margin: 0 -9px;
    border-radius: 11px;
    background-color: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
            stop:0 --ThemeColorPrimary,
            stop:0.5 --ThemeColorPrimary,
            stop:0.6 rgb(69, 69, 69),
            stop:1 rgb(69, 69, 69));
}

QSlider::handle:vertical:hover {
    background-color: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
            stop:0 --ThemeColorPrimary,
            stop:0.6 --ThemeColorPrimary,
            stop:0.7 rgb(69, 69, 69),
            stop:1 rgb(69, 69, 69));
}

QSlider::handle:vertical:pressed {
    background-color: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
            stop:0 --ThemeColorPrimary,
            stop:0.4 --ThemeColorPrimary,
            stop:0.5 rgb(69, 69, 69),
            stop:1 rgb(69, 69, 69));
}