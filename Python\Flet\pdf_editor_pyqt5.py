#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文本替换编辑工具 - PyQt5版本
基于PyQt5框架开发的GUI应用程序，支持PDF文件的文本查找和替换功能
"""

import sys
import os
from pathlib import Path

try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                                QHBoxLayout, QGridLayout, QLabel, QPushButton, 
                                QTextEdit, QFileDialog, QMessageBox, QGroupBox,
                                QStatusBar, QSplitter, QFrame)
    from PyQt5.QtCore import Qt, QThread, pyqtSignal
    from PyQt5.QtGui import QFont, QIcon, QPalette, QColor
except ImportError:
    print("请安装PyQt5: pip install PyQt5")
    sys.exit(1)

try:
    import PyPDF2
    import pdfplumber
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.lib.styles import getSampleStyleSheet
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.units import inch
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    print("请安装: pip install PyPDF2 pdfplumber reportlab")


class PDFTextEditor:
    def __init__(self):
        self.current_pdf_path = None
        self.pdf_text = ""
        self.modified_text = ""
        self.replacement_pairs = []
        
    def extract_text_from_pdf(self, pdf_path):
        """从PDF文件中提取文本"""
        try:
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            return text
        except Exception as e:
            raise Exception(f"PDF文本提取失败: {str(e)}")
    
    def replace_text(self, original_text, find_text, replace_text):
        """执行文本替换"""
        if not find_text:
            return original_text
        return original_text.replace(find_text, replace_text)
    
    def create_pdf_from_text(self, text, output_path):
        """从文本创建PDF文件"""
        try:
            doc = SimpleDocTemplate(output_path, pagesize=A4)
            
            # 尝试注册中文字体
            try:
                font_paths = [
                    "C:/Windows/Fonts/simsun.ttc",
                    "C:/Windows/Fonts/msyh.ttc",
                    "C:/Windows/Fonts/simhei.ttf"
                ]
                
                font_registered = False
                for font_path in font_paths:
                    if os.path.exists(font_path):
                        try:
                            pdfmetrics.registerFont(TTFont('Chinese', font_path))
                            font_registered = True
                            break
                        except:
                            continue
                
                font_name = 'Chinese' if font_registered else 'Helvetica'
                    
            except Exception:
                font_name = 'Helvetica'
            
            # 创建样式
            styles = getSampleStyleSheet()
            normal_style = styles['Normal']
            normal_style.fontName = font_name
            normal_style.fontSize = 12
            normal_style.leading = 14
            
            # 将文本分段并创建段落
            story = []
            paragraphs = text.split('\n')
            
            for para_text in paragraphs:
                if para_text.strip():
                    try:
                        para = Paragraph(para_text, normal_style)
                        story.append(para)
                    except Exception:
                        para = Paragraph(para_text.encode('ascii', 'ignore').decode(), normal_style)
                        story.append(para)
                else:
                    story.append(Spacer(1, 0.2*inch))
            
            doc.build(story)
            return True
            
        except Exception as e:
            raise Exception(f"PDF创建失败: {str(e)}")


class PDFProcessThread(QThread):
    """PDF处理线程"""
    finished = pyqtSignal(str)
    error = pyqtSignal(str)
    
    def __init__(self, pdf_editor, action, *args):
        super().__init__()
        self.pdf_editor = pdf_editor
        self.action = action
        self.args = args
    
    def run(self):
        try:
            if self.action == "extract":
                result = self.pdf_editor.extract_text_from_pdf(self.args[0])
                self.finished.emit(result)
            elif self.action == "save":
                self.pdf_editor.create_pdf_from_text(self.args[0], self.args[1])
                self.finished.emit(f"PDF已保存至: {Path(self.args[1]).name}")
        except Exception as e:
            self.error.emit(str(e))


class PDFEditorMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.pdf_editor = PDFTextEditor()
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("PDF文本替换编辑工具")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(800, 600)
        
        # 设置应用样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                text-align: center;
                font-size: 14px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px;
                font-family: 'Consolas', monospace;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题
        title_label = QLabel("PDF文本替换编辑工具")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #333; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # 文件导入区域
        import_group = QGroupBox("📁 文件导入")
        import_layout = QHBoxLayout(import_group)
        
        self.select_button = QPushButton("选择PDF文件")
        self.select_button.clicked.connect(self.select_file)
        self.select_button.setStyleSheet("QPushButton { background-color: #2196F3; }")
        import_layout.addWidget(self.select_button)
        
        self.file_path_label = QLabel("未选择文件")
        self.file_path_label.setStyleSheet("color: #666; font-style: italic;")
        import_layout.addWidget(self.file_path_label)
        import_layout.addStretch()
        
        main_layout.addWidget(import_group)
        
        # 主要内容区域
        content_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：预览区域
        preview_group = QGroupBox("👀 内容预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setPlaceholderText("PDF内容预览将在这里显示...")
        preview_layout.addWidget(self.preview_text)
        
        content_splitter.addWidget(preview_group)
        
        # 右侧：修改区域
        edit_group = QGroupBox("🔄 文本替换")
        edit_layout = QVBoxLayout(edit_group)
        
        # 原文输入
        edit_layout.addWidget(QLabel("原文:"))
        self.find_text = QTextEdit()
        self.find_text.setMaximumHeight(120)
        self.find_text.setPlaceholderText("输入需要替换的原始文字")
        edit_layout.addWidget(self.find_text)
        
        # 替换文本输入
        edit_layout.addWidget(QLabel("变更为:"))
        self.replace_text = QTextEdit()
        self.replace_text.setMaximumHeight(120)
        self.replace_text.setPlaceholderText("输入替换后的新文字")
        edit_layout.addWidget(self.replace_text)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.replace_button = QPushButton("执行替换")
        self.replace_button.clicked.connect(self.replace_text_action)
        self.replace_button.setStyleSheet("QPushButton { background-color: #FF9800; }")
        button_layout.addWidget(self.replace_button)
        
        self.save_button = QPushButton("保存PDF")
        self.save_button.clicked.connect(self.save_pdf)
        self.save_button.setStyleSheet("QPushButton { background-color: #4CAF50; }")
        button_layout.addWidget(self.save_button)
        
        edit_layout.addLayout(button_layout)
        edit_layout.addStretch()
        
        content_splitter.addWidget(edit_group)
        content_splitter.setSizes([700, 500])
        
        main_layout.addWidget(content_splitter)
        
        # 状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
        self.status_bar.setStyleSheet("QStatusBar { background-color: #e0e0e0; }")
        
    def update_status(self, message, color="black"):
        """更新状态信息"""
        self.status_bar.showMessage(message)
        self.status_bar.setStyleSheet(f"QStatusBar {{ background-color: #e0e0e0; color: {color}; }}")
        
    def select_file(self):
        """选择PDF文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择PDF文件", "", "PDF文件 (*.pdf);;所有文件 (*)"
        )
        
        if file_path:
            self.pdf_editor.current_pdf_path = file_path
            self.file_path_label.setText(f"已选择: {Path(file_path).name}")
            self.file_path_label.setStyleSheet("color: black;")
            
            # 在后台线程中提取PDF文本
            self.update_status("正在提取PDF文本...", "blue")
            self.select_button.setEnabled(False)
            
            self.pdf_thread = PDFProcessThread(self.pdf_editor, "extract", file_path)
            self.pdf_thread.finished.connect(self.on_pdf_extracted)
            self.pdf_thread.error.connect(self.on_error)
            self.pdf_thread.start()
    
    def on_pdf_extracted(self, text):
        """PDF文本提取完成"""
        self.pdf_editor.pdf_text = text
        self.pdf_editor.modified_text = text
        
        self.preview_text.setPlainText(text)
        self.update_status(f"PDF加载成功，共提取 {len(text)} 个字符", "green")
        self.select_button.setEnabled(True)
    
    def on_error(self, error_message):
        """处理错误"""
        self.update_status(f"错误: {error_message}", "red")
        QMessageBox.critical(self, "错误", error_message)
        self.select_button.setEnabled(True)
    
    def replace_text_action(self):
        """执行文本替换"""
        if not self.pdf_editor.current_pdf_path:
            self.update_status("请先选择PDF文件", "red")
            QMessageBox.warning(self, "警告", "请先选择PDF文件")
            return
        
        find_text = self.find_text.toPlainText().strip()
        replace_text = self.replace_text.toPlainText().strip()
        
        if not find_text:
            self.update_status("请输入要替换的原文", "red")
            QMessageBox.warning(self, "警告", "请输入要替换的原文")
            return
        
        # 执行替换
        self.update_status("正在执行文本替换...", "blue")
        
        self.pdf_editor.modified_text = self.pdf_editor.replace_text(
            self.pdf_editor.modified_text, find_text, replace_text
        )
        
        # 更新预览
        self.preview_text.setPlainText(self.pdf_editor.modified_text)
        
        # 记录替换操作
        replacement_info = f"'{find_text}' → '{replace_text}'"
        if replacement_info not in [pair[2] for pair in self.pdf_editor.replacement_pairs]:
            self.pdf_editor.replacement_pairs.append((find_text, replace_text, replacement_info))
        
        self.update_status(f"替换完成: {replacement_info}", "green")
    
    def save_pdf(self):
        """保存PDF文件"""
        if not self.pdf_editor.modified_text:
            self.update_status("没有可保存的内容", "red")
            QMessageBox.warning(self, "警告", "没有可保存的内容")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存PDF文件", "modified_document.pdf", "PDF文件 (*.pdf);;所有文件 (*)"
        )
        
        if file_path:
            self.update_status("正在生成PDF文件...", "blue")
            self.save_button.setEnabled(False)
            
            self.save_thread = PDFProcessThread(
                self.pdf_editor, "save", self.pdf_editor.modified_text, file_path
            )
            self.save_thread.finished.connect(self.on_pdf_saved)
            self.save_thread.error.connect(self.on_save_error)
            self.save_thread.start()
    
    def on_pdf_saved(self, message):
        """PDF保存完成"""
        self.update_status(message, "green")
        QMessageBox.information(self, "成功", "PDF文件保存成功！")
        self.save_button.setEnabled(True)
    
    def on_save_error(self, error_message):
        """保存错误处理"""
        self.update_status(f"保存失败: {error_message}", "red")
        QMessageBox.critical(self, "错误", f"PDF保存失败:\n{error_message}")
        self.save_button.setEnabled(True)


def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用现代化样式
    
    window = PDFEditorMainWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
