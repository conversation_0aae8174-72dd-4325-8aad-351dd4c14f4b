import flet as ft
import scapy.all as scapy
import threading
import time
import netifaces
import socket
import requests
import json

class NetworkScanner:
    def __init__(self, page: ft.Page):
        self.page = page
        self.page.title = "网络扫描器"
        self.page.window_width = 800
        self.page.window_height = 600
        self.page.padding = 20
        self.scanning = False
        
        # 创建UI组件
        self.start_btn = ft.ElevatedButton("开始扫描", on_click=self.start_scan)
        self.stop_btn = ft.ElevatedButton("停止扫描", on_click=self.stop_scan, disabled=True)
        self.progress = ft.ProgressRing(width=20, height=20, visible=False)
        self.result_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("IP地址")),
                ft.DataColumn(ft.Text("MAC地址")),
                ft.DataColumn(ft.Text("设备厂商")),
                ft.DataColumn(ft.Text("主机名")),
                ft.DataColumn(ft.Text("状态"))
            ],
            rows=[],
            border=ft.border.all(1, "gray"),
            border_radius=10,
            vertical_lines=ft.border.BorderSide(1, "gray"),
            horizontal_lines=ft.border.BorderSide(1, "gray"),
        )

        # 布局
        self.page.add(
            ft.Container(
                content=ft.Column([
                    ft.Row(
                        [self.start_btn, self.stop_btn, self.progress],
                        alignment=ft.MainAxisAlignment.START,
                    ),
                    ft.Container(height=20),  # 间距
                    self.result_table
                ]),
                padding=10,
            )
        )

    def get_network_range(self):
        """获取当前网络的IP范围"""
        gateways = netifaces.gateways()
        if 'default' in gateways and netifaces.AF_INET in gateways['default']:
            gateway_ip = gateways['default'][netifaces.AF_INET][0]
            return f"{'.'.join(gateway_ip.split('.')[:3])}.0/24"
        return None

    def get_manufacturer(self, mac):
        """通过MAC地址获取设备制造商信息"""
        try:
            url = f"https://api.macvendors.com/{mac}"
            response = requests.get(url)
            if response.status_code == 200:
                return response.text
            return "未知"
        except:
            return "未知"

    def scan_network(self):
        """执行网络扫描"""
        network_range = self.get_network_range()
        if not network_range:
            return
        
        # 创建ARP请求包
        arp_request = scapy.ARP(pdst=network_range)
        broadcast = scapy.Ether(dst="ff:ff:ff:ff:ff:ff")
        arp_request_broadcast = broadcast/arp_request

        while self.scanning:
            # 发送ARP请求并接收响应
            answered_list = scapy.srp(arp_request_broadcast, timeout=1, verbose=False)[0]
            
            # 清空现有结果
            self.result_table.rows.clear()
            
            # 处理扫描结果
            for element in answered_list:
                ip = element[1].psrc
                mac = element[1].hwsrc.upper()
                
                # 获取主机名
                try:
                    hostname = socket.gethostbyaddr(ip)[0]
                except:
                    hostname = "未知"
                
                # 获取制造商信息
                manufacturer = self.get_manufacturer(mac)
                
                # 添加到结果表格
                self.result_table.rows.append(
                    ft.DataRow(cells=[
                        ft.DataCell(ft.Text(ip)),
                        ft.DataCell(ft.Text(mac)),
                        ft.DataCell(ft.Text(manufacturer)),
                        ft.DataCell(ft.Text(hostname)),
                        ft.DataCell(ft.Text("在线"))
                    ])
                )
            
            self.page.update()
            time.sleep(5)  # 每5秒刷新一次

    def start_scan(self, e):
        """开始扫描"""
        self.scanning = True
        self.start_btn.disabled = True
        self.stop_btn.disabled = False
        self.progress.visible = True
        self.page.update()
        
        # 在新线程中运行扫描
        threading.Thread(target=self.scan_network, daemon=True).start()

    def stop_scan(self, e):
        """停止扫描"""
        self.scanning = False
        self.start_btn.disabled = False
        self.stop_btn.disabled = True
        self.progress.visible = False
        self.page.update()

def main(page: ft.Page):
    page.window_center()  # 窗口居中
    NetworkScanner(page)

if __name__ == "__main__":
    ft.app(target=main)