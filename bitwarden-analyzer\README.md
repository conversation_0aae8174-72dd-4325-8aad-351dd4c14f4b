# Bitwarden 数据筛查工具

一个用于分析和筛查 Bitwarden 密码管理器数据的工具，帮助用户识别无效链接、重复域名和重复密码。

## 🆕 Python版本特性

- **状态保存**: 支持分析状态保存和恢复，刷新页面不会丢失数据
- **数据持久化**: 使用SQLite数据库存储任务和结果
- **后台运行**: Python后端处理，支持长时间运行
- **并发测试**: 多线程并发测试，提高分析速度
- **暂停恢复**: 支持暂停和恢复分析任务
- **历史记录**: 保存所有分析历史，可随时查看

## 🚀 快速开始

### 方法一：一键启动（推荐）

**Windows用户**：
- 双击 `启动工具.bat` 文件

**其他系统**：
```bash
python start.py
```

### 方法二：手动安装

1. **安装依赖**
   ```bash
   python install.py
   ```

2. **启动服务**
   ```bash
   python run.py
   # 或者
   python start.py
   ```

3. **访问工具**
   - 自动打开浏览器访问 http://localhost:5000
   - 或手动打开浏览器访问该地址

### 故障排除

#### 问题：ModuleNotFoundError: No module named 'flask_cors'
**解决方案**：
```bash
# 方法1：使用简化启动
python start.py

# 方法2：手动安装依赖
pip install Flask Flask-CORS requests

# 方法3：重新运行安装脚本
python install.py
```

#### 问题：端口5000被占用
**解决方案**：
- 关闭占用端口的程序
- 或修改 `app.py` 中的端口号

#### 问题：Python版本过低
**解决方案**：
- 升级到Python 3.7或更高版本

### 使用步骤

1. **上传数据文件** - 支持Bitwarden导出的JSON、CSV、TXT格式
2. **配置设置** - 选择是否跳过内网地址等选项
3. **开始分析** - 点击开始分析，后台自动处理
4. **查看结果** - 实时查看分析进度和结果
5. **导出报告** - 分析完成后可导出详细报告

## 📁 项目结构

```
bitwarden-analyzer/
├── app.py              # Flask后端应用
├── run.py              # 启动脚本
├── install.py          # 安装脚本
├── requirements.txt    # Python依赖
├── templates/          # HTML模板
│   └── index.html
├── static/             # 静态文件
│   └── app.js
├── uploads/            # 上传文件存储
├── results/            # 导出结果存储
├── bitwarden_analysis.db  # SQLite数据库
└── 示例数据文件...
```

## 功能特性

### 🔍 链接有效性检测
- 自动测试每个保存的网站链接是否可访问
- 支持手动跳过、标记无效网站、标记内网网站
- 实时显示测试进度和结果

### 🔄 重复项检测
- 检测重复的域名
- 检测重复的密码
- 生成详细的重复项报告

### 📊 数据分析报告
- 生成完整的分析摘要
- 提供清理建议
- 支持导出 JSON 和 CSV 格式报告

### 🎨 用户友好界面
- 现代化的响应式设计
- 拖拽上传文件支持
- 实时进度显示
- 动态结果展示

## 使用方法

### 1. 准备数据文件
从 Bitwarden 导出数据，支持以下格式：
- **JSON 格式**：Bitwarden 标准导出格式
- **CSV 格式**：包含 name, url, username, password 列
- **TXT 格式**：包含 URL 的纯文本文件

### 2. 上传文件
- 点击上传区域选择文件
- 或直接拖拽文件到上传区域
- 工具会自动解析数据并显示统计信息

### 3. 开始分析
- 点击"开始分析"按钮
- 工具会逐个测试每个链接的可访问性
- 可以随时暂停、跳过或手动标记网站

### 4. 查看结果
- 右侧面板实时显示测试结果
- 分析完成后查看详细报告
- 导出分析结果用于后续处理

## 手动操作选项

在测试过程中，您可以对当前测试的网站进行以下操作：

- **⏭️ 跳过**：跳过当前网站的测试
- **❌ 标记无效**：将网站标记为无效，建议删除
- **🏠 标记内网**：将网站标记为内网网站，单独管理

## 文件格式说明

### Bitwarden JSON 格式
```json
{
  "items": [
    {
      "name": "网站名称",
      "login": {
        "username": "用户名",
        "password": "密码",
        "uris": [
          {
            "uri": "https://example.com"
          }
        ]
      }
    }
  ]
}
```

### CSV 格式
```csv
name,url,username,password
网站名称,https://example.com,用户名,密码
```

### TXT 格式
```
https://example.com
https://another-site.com
```

## 导出报告

分析完成后，可以导出以下格式的报告：

- **JSON 报告**：包含完整的分析数据、统计信息和建议
- **CSV 报告**：表格格式，便于在 Excel 中查看和处理

## 安全说明

- 所有数据处理都在本地浏览器中进行
- 不会将您的密码数据发送到任何服务器
- 链接测试使用 HEAD 请求，不会泄露登录信息
- 建议在分析完成后及时删除上传的数据文件

## 技术特性

- 纯前端实现，无需服务器
- 支持现代浏览器
- 响应式设计，支持移动设备
- 使用 Fetch API 进行链接测试
- 支持 CORS 和超时处理

## 使用建议

1. **备份数据**：在进行任何清理操作前，请备份您的 Bitwarden 数据
2. **分批处理**：如果数据量很大，建议分批上传和分析
3. **网络环境**：确保网络连接稳定，某些网站可能因为防火墙或地理限制无法访问
4. **内网网站**：对于公司内网网站，建议手动标记而不是删除

## 示例数据

项目包含 `sample-data.json` 文件，您可以使用它来测试工具的功能。

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 故障排除

### 链接测试失败
- 某些网站可能阻止 HEAD 请求
- CORS 策略可能阻止跨域请求
- 网络超时或连接问题

### 文件解析失败
- 检查文件格式是否正确
- 确保 JSON 文件语法正确
- CSV 文件应包含正确的列标题

如有问题，请检查浏览器控制台的错误信息。
