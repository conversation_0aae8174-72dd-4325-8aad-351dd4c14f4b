SpinBox,
DoubleSpinBox,
DateEdit,
DateTimeEdit,
TimeEdit {
    background-color: rgba(255, 255, 255, 0.0605);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-bottom: 1px solid rgba(255, 255, 255, 0.5442);
    border-radius: 5px;
    /* font: 14px "Segoe UI", "Microsoft YaHei"; */
    padding: 0px 80px 0 10px;
    color: white;
    selection-background-color: --ThemeColorPrimary;
    selection-color: black;
}

SpinBox:hover,
DoubleSpinBox:hover,
DateEdit:hover,
DateTimeEdit:hover,
TimeEdit:hover {
    background-color: rgba(255, 255, 255, 0.0837);
}

SpinBox:focus,
DoubleSpinBox:focus,
DateEdit:focus,
DateTimeEdit:focus,
TimeEdit:focus{
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    background-color: rgba(30, 30, 30, 0.7);
}

SpinBox:disabled,
DoubleSpinBox:disabled,
DateEdit:disabled,
DateTimeEdit:disabled,
TimeEdit:disabled{
    color: rgba(255, 255, 255, 150);
    background-color: rgba(255, 255, 255, 0.0837);
    border: 1px solid gba(255, 255, 255, 0.0698);
}

SpinButton {
    background-color: transparent;
    border-radius: 4px;
    margin: 0;
}

SpinButton:hover {
    background-color: rgba(255, 255, 255, 9);
}

SpinButton:pressed {
    background-color: rgba(255, 255, 255, 6);
}