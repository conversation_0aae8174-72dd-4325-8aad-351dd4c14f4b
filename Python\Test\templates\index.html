<!DOCTYPE html>
<html>
<head>
    <title>运城地图</title>
    <style>
        .container {
            display: flex;
            height: 100vh;
        }
        .map-container {
            flex: 2;
            height: 100%;
        }
        .info-panel {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
            overflow-y: auto;
        }
        .district-info {
            margin-bottom: 15px;
            padding: 10px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="map-container">
            <iframe src="/map" width="100%" height="100%" frameborder="0"></iframe>
        </div>
        <div class="info-panel">
            <h2>区县信息</h2>
            {% for district, data in district_data.items() %}
            <div class="district-info">
                <h3>{{ district }}</h3>
                <p>用户数：{{ data['用户数'] }}</p>
                <p>终端数：{{ data['终端数'] }}</p>
            </div>
            {% endfor %}
        </div>
    </div>
</body>
</html>