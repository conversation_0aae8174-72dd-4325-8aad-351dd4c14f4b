#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试PDF编辑工具的颜色问题
"""

import flet as ft

def main(page: ft.Page):
    page.title = "颜色测试"
    page.window_width = 400
    page.window_height = 300
    
    def close_app(e):
        page.window_close()
    
    # 测试所有用到的颜色
    page.add(
        ft.Column([
            ft.Text("颜色测试", size=20, weight=ft.FontWeight.BOLD),
            ft.Text("GREY_600", color=ft.Colors.GREY_600),
            ft.Text("GREEN", color=ft.Colors.GREEN),
            ft.Text("BLUE", color=ft.Colors.BLUE),
            ft.Text("RED", color=ft.Colors.RED),
            ft.Text("WHITE (背景黑色)", color=ft.Colors.WHITE, bgcolor=ft.Colors.BLACK),
            ft.Container(
                content=ft.Text("GREY_100背景", color=ft.Colors.BLACK),
                bgcolor=ft.Colors.GREY_100,
                padding=10,
                border_radius=5
            ),
            ft.ElevatedButton("关闭", on_click=close_app),
            ft.Text("✅ 如果您能看到所有颜色，说明颜色问题已解决！", color=ft.Colors.GREEN)
        ], 
        spacing=10,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER
        )
    )

if __name__ == "__main__":
    print("启动颜色测试...")
    try:
        ft.app(target=main)
        print("✅ 颜色测试完成")
    except Exception as e:
        print(f"❌ 颜色测试失败: {e}")
