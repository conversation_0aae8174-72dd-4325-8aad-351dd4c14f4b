import flet as ft
import requests
import json
import asyncio
from typing import Dict, Any, Optional
import logging
import os
import subprocess
import platform
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MCPTool:
    """MCP工具基类"""
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description

    async def execute(self, **kwargs) -> Dict[str, Any]:
        """执行工具"""
        raise NotImplementedError

class FetchTool(MCPTool):
    """Fetch工具实现"""
    def __init__(self):
        super().__init__("fetch", "获取网络资源")

    async def execute(self, url: str, method: str = "GET", headers: Dict = None, data: Any = None) -> Dict[str, Any]:
        """执行fetch请求"""
        try:
            response = requests.request(
                method=method,
                url=url,
                headers=headers or {},
                json=data if data else None,
                timeout=30
            )

            result = {
                "success": True,
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "content": response.text,
                "url": url,
                "method": method
            }

            # 尝试解析JSON
            try:
                result["json"] = response.json()
            except:
                result["json"] = None

            return result
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "url": url,
                "method": method
            }

class ShellTool(MCPTool):
    """Shell命令工具"""
    def __init__(self):
        super().__init__("shell", "执行系统命令")

    async def execute(self, command: str, cwd: str = None) -> Dict[str, Any]:
        """执行shell命令"""
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                cwd=cwd,
                timeout=30
            )

            return {
                "success": True,
                "command": command,
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "cwd": cwd or os.getcwd()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "command": command
            }

class FileSystemTool(MCPTool):
    """文件系统工具"""
    def __init__(self):
        super().__init__("filesystem", "文件系统操作")

    async def execute(self, action: str, path: str, content: str = None) -> Dict[str, Any]:
        """执行文件系统操作"""
        try:
            if action == "read":
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return {
                    "success": True,
                    "action": action,
                    "path": path,
                    "content": content,
                    "size": len(content)
                }
            elif action == "write":
                with open(path, 'w', encoding='utf-8') as f:
                    f.write(content or "")
                return {
                    "success": True,
                    "action": action,
                    "path": path,
                    "bytes_written": len(content or "")
                }
            elif action == "list":
                items = os.listdir(path)
                return {
                    "success": True,
                    "action": action,
                    "path": path,
                    "items": items,
                    "count": len(items)
                }
            else:
                return {
                    "success": False,
                    "error": f"不支持的操作: {action}"
                }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "action": action,
                "path": path
            }

class MCPFetchDemo:
    def __init__(self, page: ft.Page):
        self.page = page
        self.page.title = "MCP Fetch工具演示"
        self.page.theme_mode = ft.ThemeMode.LIGHT
        self.page.window_width = 1400
        self.page.window_height = 800
        self.page.padding = 20

        # 初始化Fetch工具
        self.fetch_tool = FetchTool()

        # API配置
        self.api_url = ""
        self.api_key = ""

        # UI组件
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        title = ft.Text(
            "MCP工具演示平台",
            size=24,
            weight=ft.FontWeight.BOLD,
            color=ft.Colors.BLUE_700
        )

        # API配置区域
        self.api_url_field = ft.TextField(
            label="LLM API URL",
            hint_text="例如: https://api.openai.com/v1/chat/completions",
            width=400,
            value="https://api.openai.com/v1/chat/completions"
        )

        self.api_key_field = ft.TextField(
            label="API Key",
            hint_text="输入你的API密钥",
            password=True,
            width=400
        )

        # 工具选择
        self.tool_selector = ft.Dropdown(
            label="选择MCP工具",
            width=200,
            value="fetch",
            options=[
                ft.dropdown.Option("fetch", "Fetch - 网络请求"),
                ft.dropdown.Option("shell", "Shell - 系统命令"),
                ft.dropdown.Option("filesystem", "FileSystem - 文件操作"),
            ],
            on_change=self.on_tool_change
        )

        # 工具配置容器
        self.tool_config_container = ft.Container(
            content=self.create_fetch_config(),
            padding=10,
            border=ft.border.all(1, ft.Colors.GREY_300),
            border_radius=5
        )
        
        # 按钮
        self.execute_button = ft.ElevatedButton(
            "直接执行工具",
            on_click=self.execute_tool,
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE
        )

        self.mcp_button = ft.ElevatedButton(
            "通过MCP调用",
            on_click=self.call_via_mcp,
            bgcolor=ft.Colors.GREEN_600,
            color=ft.Colors.WHITE
        )

        # 结果显示区域
        self.result_text = ft.TextField(
            label="执行结果",
            multiline=True,
            min_lines=12,
            max_lines=20,
            width=1100,
            read_only=True
        )

        # 状态显示
        self.status_text = ft.Text(
            "就绪 - 请选择工具并配置参数",
            color=ft.Colors.GREEN_600,
            size=14
        )

        # 左侧配置面板
        left_panel = ft.Container(
            content=ft.Column([
                title,
                ft.Divider(),

                # API配置
                ft.Text("LLM API 配置", size=16, weight=ft.FontWeight.BOLD),
                self.api_url_field,
                self.api_key_field,
                ft.Divider(),

                # 工具选择和配置
                ft.Row([
                    ft.Text("工具选择", size=16, weight=ft.FontWeight.BOLD),
                    self.tool_selector
                ]),
                self.tool_config_container,

                # 按钮
                ft.Column([
                    self.execute_button,
                    self.mcp_button
                ], spacing=10),

            ], spacing=10, scroll=ft.ScrollMode.AUTO),
            width=500,
            padding=20
        )

        # 右侧结果面板
        right_panel = ft.Container(
            content=ft.Column([
                ft.Text("执行结果", size=18, weight=ft.FontWeight.BOLD),
                self.status_text,
                self.result_text,
            ], spacing=10),
            width=650,
            padding=20
        )

        # 主布局 - 左右分栏
        self.page.add(
            ft.Row([
                left_panel,
                ft.VerticalDivider(width=1),
                right_panel
            ], spacing=0, expand=True)
        )

    def on_tool_change(self, e):
        """工具选择改变时的回调"""
        self.current_tool = e.control.value

        if self.current_tool == "fetch":
            self.tool_config_container.content = self.create_fetch_config()
        elif self.current_tool == "shell":
            self.tool_config_container.content = self.create_shell_config()
        elif self.current_tool == "filesystem":
            self.tool_config_container.content = self.create_filesystem_config()

        self.page.update()

    def create_fetch_config(self):
        """创建Fetch工具配置界面"""
        self.fetch_url_field = ft.TextField(
            label="URL",
            hint_text="例如: https://api.github.com/users/octocat",
            width=500,
            value="https://httpbin.org/json"
        )

        self.method_dropdown = ft.Dropdown(
            label="HTTP方法",
            width=150,
            value="GET",
            options=[
                ft.dropdown.Option("GET"),
                ft.dropdown.Option("POST"),
                ft.dropdown.Option("PUT"),
                ft.dropdown.Option("DELETE"),
            ]
        )

        self.headers_field = ft.TextField(
            label="请求头 (JSON格式)",
            hint_text='{"Content-Type": "application/json"}',
            multiline=True,
            min_lines=2,
            max_lines=3,
            width=500
        )

        self.body_field = ft.TextField(
            label="请求体 (JSON格式)",
            hint_text='{"key": "value"}',
            multiline=True,
            min_lines=3,
            max_lines=4,
            width=500
        )

        return ft.Column([
            ft.Text("Fetch工具配置", size=16, weight=ft.FontWeight.BOLD),
            ft.Row([self.fetch_url_field, self.method_dropdown]),
            self.headers_field,
            self.body_field,
        ], spacing=10)

    def create_shell_config(self):
        """创建Shell工具配置界面"""
        self.shell_command_field = ft.TextField(
            label="Shell命令",
            hint_text="例如: ls -la 或 dir",
            width=500,
            value="echo 'Hello from MCP Shell Tool!'"
        )

        self.shell_cwd_field = ft.TextField(
            label="工作目录 (可选)",
            hint_text="例如: /home/<USER>"
            self.status_text.value = "MCP调用成功"
            self.status_text.color = ft.Colors.GREEN_600

        except Exception as ex:
            self.result_text.value = f"MCP调用错误: {str(ex)}"
            self.status_text.value = f"MCP调用失败: {str(ex)}"
            self.status_text.color = ft.Colors.RED_600
            logger.error(f"MCP调用失败: {ex}")

        self.page.update()

    def get_current_tool_config(self) -> Dict[str, Any]:
        """获取当前工具的配置"""
        if self.current_tool == "fetch":
            return {
                "tool": "fetch",
                "url": getattr(self, 'fetch_url_field', {}).value if hasattr(self, 'fetch_url_field') else "",
                "method": getattr(self, 'method_dropdown', {}).value if hasattr(self, 'method_dropdown') else "GET",
                "headers": getattr(self, 'headers_field', {}).value if hasattr(self, 'headers_field') else "",
                "body": getattr(self, 'body_field', {}).value if hasattr(self, 'body_field') else ""
            }
        elif self.current_tool == "shell":
            return {
                "tool": "shell",
                "command": getattr(self, 'shell_command_field', {}).value if hasattr(self, 'shell_command_field') else "",
                "cwd": getattr(self, 'shell_cwd_field', {}).value if hasattr(self, 'shell_cwd_field') else ""
            }
        elif self.current_tool == "filesystem":
            return {
                "tool": "filesystem",
                "action": getattr(self, 'fs_action_dropdown', {}).value if hasattr(self, 'fs_action_dropdown') else "read",
                "path": getattr(self, 'fs_path_field', {}).value if hasattr(self, 'fs_path_field') else "",
                "content": getattr(self, 'fs_content_field', {}).value if hasattr(self, 'fs_content_field') else ""
            }
        return {}

    def build_mcp_prompt(self) -> str:
        """构建MCP工具调用的prompt"""
        config = self.get_current_tool_config()

        if self.current_tool == "fetch":
            prompt = f"""
请使用fetch工具获取网络资源。以下是配置信息：

工具: {config['tool']}
URL: {config['url']}
HTTP方法: {config['method']}
请求头: {config['headers'] or '无'}
请求体: {config['body'] or '无'}

请执行fetch请求并返回详细的结果，包括状态码、响应头和响应内容。如果是JSON响应，请格式化显示。
"""
        elif self.current_tool == "shell":
            prompt = f"""
请使用shell工具执行系统命令。以下是配置信息：

工具: {config['tool']}
命令: {config['command']}
工作目录: {config['cwd'] or '当前目录'}

请执行shell命令并返回执行结果，包括返回码、标准输出和标准错误。
注意：这是一个演示环境，请确保命令的安全性。
"""
        elif self.current_tool == "filesystem":
            prompt = f"""
请使用filesystem工具进行文件系统操作。以下是配置信息：

工具: {config['tool']}
操作: {config['action']}
路径: {config['path']}
内容: {config['content'] or '无（读取或列表操作）'}

请执行文件系统操作并返回结果。对于读取操作，返回文件内容；对于写入操作，返回写入状态；对于列表操作，返回目录内容。
"""
        else:
            prompt = f"请使用{self.current_tool}工具，配置信息：{json.dumps(config, ensure_ascii=False)}"

        return prompt
    
    async def call_llm_api(self, prompt: str) -> str:
        """调用LLM API"""
        headers = {
            "Authorization": f"Bearer {self.api_key_field.value}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "deepseek-ai/DeepSeek-V3-0324-fast",
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 2000
        }
        
        response = requests.post(
            self.api_url_field.value,
            headers=headers,
            json=payload,
            timeout=60
        )
        
        if response.status_code != 200:
            raise Exception(f"API调用失败: {response.status_code} - {response.text}")
        
        result = response.json()
        return result["choices"][0]["message"]["content"]

def main(page: ft.Page):
    MCPToolDemo(page)

if __name__ == "__main__":
    ft.app(target=main)
