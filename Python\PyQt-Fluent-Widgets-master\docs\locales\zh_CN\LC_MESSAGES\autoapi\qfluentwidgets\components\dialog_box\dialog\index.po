# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON>o
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-22 20:49+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/dialog/index.rst:2
#: ********************************
msgid "dialog"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/dialog/index.rst:8
#: ********************************
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/dialog/index.rst:20:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`Ui_MessageBox "
"<qfluentwidgets.components.dialog_box.dialog.Ui_MessageBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/dialog/index.rst:23
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/dialog/index.rst:20:<autosummary>:1
#: ******************************** cf36129c32bf49cc84e16b952583647f
msgid "Ui of message box"
msgstr "消息框的 Ui 类"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/dialog/index.rst:20:<autosummary>:1
#: ********************************
msgid ":py:obj:`Dialog <qfluentwidgets.components.dialog_box.dialog.Dialog>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/dialog/index.rst:38
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/dialog/index.rst:20:<autosummary>:1
#: ******************************** 9001364cb2d54a2b81c4794a9570fe8a
msgid "Dialog box"
msgstr "无边框消息框"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/dialog/index.rst:20:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`MessageBox "
"<qfluentwidgets.components.dialog_box.dialog.MessageBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/dialog/index.rst:56
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/dialog/index.rst:20:<autosummary>:1
#: ******************************** f12a686e07764535b1cd524480b07698
msgid "Message box"
msgstr "带遮罩的消息框"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/dialog/index.rst:36
#: ********************************
msgid "Bases: :py:obj:`qframelesswindow.FramelessDialog`, :py:obj:`Ui_MessageBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/dialog/index.rst:54
#: ********************************
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.dialog_box.mask_dialog_base.MaskDialogBase`,"
" :py:obj:`Ui_MessageBox`"
msgstr ""

