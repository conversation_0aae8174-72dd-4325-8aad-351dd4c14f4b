from flask import Flask, render_template, request, jsonify
import requests
import os

app = Flask(__name__)

@app.route('/', methods=['GET'])
def index():
    try:
        # 打印当前工作目录和模板目录
        print("当前工作目录:", os.getcwd())
        print("模板目录:", os.path.join(os.getcwd(), 'templates'))
        print("模板文件是否存在:", os.path.exists(os.path.join(os.getcwd(), 'templates', 'index.html')))
        return render_template('index.html')
    except Exception as e:
        print("错误详情:", str(e))
        return f"Error loading template: {str(e)}"

@app.route('/check_balance', methods=['POST'])
def check_balance():
    api_key = request.form.get('api_key')
    
    url = "https://api.hyperbolic.xyz/billing/get_current_balance"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            credits = response.json().get('credits', 0)
            balance_usd = credits / 100  # 转换为美元
            return jsonify({
                'success': True,
                'balance': f"${balance_usd:.2f}"
            })
        else:
            return jsonify({
                'success': False,
                'message': '查询失败，请检查API Key是否正确'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'发生错误: {str(e)}'
        })

if __name__ == '__main__':
    app.run(debug=True)
