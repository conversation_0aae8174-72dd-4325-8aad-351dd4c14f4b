<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信读书笔记助手</title>
    <link rel="stylesheet" href="popup.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header>
        <div class="logo">
            <span class="logo-text">微信读书笔记</span>
            <div class="logo-icon"><i class="fas fa-book-reader"></i></div>
        </div>
    </header>

    <main>
        <div class="controls">
            <div class="search-container">
                <input type="text" id="search-input" placeholder="搜索笔记内容...">
                <button id="search-button"><i class="fas fa-search"></i></button>
            </div>
            <div class="filter-container">
                <select id="book-filter">
                    <option value="all">所有书籍</option>
                    <!-- 书籍选项将通过JavaScript动态添加 -->
                </select>
                <select id="color-filter">
                    <option value="all">所有颜色</option>
                    <option value="3">黄色标注</option>
                    <option value="4">绿色标注</option>
                    <option value="5">蓝色标注</option>
                </select>
            </div>
        </div>

        <div class="notes-container">
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>正在加载笔记...</p>
            </div>
            <div id="error-message" class="error-message hidden"></div>
            <div id="notes-list" class="notes-list hidden">
                <!-- 笔记将通过JavaScript动态添加 -->
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <p>微信读书笔记助手 v1.0</p>
        </div>
    </footer>

    <script src="popup.js"></script>
</body>
</html>
