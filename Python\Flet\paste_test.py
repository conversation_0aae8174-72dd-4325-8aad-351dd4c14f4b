import flet as ft
import pyperclip

# text = pyperclip.paste()
# print(text)


def main(page):
    t = ft.Text()
    t2 = ft.TextField()
    def btn_click(e):
        t.value = pyperclip.paste()
        t2.value = pyperclip.paste().upper()
        page.update()
    def copy_click(e):
        t.value = t2.value
        page.update()

    btn2 = ft.ElevatedButton('copy', on_click=copy_click)
    btn = ft.ElevatedButton('Clicd me ', on_click=btn_click)
    page.add(
        btn,
        t,t2,
        btn2
    )


ft.app(target=main)