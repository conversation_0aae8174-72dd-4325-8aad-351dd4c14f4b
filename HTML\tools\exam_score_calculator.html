<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考试成绩分析工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 14px;
        }

        .input-group {
            margin-bottom: 25px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 16px;
        }

        .input-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .input-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .calculate-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 30px;
        }

        .calculate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .results {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            display: none;
        }

        .results.show {
            display: block;
            animation: fadeInUp 0.5s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e1e5e9;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            font-weight: 600;
            color: #333;
            font-size: 16px;
        }

        .result-value {
            font-size: 18px;
            font-weight: 700;
            color: #667eea;
        }

        .summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: center;
        }

        .summary h3 {
            margin-bottom: 10px;
            font-size: 20px;
        }

        .summary p {
            font-size: 18px;
            font-weight: 600;
        }

        .error {
            background: #fee;
            color: #c33;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border: 1px solid #fcc;
            display: none;
        }

        .error.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 考试成绩分析工具</h1>
            <p>单选100道(每题0.5分) + 多选50道(每题1分) = 总分100分</p>
        </div>

        <div class="input-group">
            <label for="correctRate">试题正确率 (%)</label>
            <input type="number" id="correctRate" placeholder="例如：67.33" step="0.01" min="0" max="100">
        </div>

        <div class="input-group">
            <label for="totalScore">总得分</label>
            <input type="number" id="totalScore" placeholder="例如：63.5" step="0.1" min="0" max="100">
        </div>

        <button class="calculate-btn" onclick="calculateResults()">🔍 分析成绩</button>

        <div class="error" id="errorMsg"></div>

        <div class="results" id="results">
            <div class="result-item">
                <span class="result-label">单选正确数量</span>
                <span class="result-value" id="singleCorrect">-</span>
            </div>
            <div class="result-item">
                <span class="result-label">多选正确数量</span>
                <span class="result-value" id="multipleCorrect">-</span>
            </div>
            <div class="result-item">
                <span class="result-label">单选得分</span>
                <span class="result-value" id="singleScore">-</span>
            </div>
            <div class="result-item">
                <span class="result-label">多选得分</span>
                <span class="result-value" id="multipleScore">-</span>
            </div>
            
            <div class="summary">
                <h3>📈 成绩总结</h3>
                <p id="summaryText">-</p>
            </div>
        </div>
    </div>

    <script>
        function calculateResults() {
            const correctRate = parseFloat(document.getElementById('correctRate').value);
            const totalScore = parseFloat(document.getElementById('totalScore').value);
            const errorDiv = document.getElementById('errorMsg');
            const resultsDiv = document.getElementById('results');

            // 清除之前的错误信息
            errorDiv.classList.remove('show');
            resultsDiv.classList.remove('show');

            // 验证输入
            if (isNaN(correctRate) || isNaN(totalScore)) {
                showError('请输入有效的数字');
                return;
            }

            if (correctRate < 0 || correctRate > 100) {
                showError('正确率应在0-100之间');
                return;
            }

            if (totalScore < 0 || totalScore > 100) {
                showError('总得分应在0-100之间');
                return;
            }

            // 计算逻辑
            // 设单选正确x道，多选正确y道
            // 方程组：
            // (x + y) / 150 = correctRate / 100  (正确率方程)
            // 0.5 * x + 1 * y = totalScore       (得分方程)

            const totalQuestions = 150;
            const totalCorrect = (correctRate / 100) * totalQuestions;

            // 从得分方程：y = totalScore - 0.5 * x
            // 代入正确率方程：x + (totalScore - 0.5 * x) = totalCorrect
            // 化简：0.5 * x = totalCorrect - totalScore
            // 所以：x = 2 * (totalCorrect - totalScore)

            const singleCorrect = Math.round(2 * (totalCorrect - totalScore));
            const multipleCorrect = Math.round(totalCorrect - singleCorrect);

            // 验证结果的合理性
            if (singleCorrect < 0 || singleCorrect > 100 || multipleCorrect < 0 || multipleCorrect > 50) {
                showError('输入的数据不符合题目设置，请检查正确率和得分是否匹配');
                return;
            }

            // 计算各部分得分
            const singleScore = singleCorrect * 0.5;
            const multipleScore = multipleCorrect * 1;

            // 显示结果
            document.getElementById('singleCorrect').textContent = singleCorrect;
            document.getElementById('multipleCorrect').textContent = multipleCorrect;
            document.getElementById('singleScore').textContent = singleScore.toFixed(1);
            document.getElementById('multipleScore').textContent = multipleScore.toFixed(1);
            document.getElementById('summaryText').textContent = `${singleCorrect}/100, ${multipleCorrect}/50`;

            resultsDiv.classList.add('show');
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMsg');
            errorDiv.textContent = message;
            errorDiv.classList.add('show');
        }

        // 回车键触发计算
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                calculateResults();
            }
        });

        // 示例数据填充
        window.onload = function() {
            document.getElementById('correctRate').value = '67.33';
            document.getElementById('totalScore').value = '63.5';
        };
    </script>
</body>
</html>
