import flet as ft
import asyncio
import time

# 示例1：使用异步
async def async_counter(page: ft.Page, text: ft.Text):
    for i in range(5):
        text.value = f"异步计数：{i}"
        await page.update_async()  # 异步更新UI
        await asyncio.sleep(1)     # 异步等待，不阻塞

# 示例2：模拟耗时操作
async def async_operation(page: ft.Page, output: ft.Text):
    # 开始操作
    output.value = "开始操作..."
    await page.update_async()
    
    # 模拟耗时任务
    await asyncio.sleep(2)
    
    # 更新结果
    output.value = "操作完成！"
    await page.update_async()

async def main(page: ft.Page):
    page.title = "异步示例"
    
    # 创建显示组件
    counter_text = ft.Text("等待开始...")
    operation_text = ft.Text("准备就绪")
    
    # 异步按钮点击处理
    async def start_counter(e):
        button.disabled = True
        await page.update_async()
        await async_counter(page, counter_text)
        button.disabled = False
        await page.update_async()
    
    # 创建按钮
    button = ft.ElevatedButton("开始计数", on_click=start_counter)
    
    # 添加组件到页面
    page.add(
        button,
        counter_text,
        operation_text,
    )
    
    # 同时执行多个异步操作
    await asyncio.gather(
        async_operation(page, operation_text),
        page.update_async()
    )

ft.app(target=main)
