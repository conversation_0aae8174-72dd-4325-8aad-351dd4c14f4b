<!DOCTYPE html>
<html>
<head>
    <title>生成扩展图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .icon-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        .icon-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        canvas {
            border: 1px solid #ddd;
            margin-bottom: 10px;
        }
        button {
            background-color: #1aad19;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #159b14;
        }
        .instructions {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>微信读书笔记助手 - 图标生成器</h1>
    
    <div class="instructions">
        <h2>使用说明</h2>
        <ol>
            <li>下面显示了三种尺寸的图标预览</li>
            <li>点击每个图标下方的"下载"按钮来保存对应尺寸的PNG图标</li>
            <li>将下载的图标文件重命名为 icon16.png、icon48.png 和 icon128.png</li>
            <li>将这些文件放在扩展的 icons 文件夹中</li>
        </ol>
    </div>
    
    <div class="icon-preview">
        <div class="icon-container">
            <canvas id="canvas16" width="16" height="16"></canvas>
            <span>16x16</span>
            <button onclick="downloadIcon(16)">下载 icon16.png</button>
        </div>
        
        <div class="icon-container">
            <canvas id="canvas48" width="48" height="48"></canvas>
            <span>48x48</span>
            <button onclick="downloadIcon(48)">下载 icon48.png</button>
        </div>
        
        <div class="icon-container">
            <canvas id="canvas128" width="128" height="128"></canvas>
            <span>128x128</span>
            <button onclick="downloadIcon(128)">下载 icon128.png</button>
        </div>
    </div>
    
    <script>
        // SVG 图标数据
        const svgData = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   width="128"
   height="128"
   viewBox="0 0 33.866666 33.866668"
   version="1.1"
   xmlns="http://www.w3.org/2000/svg">
  <circle
     style="fill:#1aad19;fill-opacity:1;stroke:none;stroke-width:0.264583"
     cx="16.933332"
     cy="16.933332"
     r="16.933332" />
  <g
     transform="matrix(0.26458333,0,0,0.26458333,4.2333333,4.2333333)"
     style="fill:#ffffff">
    <path
       d="M 84,11 H 36 C 26.5,11 19,18.5 19,28 v 56 c 0,9.5 7.5,17 17,17 h 48 c 9.5,0 17,-7.5 17,-17 V 28 C 101,18.5 93.5,11 84,11 Z M 36,17 h 48 c 6.1,0 11,4.9 11,11 v 3 H 25 v -3 c 0,-6.1 4.9,-11 11,-11 z m 48,78 H 36 c -6.1,0 -11,-4.9 -11,-11 V 37 h 70 v 47 c 0,6.1 -4.9,11 -11,11 z"
       style="fill:#ffffff" />
    <path
       d="m 60,65 c 9.4,0 17,-7.6 17,-17 0,-9.4 -7.6,-17 -17,-17 -9.4,0 -17,7.6 -17,17 0,9.4 7.6,17 17,17 z m 0,-28 c 6.1,0 11,4.9 11,11 0,6.1 -4.9,11 -11,11 -6.1,0 -11,-4.9 -11,-11 0,-6.1 4.9,-11 11,-11 z"
       style="fill:#ffffff" />
    <path
       d="m 47,75 c -1.7,0 -3,1.3 -3,3 0,1.7 1.3,3 3,3 h 26 c 1.7,0 3,-1.3 3,-3 0,-1.7 -1.3,-3 -3,-3 z"
       style="fill:#ffffff" />
    <path
       d="m 47,85 c -1.7,0 -3,1.3 -3,3 0,1.7 1.3,3 3,3 h 26 c 1.7,0 3,-1.3 3,-3 0,-1.7 -1.3,-3 -3,-3 z"
       style="fill:#ffffff" />
  </g>
</svg>`;
        
        // 创建 SVG 图像对象
        const img = new Image();
        img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
        
        // 图像加载完成后绘制到画布上
        img.onload = function() {
            drawIcon(16);
            drawIcon(48);
            drawIcon(128);
        };
        
        // 在画布上绘制图标
        function drawIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, size, size);
            ctx.drawImage(img, 0, 0, size, size);
        }
        
        // 下载图标
        function downloadIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
    </script>
</body>
</html>
