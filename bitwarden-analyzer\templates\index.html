<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitwarden 数据筛查工具 - Python版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: flex;
            min-height: 600px;
        }

        .left-panel {
            flex: 1;
            padding: 30px;
            border-right: 1px solid #eee;
        }

        .right-panel {
            flex: 2;
            padding: 30px;
            background: #f8f9fa;
        }

        .upload-section {
            margin-bottom: 30px;
        }

        .upload-area {
            border: 3px dashed #4facfe;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            background: #f0f4ff;
            border-color: #2196F3;
        }

        .upload-area.dragover {
            background: #e3f2fd;
            border-color: #1976D2;
        }

        .upload-icon {
            font-size: 3em;
            color: #4facfe;
            margin-bottom: 15px;
        }

        .file-input {
            display: none;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4facfe;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        .task-list {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .task-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .task-item:hover {
            background-color: #f8f9fa;
        }

        .task-item:last-child {
            border-bottom: none;
        }

        .task-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .task-status {
            font-size: 0.9em;
            color: #666;
        }

        .task-actions {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 5px 10px;
            font-size: 0.8em;
            border-radius: 15px;
        }

        .btn-delete {
            background: #dc3545;
        }

        .btn-delete:hover {
            background: #c82333;
        }

        .clickable-url {
            color: #007bff;
            text-decoration: underline;
            cursor: pointer;
        }

        .clickable-url:hover {
            color: #0056b3;
        }

        .status-running {
            color: #ff9800;
        }

        .status-completed {
            color: #4caf50;
        }

        .status-failed {
            color: #f44336;
        }

        .results-section {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: white;
        }

        .result-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .result-item:hover {
            background-color: #f8f9fa;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .status-success {
            color: #4caf50;
            font-weight: bold;
        }

        .status-failed {
            color: #f44336;
            font-weight: bold;
        }

        .status-skipped {
            color: #ff9800;
            font-weight: bold;
        }

        .status-internal {
            color: #9c27b0;
            font-weight: bold;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .notification.success {
            background: #4caf50;
        }

        .notification.error {
            background: #f44336;
        }

        .notification.info {
            background: #2196F3;
        }

        .settings-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .settings-section h4 {
            margin-bottom: 10px;
            color: #333;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
        }

        .current-test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            border-left: 4px solid #4facfe;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .current-test-section h4 {
            margin-bottom: 15px;
            color: #333;
        }

        .current-test-box {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
        }

        .test-site-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            font-size: 1.1em;
        }

        .test-url {
            color: #007bff;
            margin-bottom: 8px;
            word-break: break-all;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .test-status {
            color: #666;
            font-size: 0.9em;
        }

        .test-status-value {
            font-weight: bold;
            color: #28a745;
        }

        .manual-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-skip {
            background: #ff9800;
        }

        .btn-skip:hover {
            background: #f57c00;
        }

        .btn-invalid {
            background: #f44336;
        }

        .btn-invalid:hover {
            background: #d32f2f;
        }

        .btn-internal {
            background: #9c27b0;
        }

        .btn-internal:hover {
            background: #7b1fa2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Bitwarden 数据筛查工具</h1>
            <p>Python后端版本 • 支持状态保存 • 数据持久化</p>
        </div>

        <div class="main-content">
            <div class="left-panel">
                <div class="upload-section">
                    <h3>📁 上传数据文件</h3>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">📄</div>
                        <p>点击或拖拽文件到此处</p>
                        <p style="font-size: 0.9em; color: #666; margin-top: 10px;">
                            支持 .txt, .csv, .json 格式
                        </p>
                    </div>
                    <input type="file" id="fileInput" class="file-input" accept=".txt,.csv,.json">
                </div>

                <div class="settings-section">
                    <h4>⚙️ 测试设置</h4>
                    <div class="checkbox-group">
                        <input type="checkbox" id="skipInternalUrls">
                        <span>跳过内网网段测试</span>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="enableDetailedLogging">
                        <span>启用详细日志</span>
                    </div>
                </div>

                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalCount">0</div>
                        <div class="stat-label">总条目数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="testedCount">0</div>
                        <div class="stat-label">已测试</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="successCount">0</div>
                        <div class="stat-label">有效链接</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="duplicateCount">0</div>
                        <div class="stat-label">重复项</div>
                    </div>
                </div>

                <div class="current-test-section" id="currentTestSection" style="display: none;">
                    <h4>🔍 正在测试</h4>
                    <div class="current-test-box">
                        <div class="test-site-name">
                            <span id="currentSiteName">网站名称</span>
                        </div>
                        <div class="test-url">
                            <span id="currentUrl">URL地址</span>
                        </div>
                        <div class="test-status">
                            状态: <span id="currentStatus" class="test-status-value">准备中...</span>
                        </div>
                    </div>
                    <div class="manual-controls" style="display: flex; gap: 10px; margin-top: 15px;">
                        <button class="btn btn-skip" onclick="skipCurrent()" style="background: #ff9800;">⏭️ 跳过</button>
                        <button class="btn btn-invalid" onclick="markInvalid()" style="background: #f44336;">❌ 标记无效</button>
                        <button class="btn btn-internal" onclick="markInternal()" style="background: #9c27b0;">🏠 标记内网</button>
                    </div>
                </div>

                <div style="margin-top: 30px;">
                    <button class="btn" id="startBtn" onclick="startAnalysis()" disabled>🚀 开始分析</button>
                    <button class="btn" id="pauseBtn" onclick="pauseAnalysis()" disabled>⏸️ 暂停</button>
                    <button class="btn" id="exportBtn" onclick="exportReport()" disabled>📊 导出报告</button>
                </div>
            </div>

            <div class="right-panel">
                <h3>📋 任务列表</h3>
                <div class="task-list" id="taskList">
                    <div style="padding: 40px; text-align: center; color: #666;">
                        暂无任务，请上传数据文件开始分析
                    </div>
                </div>

                <h3>📈 实时结果</h3>
                <div class="results-section" id="resultsSection">
                    <div style="padding: 40px; text-align: center; color: #666;">
                        选择任务查看结果
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/app.js"></script>
</body>
</html>
