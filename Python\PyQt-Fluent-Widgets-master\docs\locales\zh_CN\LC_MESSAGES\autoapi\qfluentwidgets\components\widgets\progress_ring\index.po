# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON>o
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-10 23:18+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/widgets/progress_ring/index.rst:2
#: d983dd55e0d64713a96e28c967256a0e
msgid "progress_ring"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/progress_ring/index.rst:8
#: 622afc2a068f4ac4bd3407d576cfec20
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/progress_ring/index.rst:18:<autosummary>:1
#: 5a0087d7986f423fafd562dfe6e6c22a
msgid ""
":py:obj:`ProgressRing "
"<qfluentwidgets.components.widgets.progress_ring.ProgressRing>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/progress_ring/index.rst:23
#: ../../source/autoapi/qfluentwidgets/components/widgets/progress_ring/index.rst:18:<autosummary>:1
#: 7eeb5a95fac4465dae7a4d9f52a5680f fdb1ce571cbb474ebf078cdeac2ef84d
msgid "Progress ring"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/progress_ring/index.rst:21
#: da7247771d414ebca7b1e1155c996522
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.widgets.progress_bar.ProgressBar`"
msgstr ""

