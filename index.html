<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API余额查询</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header>
        <div class="logo">
            <span class="logo-text">API余额查询</span>
            <div class="logo-icon"><i class="fas fa-wallet"></i></div>
        </div>
    </header>

    <main>
        <section class="hero">
            <h1>查询API余额</h1>
            <p>快速安全地查询您的API账户余额</p>
        </section>

        <div class="balance-cards">
            <section class="balance-card silicon">
                <div class="card-header">
                    <h2>API余额查询</h2>
                    <div class="token-icon"><i class="fas fa-microchip"></i></div>
                </div>
                <div class="card-body">
                    <div class="api-notice api-notice-info">
                        <p>输入您的API令牌查询账户余额</p>
                    </div>
                    <form id="silicon-form" class="balance-form">
                        <div class="input-group">
                            <label for="silicon-address">API令牌</label>
                            <input type="text" id="silicon-address" placeholder="请输入您的API令牌">
                        </div>
                        <button type="submit" class="check-button">
                            <span>查询余额</span>
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </form>
                    <div class="result-container" id="silicon-result">
                        <div class="loader" id="silicon-loader">
                            <div class="spinner"></div>
                        </div>
                        <div class="balance-display" id="silicon-balance-display">
                            <h3>账户余额</h3>
                            <div class="balance-amount">
                                <span id="silicon-balance">0.00</span>
                            </div>

                            <div class="balance-info">
                                <div class="info-item">
                                    <span class="info-label">用户</span>
                                    <span class="info-value" id="silicon-user">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">更新时间</span>
                                    <span class="info-value" id="silicon-last-updated">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="balance-card hyperbolic">
                <div class="card-header">
                    <h2>Hyperbolic余额查询</h2>
                    <div class="token-icon"><i class="fas fa-chart-line"></i></div>
                </div>
                <div class="card-body">
                    <div class="api-notice api-notice-info">
                        <p>输入您的Hyperbolic API令牌查询账户余额</p>
                    </div>
                    <form id="hyperbolic-form" class="balance-form">
                        <div class="input-group">
                            <label for="hyperbolic-address">API令牌</label>
                            <input type="text" id="hyperbolic-address" placeholder="请输入您的Hyperbolic API令牌">
                        </div>
                        <button type="submit" class="check-button">
                            <span>查询余额</span>
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </form>
                    <div class="result-container" id="hyperbolic-result">
                        <div class="loader" id="hyperbolic-loader">
                            <div class="spinner"></div>
                        </div>
                        <div class="balance-display" id="hyperbolic-balance-display">
                            <h3>账户余额</h3>
                            <div class="balance-amount">
                                <span id="hyperbolic-balance">0.00</span>
                            </div>

                            <div class="balance-info">
                                <div class="info-item">
                                    <span class="info-label">用户</span>
                                    <span class="info-value" id="hyperbolic-user">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">更新时间</span>
                                    <span class="info-value" id="hyperbolic-last-updated">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <script src="script.js"></script>
</body>
</html>
