# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON><PERSON>
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-22 20:49+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/widgets/label/index.rst:2
#: 40a6af00cfc04070863d9d4260a7a048
msgid "label"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/label/index.rst:8
#: 8646dcf0fc37413ba6932d465b9bd402
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/label/index.rst:18:<autosummary>:1
#: 242fbfdfee5e43d9a33cf5cce31fed4c
msgid ""
":py:obj:`PixmapLabel "
"<qfluentwidgets.components.widgets.label.PixmapLabel>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/label/index.rst:23
#: ../../source/autoapi/qfluentwidgets/components/widgets/label/index.rst:18:<autosummary>:1
#: 77e5c84f05fc4091ae94bdea64003534 cd38f3d2bc17417cb2d8000abe3b5187
msgid "Label for high dpi pixmap"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/label/index.rst:21
#: 2be4e7928f07407785e158bb74a57756
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QLabel`"
msgstr ""

