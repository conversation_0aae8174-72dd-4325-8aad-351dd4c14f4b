# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON><PERSON>
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-10 23:18+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/widgets/list_view/index.rst:2
#: 1b66d073dc2c4c0990f1e6af2c856bf6
msgid "list_view"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/list_view/index.rst:8
#: 8f3eeef649e54beaa4c94eacca829e2a
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/list_view/index.rst:21:<autosummary>:1
#: 4fc4aece650b41c2916728490fdbd661
msgid ""
":py:obj:`ListItemDelegate "
"<qfluentwidgets.components.widgets.list_view.ListItemDelegate>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/list_view/index.rst:26
#: ../../source/autoapi/qfluentwidgets/components/widgets/list_view/index.rst:21:<autosummary>:1
#: d42c4d236c504d6dbf0080d0dbd756f0 eff41f72662d4315ad6b91cf3d078826
msgid "List item delegate"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/list_view/index.rst:21:<autosummary>:1
#: f4f2005c432b4a33b3e66971e4426341
msgid ""
":py:obj:`ListBase "
"<qfluentwidgets.components.widgets.list_view.ListBase>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/list_view/index.rst:21:<autosummary>:1
#: a1bc9bcadcce4bdf87197b23cbdd41ca
msgid ""
":py:obj:`ListWidget "
"<qfluentwidgets.components.widgets.list_view.ListWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/list_view/index.rst:63
#: ../../source/autoapi/qfluentwidgets/components/widgets/list_view/index.rst:21:<autosummary>:1
#: 2b9c3798fa3b49d8a2499baa8b252354 d4425822b61f4c949b6791f8604f1faa
msgid "List widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/list_view/index.rst:21:<autosummary>:1
#: 9265526378124a28a5676d5a9c8120df
msgid ""
":py:obj:`ListView "
"<qfluentwidgets.components.widgets.list_view.ListView>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/list_view/index.rst:76
#: ../../source/autoapi/qfluentwidgets/components/widgets/list_view/index.rst:21:<autosummary>:1
#: 021c1ca33869415ba3497869569c027e 361416f3a67349d08a183b914ae63a5c
msgid "List view"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/list_view/index.rst:24
#: a261a0fed4ac4361987d9fc3975721c9
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.widgets.table_view.TableItemDelegate`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/list_view/index.rst:61
#: fc1c782baa264db5877e686dbcac2869
msgid "Bases: :py:obj:`ListBase`, :py:obj:`PyQt5.QtWidgets.QListWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/list_view/index.rst:74
#: 188bfa08f9e94bd6a734720a73d4717e
msgid "Bases: :py:obj:`ListBase`, :py:obj:`PyQt5.QtWidgets.QListView`"
msgstr ""

