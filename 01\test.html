<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vaultwarden 密码去重工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #f5f6fa;
            color: #2c3e50;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .upload-section {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #3498db;
            border-radius: 8px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #2980b9;
            background: #f1f3f5;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 20px 0;
        }

        .action-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .check-btn {
            background: #2ecc71;
            color: white;
        }

        .check-btn:hover {
            background: #27ae60;
        }

        .remove-btn {
            background: #e74c3c;
            color: white;
        }

        .remove-btn:hover {
            background: #c0392b;
        }

        .results {
            margin-top: 30px;
        }

        .duplicate-item {
            background: #fff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            animation: fadeIn 0.5s ease;
        }

        .duplicate-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .status {
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
        }

        .success {
            background: #d4edda;
            color: #155724;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Vaultwarden 密码去重工具</h1>
        
        <div class="upload-section">
            <input type="file" id="fileInput" class="file-input" accept=".json">
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                选择 Vaultwarden 导出文件
            </button>
            <p id="fileName" style="margin-top: 10px;"></p>
        </div>

        <div class="action-buttons">
            <button class="action-btn check-btn" onclick="checkDuplicates()">检查重复项</button>
            <button class="action-btn remove-btn" onclick="removeDuplicates()">删除重复项</button>
        </div>

        <div id="loading" class="loading"></div>
        <div id="status" class="status"></div>
        <div id="results" class="results"></div>
    </div>

    <script>
        let vaultData = null;
        let duplicateItems = [];

        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                document.getElementById('fileName').textContent = `已选择: ${file.name}`;
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        vaultData = JSON.parse(e.target.result);
                        showStatus('文件加载成功！', 'success');
                    } catch (error) {
                        showStatus('文件格式错误，请确保选择正确的 Vaultwarden 导出文件！', 'error');
                    }
                };
                reader.readAsText(file);
            }
        });

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function checkDuplicates() {
            if (!vaultData) {
                showStatus('请先选择文件！', 'error');
                return;
            }

            showLoading(true);
            setTimeout(() => {
                const items = vaultData.items || [];
                const seen = new Map();
                duplicateItems = [];

                items.forEach(item => {
                    const key = `${item.name}_${item.login?.username || ''}`;
                    if (seen.has(key)) {
                        duplicateItems.push({
                            original: seen.get(key),
                            duplicate: item
                        });
                    } else {
                        seen.set(key, item);
                    }
                });

                displayResults();
                showLoading(false);
            }, 500);
        }

        function displayResults() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';

            if (duplicateItems.length === 0) {
                showStatus('未发现重复项！', 'success');
                return;
            }

            showStatus(`发现 ${duplicateItems.length} 组重复项`, 'success');

            duplicateItems.forEach((group, index) => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'duplicate-item';
                itemDiv.innerHTML = `
                    <h3>重复组 ${index + 1}</h3>
                    <p><strong>名称:</strong> ${group.original.name}</p>
                    <p><strong>用户名:</strong> ${group.original.login?.username || '无'}</p>
                    <p><strong>URL:</strong> ${group.original.login?.uris?.[0]?.uri || '无'}</p>
                `;
                resultsDiv.appendChild(itemDiv);
            });
        }

        function removeDuplicates() {
            if (!vaultData || duplicateItems.length === 0) {
                showStatus('没有可删除的重复项！', 'error');
                return;
            }

            showLoading(true);
            setTimeout(() => {
                const items = vaultData.items || [];
                const seen = new Map();
                const uniqueItems = [];

                items.forEach(item => {
                    const key = `${item.name}_${item.login?.username || ''}`;
                    if (!seen.has(key)) {
                        seen.set(key, true);
                        uniqueItems.push(item);
                    }
                });

                vaultData.items = uniqueItems;
                
                // 创建下载链接
                const blob = new Blob([JSON.stringify(vaultData, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'vaultwarden_deduplicated.json';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                showStatus('重复项已删除，新文件已下载！', 'success');
                duplicateItems = [];
                displayResults();
                showLoading(false);
            }, 500);
        }
    </script>
</body>
</html>
