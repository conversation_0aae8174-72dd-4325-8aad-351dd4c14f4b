SettingCard {
    border: 1px solid rgb(230, 230, 230);
    border-radius: 6px;
    background-color: rgb(253, 253, 253);
}

QLabel {
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: black;
    padding: 0;
    border: none;
    background-color: transparent;
}

QLabel#contentLabel {
    font: 11px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: rgb(96, 96, 96);
    padding: 0;
}

RangeSettingCard > QLabel#valueLabel{
    color: rgb(96, 96, 96);
}


QPushButton {
    border: 1px solid rgb(238, 239, 238);
    border-radius: 5px;
    border-bottom: 1px solid rgb(212, 213, 212);
    padding: 5px 36px 5px 36px;
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: black;
    background-color: rgb(254, 254, 254);
    outline: none;
}

QPushButton:hover {
    background-color: rgb(251, 251, 251);
}

QPushButton:pressed {
    background-color: rgb(252, 252, 252);
    border-bottom: 1px solid rgb(238, 239, 238);
    color: rgba(0, 0, 0, 0.63);
}


#primaryButton {
    color: white;
    background-color: --ThemeColorPrimary;
    border: 1px solid --ThemeColorLight1;
    border-bottom: 1px solid --ThemeColorDark1;
    padding: 5px 12px 5px 12px;
    outline: none;
}

#primaryButton:hover {
    background-color: --ThemeColorLight1;
    border: 1px solid --ThemeColorLight2;
    border-bottom: 1px solid --ThemeColorDark1;
}

#primaryButton:pressed {
    color: rgba(255, 255, 255, 0.63);
    background-color: --ThemeColorLight3;
    border: 1px solid --ThemeColorLight3;
}

ColorPickerButton {
    border: 1px solid rgb(240, 240, 240);
    border-radius: 5px;
    border-bottom: 1px solid rgb(214, 214, 214);
}
