# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON>o
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-22 20:49+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/widgets/acrylic_label/index.rst:2
#: 9f29aa7835cf45848bf189701eb34c50
msgid "acrylic_label"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/acrylic_label/index.rst:8
#: bc68aed7270e4d8399a0f27c0c160d65
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/acrylic_label/index.rst:21:<autosummary>:1
#: 3ebd583289174cb7913177f9dabf6d9f
msgid ""
":py:obj:`BlurCoverThread "
"<qfluentwidgets.components.widgets.acrylic_label.BlurCoverThread>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/acrylic_label/index.rst:35
#: ../../source/autoapi/qfluentwidgets/components/widgets/acrylic_label/index.rst:21:<autosummary>:1
#: 0b9d54f544b047d0a4b1c82e22e0c68c bbf4478a94fd460a8da37fbd2de87515
msgid "Blur album cover thread"
msgstr "模糊图片线程"

#: ../../source/autoapi/qfluentwidgets/components/widgets/acrylic_label/index.rst:21:<autosummary>:1
#: d6776328b7ec4624bdb421a24030c18b
msgid ""
":py:obj:`AcrylicTextureLabel "
"<qfluentwidgets.components.widgets.acrylic_label.AcrylicTextureLabel>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/acrylic_label/index.rst:52
#: ../../source/autoapi/qfluentwidgets/components/widgets/acrylic_label/index.rst:21:<autosummary>:1
#: 7f0f53187eee498da274322a7620bb13 ea980a4a98174fe6a1ee6ff61c945e64
msgid "Acrylic texture label"
msgstr "亚克力纹理标签"

#: ../../source/autoapi/qfluentwidgets/components/widgets/acrylic_label/index.rst:21:<autosummary>:1
#: 9f34e51246244959ae61bd0d545b4d31
msgid ""
":py:obj:`AcrylicLabel "
"<qfluentwidgets.components.widgets.acrylic_label.AcrylicLabel>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/acrylic_label/index.rst:65
#: ../../source/autoapi/qfluentwidgets/components/widgets/acrylic_label/index.rst:21:<autosummary>:1
#: 32d690b13c84495aa4827dee44b1daa9 6b96c37fd94c4d979db005efd532139b
msgid "Acrylic label"
msgstr "亚克力标签"

#: ../../source/autoapi/qfluentwidgets/components/widgets/acrylic_label/index.rst:27:<autosummary>:1
#: 0c768141550e482f8fad6048b996e021
msgid ""
":py:obj:`gaussianBlur "
"<qfluentwidgets.components.widgets.acrylic_label.gaussianBlur>`\\ "
"\\(imagePath\\[\\, blurRadius\\, brightFactor\\, ...\\]\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/acrylic_label/index.rst:33
#: 9622092179f34a6f876187063ebe016a
msgid "Bases: :py:obj:`PyQt5.QtCore.QThread`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/acrylic_label/index.rst:50
#: ../../source/autoapi/qfluentwidgets/components/widgets/acrylic_label/index.rst:63
#: 7e53c6a5aeae4589bf9c3e1d968b8aaf fbfcaaec6f994134b0dd99926c68fe6e
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QLabel`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/acrylic_label/index.rst:69
#: d106913814484f50b253bc1fcd0b1ff9
msgid "set the image to be blurred"
msgstr "设置被磨砂的图片"

