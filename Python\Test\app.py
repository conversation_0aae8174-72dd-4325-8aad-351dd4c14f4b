from flask import Flask, render_template
import folium
import random
import os
import requests

# 获取当前文件所在目录的绝对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
template_dir = os.path.join(current_dir, 'templates')

app = Flask(__name__, template_folder=template_dir)

# 运城各区县中心点数据（用于标注）
districts = {
    '盐湖区': [35.0157, 111.0682],
    '临猗县': [35.1445, 110.7744],
    '万荣县': [35.4154, 110.8385],
    '闻喜县': [35.3564, 111.2248],
    '稷山县': [35.6040, 110.9833],
    '新绛县': [35.6164, 111.2247],
    '绛县': [35.4914, 111.5684],
    '垣曲县': [35.2972, 111.6701],
    '夏县': [35.1413, 111.2203],
    '平陆县': [34.8371, 111.2121],
    '芮城县': [34.6934, 110.6944],
    '永济市': [34.8671, 110.4477],
    '河津市': [35.5964, 110.7120],
}

def generate_random_data():
    data = {}
    for district in districts:
        data[district] = {
            '用户数': random.randint(10000, 100000),
            '终端数': random.randint(5000, 50000)
        }
    return data

def get_geojson_data():
    url = "https://geo.datav.aliyun.com/areas_v3/bound/140800_full.json"
    try:
        response = requests.get(url)
        return response.json()
    except Exception as e:
        print(f"获取地理数据失败: {e}")
        return None

@app.route('/')
def index():
    # 创建地图，设置中心点为运城市中心
    m = folium.Map(
        location=[35.0299, 111.0082],
        zoom_start=9,
        min_zoom=8,
        max_zoom=12,
        tiles='cartodbpositron',  # 使用浅色地图底图
        tap=False,  # 禁用点击选择框
        boxZoom=False,  # 禁用框选
        doubleClickZoom=False  # 禁用双击缩放
    )
    
    # 添加自定义CSS样式来禁用选择框
    m.get_root().header.add_child(folium.Element("""
        <style>
            .leaflet-container {
                outline: none !important;
            }
            .leaflet-selection-rectangle-pane {
                display: none !important;
            }
        </style>
    """))
    
    district_data = generate_random_data()
    geojson_data = get_geojson_data()
    
    if geojson_data:
        # 添加 GeoJSON 数据
        folium.GeoJson(
            geojson_data,
            name='运城区县',
            style_function=lambda x: {
                'fillColor': '#A6BDDB',
                'color': 'transparent',
                'weight': 0,
                'fillOpacity': 0.6
            },
            highlight_function=lambda x: {
                'fillColor': '#3182BD',  # 高亮时的颜色
                'color': 'transparent',
                'weight': 0,
                'fillOpacity': 0.8  # 高亮时的透明度
            },
            tooltip=folium.GeoJsonTooltip(
                fields=['name'],
                aliases=[''],
                labels=False,
                style="""
                    background-color: white;
                    border: 1px solid #000;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-size: 14px;
                    font-family: Arial;
                """
            )
        ).add_to(m)
    
    # 添加区县名称标注
    for district, coords in districts.items():
        data = district_data[district]
        
        # 添加区县名称标注
        folium.Tooltip(
            district,
            permanent=True,
            direction='center',
            style="""
                background-color: transparent;
                border: none;
                box-shadow: none;
                padding: 0;
                font-size: 12px;
                font-weight: bold;
                color: #000;
                text-shadow: 1px 1px 1px #fff;
            """
        ).add_to(folium.CircleMarker(
            location=coords,
            radius=0,
            color='transparent',
            fill=False
        ).add_to(m))
    
    # 设置地图边界
    m.fit_bounds([
        [34.5934, 110.2977],  # 最南端和最西端
        [35.7040, 111.8201]   # 最北端和最东端
    ])
    
    map_path = os.path.join(template_dir, 'map.html')
    m.save(map_path)
    return render_template('index.html', district_data=district_data)

@app.route('/map')
def map_view():
    return render_template('map.html')

if __name__ == '__main__':
    app.run(debug=True)