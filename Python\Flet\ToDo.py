import flet as ft

class TodoApp(ft.UserControl):
    def build(self):
        self.new_task = ft.TextField(hint_text = 'Whats needs to do ', expand=True)
        self.tasks = ft.Column()
        return ft.Column(
            controls=[
                ft.Row(controls=[
                    self.new_task,
                    ft.FloatingActionButton('click',on_click = self.add_clicked)    
                ]),
                self.tasks
            ]
        )
    def task_delete(self,task):
        self.tasks.controls.remove(task)
        self.update()

    def add_clicked(self,e):
        task = Task(self.new_task.value, self.task_delete)
        self.tasks.controls.append(task)
        self.new_task.value = ''
        self.update()

class Task(ft.UserControl):
    def __init__(self, task_name, task_delete):
        super().__init__()
        self.task_name = task_name
        self.task_delete = task_delete
    def delete_clicked(self,e):
        self.task_delete(self)
        
    def build(self):
        self.display_task = ft.Checkbox(value=False, label = self.task_name)
        self.edit_name = ft.TextField(expand=1)

        self.display_view = ft.Row(
            # alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            # vertical_alignment=ft.CrossAxisAlignment.CENTER,
            controls=[
                self.display_task,
                ft.Row(
                    # spacing=0,
                    controls=[
                        ft.IconButton(
                            icon=ft.icons.CREATE_OUTLINED,
                            tooltip="Edit To-Do",
                            on_click=self.edit_clicked,
                        ),
                        ft.IconButton(
                            ft.icons.DELETE_OUTLINE,
                            tooltip="Delete To-Do",
                            on_click=self.delete_clicked,
                        )
                    ]
                )
            ]
        )

        self.edit_view = ft.Row(
            visible=False,
            # alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            # vertical_alignment=ft.CrossAxisAlignment.CENTER,
            controls=[
                self.edit_name,
                ft.IconButton(
                    icon=ft.icons.DONE_OUTLINE_OUTLINED,
                    icon_color=ft.colors.GREEN,
                    tooltip="Update To-Do",
                    on_click=self.save_clicked,
                )
            ]
        )
        return ft.Column(controls=[
            self.display_view, self.edit_view
        ])
    def edit_clicked(self, e):
        self.edit_name.value = self.display_task.label
        self.display_view.visible = False
        self.edit_view.visible = True
        self.update()

    def save_clicked(self, e):
        self.display_task.label = self.edit_name.value
        self.display_view.visible = True
        self.edit_view.visible = False
        self.update()


def main(page: ft.Page):
    page.title = "ToDo App"
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.update()

    # create application instance
# create application instance
    app1 = TodoApp()

# add application's root control to the page
    page.add(app1)

ft.app(target=main)