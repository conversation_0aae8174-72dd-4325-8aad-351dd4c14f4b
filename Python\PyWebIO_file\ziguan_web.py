# from types import NoneType
import xlwings as xw
import pyperclip as pc
from pywebio.output import *
from pywebio.input import *
from pywebio.pin import *
from pywebio.exceptions import *

app=xw.App(visible=True,add_book=False)
#不显示Excel消息框
app.display_alerts=False 
#关闭屏幕更新,可加快宏的执行速度
app.screen_updating=False  
wk = app.books.open('./python/PywebIO_file/**********.xlsx')
sht = xw.sheets[0]
nrow = sht.range('b1').expand().shape[0]

def get_value(ncol,nrow):
    return sht.range(f'{ncol}{nrow}').value
lst = sht.range(f'b1:b{nrow}').value 
  
def get_info(n):
    # print(lst)
    for a in lst:
        
        if n == a:
            print('可查询到')
            i = lst.index(n) + 1 
            dldh = get_value('c',i)
            a_gl = get_value('l',i)
            z_gl = get_value('m',i)
            a_jrzd = get_value('s',i)
            a_jrjf = get_value('t',i)
            z_jrzd = get_value('u',i)
            z_jrjf = get_value('v',i)
            a_yhjf = get_value('w',i)
            a_yhzd = get_value('x',i)
            z_yhjf = get_value('y',i)
            z_yhzd = get_value('z',i)
            z_zyd = get_value('aa',i)
            z_sblx = get_value('ab', i)

            # print(type(z_jrzd))
            # print(z_jrzd)
            # if z_jrzd:
            #     pc.copy(z_yhzd)
            # print('=='* 30)
            
            # print('\n接入侧：')
            # print(f'   A机房站点：{a_jrzd}   {a_jrjf}   \n   Z机房站点:{z_jrzd}   {z_jrjf}')
            # print('\n用户侧：')
            # print(f'   A机房站点：{a_yhzd}   {a_yhjf}   \n   Z机房站点:{z_yhzd}   {z_yhjf}  \n   Z资源点{z_zyd} \n')
            
            

            # print(f"A接入设备：{get_value('g',i)}")
            # print(f"Z接入设备：{get_value('i',i)}")
            # print(f"Z用户设备类型:{z_sblx}    Z用户设备名称：{get_value('ac',i)}\n")
            
            # print(f'光路： A: {a_gl}    Z:{z_gl} \n')

            # print('电路代号：', dldh,'\n')
            # print('---' * 20)
            return {'dldh':dldh, 'agl':a_gl, 'zgl':z_gl, 'ajrzd':a_jrzd, 'ajrjf':a_jrjf, 'ayhjf':a_yhjf, 'ayhzd':a_yhzd,
                'zjrzd':z_jrzd, 'zjrjf':z_jrjf, 'zyhjf':z_yhjf, 'zzyd':z_zyd, 'zyhzd':z_yhzd, 'zsblx':z_sblx}
    
            break
    
    return None
    
      
while True:   
    try: 
        n = input('输入0L单号:')
        res = get_info(n)
        print('res=', res)
        print(type(n))
        if res == None:
            with use_scope('res', clear=True):
                put_text('未查询到该单号 %s' %n)
        else:
            with use_scope('res', clear=True):
                put_table([
                    [' ', 'A', 'Z'],
                    ['单号',span(n, col=2)] ,
                    ['接入 - 站点',res['ajrzd'],res['zjrzd']], 
                    ['接入 - 机房', res['ajrjf'],res['zjrjf']],
                    ['用户-站点',res['ayhzd'],res['zyhzd']], 
                    ['用户-机房', res['ayhjf'],res['zyhjf']],  
                    ['光路', res['agl'], res['zgl']], 
                    ['电路', span(res['dldh'],col=2)],
                    ['接入方式', ' ',res['zsblx']]
                    ])
    except SessionClosedException:
        app.kill()
        break