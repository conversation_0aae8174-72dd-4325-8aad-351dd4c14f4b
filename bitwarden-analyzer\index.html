<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitwarden 数据筛查工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: flex;
            min-height: 600px;
        }

        .left-panel {
            flex: 1;
            padding: 30px;
            border-right: 1px solid #eee;
        }

        .right-panel {
            flex: 2;
            padding: 30px;
            background: #f8f9fa;
        }

        .upload-section {
            margin-bottom: 30px;
        }

        .upload-area {
            border: 3px dashed #4facfe;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            background: #f0f4ff;
            border-color: #2196F3;
        }

        .upload-area.dragover {
            background: #e3f2fd;
            border-color: #1976D2;
        }

        .upload-icon {
            font-size: 3em;
            color: #4facfe;
            margin-bottom: 15px;
        }

        .file-input {
            display: none;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-section {
            margin-top: 30px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            width: 0%;
            transition: width 0.3s ease;
        }

        .current-test {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #4facfe;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-info-box {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #e9ecef;
        }

        .test-site-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            font-size: 1.1em;
        }

        .test-url {
            color: #007bff;
            margin-bottom: 8px;
            word-break: break-all;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .test-status {
            color: #666;
            font-size: 0.9em;
        }

        .test-status-value {
            font-weight: bold;
            color: #28a745;
        }

        .test-response {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
            padding: 8px;
            background: #fff;
            border-radius: 4px;
            border-left: 3px solid #17a2b8;
        }

        .test-controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn-skip {
            background: #ff9800;
        }

        .btn-invalid {
            background: #f44336;
        }

        .btn-internal {
            background: #9c27b0;
        }

        .results-section {
            height: 500px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: white;
        }

        .result-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            transition: background-color 0.2s ease;
        }

        .result-item:hover {
            background-color: #f8f9fa;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .status-success {
            color: #4caf50;
            font-weight: bold;
        }

        .status-failed {
            color: #f44336;
            font-weight: bold;
        }

        .status-skipped {
            color: #ff9800;
            font-weight: bold;
        }

        .status-invalid {
            color: #f44336;
            font-weight: bold;
        }

        .status-internal {
            color: #9c27b0;
            font-weight: bold;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4facfe;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        .report-section {
            margin-top: 30px;
        }

        .report-content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #ddd;
            max-height: 400px;
            overflow-y: auto;
        }

        .duplicate-group {
            background: #fff3e0;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 8px;
            border-left: 4px solid #ff9800;
        }

        .duplicate-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .duplicate-item:last-child {
            border-bottom: none;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .notification.success {
            background: #4caf50;
        }

        .notification.error {
            background: #f44336;
        }

        .notification.info {
            background: #2196F3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Bitwarden 数据筛查工具</h1>
            <p>检测链接有效性 • 筛查重复域名和密码 • 生成详细报告</p>
            <div style="margin-top: 15px;">
                <a href="help.html" class="btn" style="background: rgba(255,255,255,0.2); color: white;">📖 使用说明</a>
            </div>
        </div>

        <div class="main-content">
            <div class="left-panel">
                <div class="upload-section">
                    <h3>📁 上传数据文件</h3>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">📄</div>
                        <p>点击或拖拽文件到此处</p>
                        <p style="font-size: 0.9em; color: #666; margin-top: 10px;">
                            支持 .txt, .csv, .json 格式
                        </p>
                    </div>
                    <input type="file" id="fileInput" class="file-input" accept=".txt,.csv,.json">
                </div>

                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalCount">0</div>
                        <div class="stat-label">总条目数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="testedCount">0</div>
                        <div class="stat-label">已测试</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="successCount">0</div>
                        <div class="stat-label">有效链接</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="duplicateCount">0</div>
                        <div class="stat-label">重复项</div>
                    </div>
                </div>

                <div class="progress-section" id="progressSection" style="display: none;">
                    <h3>🔄 测试进度</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="current-test" id="currentTest" style="display: none;">
                        <h4>🔍 正在测试:</h4>
                        <div class="test-info-box">
                            <div class="test-site-name">
                                <span id="currentSiteName">网站名称</span>
                            </div>
                            <div class="test-url">
                                <span id="currentUrl">URL地址</span>
                            </div>
                            <div class="test-status">
                                状态: <span id="currentStatus" class="test-status-value">准备中...</span>
                            </div>
                            <div id="currentResponse" class="test-response" style="display: none;">
                                <strong>响应详情:</strong> <span id="responseDetails"></span>
                            </div>
                        </div>
                        <div class="test-controls">
                            <button class="btn btn-skip" onclick="skipCurrent()">⏭️ 跳过</button>
                            <button class="btn btn-invalid" onclick="markInvalid()">❌ 标记无效</button>
                            <button class="btn btn-internal" onclick="markInternal()">🏠 标记内网</button>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 30px;">
                    <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <h4 style="margin-bottom: 10px; color: #333;">⚙️ 测试设置</h4>
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="checkbox" id="skipInternalUrls" style="margin-right: 8px;">
                            <span>跳过内网网段测试（192.168.x.x, 10.x.x.x, 172.16-31.x.x, localhost等）</span>
                        </label>
                    </div>

                    <button class="btn" id="startBtn" onclick="startAnalysis()" disabled>🚀 开始分析</button>
                    <button class="btn" id="pauseBtn" onclick="pauseAnalysis()" disabled>⏸️ 暂停</button>
                    <button class="btn" id="exportBtn" onclick="exportReport()" disabled>📊 导出报告</button>
                </div>
            </div>

            <div class="right-panel">
                <h3>📋 实时结果</h3>
                <div class="results-section" id="resultsSection">
                    <div style="padding: 40px; text-align: center; color: #666;">
                        请先上传数据文件开始分析
                    </div>
                </div>

                <div class="report-section">
                    <h3>📈 分析报告</h3>
                    <div class="report-content" id="reportContent">
                        <p style="color: #666; text-align: center; padding: 20px;">
                            分析完成后将显示详细报告
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
