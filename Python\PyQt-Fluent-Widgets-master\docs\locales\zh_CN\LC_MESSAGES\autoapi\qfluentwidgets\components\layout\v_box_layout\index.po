# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON>o
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-22 20:49+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/layout/v_box_layout/index.rst:2
#: ********************************
msgid "v_box_layout"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/layout/v_box_layout/index.rst:8
#: ********************************
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/layout/v_box_layout/index.rst:18:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`VBoxLayout "
"<qfluentwidgets.components.layout.v_box_layout.VBoxLayout>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/layout/v_box_layout/index.rst:23
#: ../../source/autoapi/qfluentwidgets/components/layout/v_box_layout/index.rst:18:<autosummary>:1
#: ******************************** a71fb467c91b44a19e9fc7d1d89ee7e7
msgid "Vertical box layout"
msgstr "垂直布局"

#: ../../source/autoapi/qfluentwidgets/components/layout/v_box_layout/index.rst:21
#: ********************************
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QVBoxLayout`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/layout/v_box_layout/index.rst:27
#: ********************************
msgid "add widgets to layout"
msgstr "添加多个小部件到布局中"

#: ../../source/autoapi/qfluentwidgets/components/layout/v_box_layout/index.rst:32
#: ********************************
msgid "add widget to layout"
msgstr "添加小部件到布局中"

#: ../../source/autoapi/qfluentwidgets/components/layout/v_box_layout/index.rst:37
#: ********************************
msgid "remove widget from layout but not delete it"
msgstr "从布局中移除小部件"

#: ../../source/autoapi/qfluentwidgets/components/layout/v_box_layout/index.rst:42
#: ********************************
msgid "remove widget from layout and delete it"
msgstr "从布局中移除小部件并删除它"

#: ../../source/autoapi/qfluentwidgets/components/layout/v_box_layout/index.rst:47
#: ********************************
msgid "remove all widgets from layout"
msgstr "从布局中移除所有小部件"

