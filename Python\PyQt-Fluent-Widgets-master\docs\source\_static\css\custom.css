.sidebar-brand-text {
    font-size: 1.3rem;
}

#support>a {
    display: flex;
    justify-content: center;
}


@media (prefers-color-scheme: dark) {

    body:not([data-theme="light"]) .sig-inline,
    body:not([data-theme="light"]) code.literal {
        padding: 2px 5px;
    }

    body[data-theme="light"] .sig-inline,
    body[data-theme="light"] code.literal {
        background: white;
        padding: 2px 5px;
        color: #e74c3c;
    }

    body:not([data-theme="light"]) hr {
        border: none;
        border-bottom: 1px solid rgb(33 38 45);
    }

    body[data-theme="light"] hr {
        border: none;
        border-bottom: 1px solid rgb(216 222 228);
    }
}

@media (prefers-color-scheme: light) {

    body:not([data-theme="dark"]) .sig-inline,
    body:not([data-theme="dark"]) code.literal {
        background: white;
        padding: 2px 5px;
        color: #e74c3c;
    }

    body:not([data-theme="dark"]) hr {
        border: none;
        border-bottom: 1px solid rgb(216 222 228);
    }

    body[data-theme="dark"] hr {
        border: none;
        border-bottom: 1px solid rgb(33 38 45);
    }
}

article table.align-default {
    display: table;
    text-align: center;
    width: 100%;
}

table.docutils th {
    border-top: 1px solid var(--color-table-border);
}

table.docutils td,
table.docutils th {
    border-left: 1px solid var(--color-table-border);
    border-right: 1px solid var(--color-table-border);
}

a[data-fancybox] img {
    cursor: zoom-in;
}

a[data-fancybox]::after {
    display: none !important;
}

.fancybox-zoomIn {
    animation: 0.5s ease both fancybox-zoom-in;
}

.fancybox-zoomOut {
    animation: 0.5s ease both fancybox-zoom-out;
}

@keyframes fancybox-zoom-in {
    from {
        opacity: 0;
        transform: scale(0.75);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fancybox-zoom-out {
    to {
        opacity: 0;
        transform: scale(1.25);
    }
}