import flet as ft

def main(page: ft.Page):
    page.title = "Simple Calculator"
    page.window_width = 360
    page.window_height = 640
    page.padding = 50
    page.bgcolor = ft.colors.WHITE

    # Display
    display = ft.TextField(
        value="0",
        text_align=ft.TextAlign.END,
        read_only=True,
        width=300,
        height=80,
        border_radius=10,
        border_color=ft.colors.GREY_300,
        cursor_color=ft.colors.TRANSPARENT,
        cursor_width=0
    )

    # Buttons
    buttons = [
        ["7", "8", "9", "/"],
        ["4", "5", "6", "*"],
        ["1", "2", "3", "-"],
        ["C", "0", "=", "+"],
    ]

    def on_button_tap(e):
        button = e.control
        if button.data == "=":
            try:
                display.value = str(eval(display.value))
            except:
                display.value = "Error"
        elif button.data == "C":
            display.value = "0"
        else:
            if display.value == "0":
                display.value = ""
            display.value += button.data
        page.update()

    # Layout
    page.add(display)
    for row in buttons:
        page.add(
            ft.Row(
                [
                    ft.ElevatedButton(
                        text=button,
                        width=70,
                        height=70,
                        color=ft.colors.WHITE,
                        bgcolor=ft.colors.BLUE_GREY_200,
                        border_radius=10,
                        data=button,
                        on_click=on_button_tap,
                    )
                    for button in row
                ],
                alignment=ft.MainAxisAlignment.CENTER,
            )
        )

    page.update()

ft.app(target=main)
