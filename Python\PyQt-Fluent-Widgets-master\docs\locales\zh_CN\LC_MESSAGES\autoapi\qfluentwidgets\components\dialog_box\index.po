# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON>o
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-23 00:38+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:2
#: ********************************
msgid "dialog_box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:21
#: ********************************
msgid "Package Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:35:<autosummary>:1
#: ********************************
msgid ":py:obj:`ColorDialog <qfluentwidgets.components.dialog_box.ColorDialog>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:40
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:35:<autosummary>:1
#: ******************************** 5f55578538f74f39bfb0b2632af41c86
msgid "Color dialog"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:35:<autosummary>:1
#: ********************************
msgid ":py:obj:`Dialog <qfluentwidgets.components.dialog_box.Dialog>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:66
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:35:<autosummary>:1
#: ******************************** e7902bf20c31479eb9fa9ccbbe62caff
msgid "Dialog box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:35:<autosummary>:1
#: ********************************
msgid ":py:obj:`MessageBox <qfluentwidgets.components.dialog_box.MessageBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:84
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:35:<autosummary>:1
#: ******************************** 5e00d382105040088775d1e3ddaeefd2
msgid "Message box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:35:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`FolderListDialog "
"<qfluentwidgets.components.dialog_box.FolderListDialog>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:102
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:35:<autosummary>:1
#: ******************************** 8ab3c86a91d644acb040545820594ac9
msgid "Folder list dialog box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:35:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`MessageDialog "
"<qfluentwidgets.components.dialog_box.MessageDialog>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:113
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:35:<autosummary>:1
#: ******************************** 92ccb46f8a2e4e85a66bdc7b3a867e09
msgid "Win10 style message dialog box with a mask"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:38
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:100
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:111
#: ******************************** c8ce5ee0e53f4c1faedc3c519f6ceff3
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.dialog_box.mask_dialog_base.MaskDialogBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:48
#: ********************************
msgid "set color"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:53
#: ********************************
msgid "update style sheet"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:58
#: ********************************
msgid "fade in"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:64
#: ********************************
msgid "Bases: :py:obj:`qframelesswindow.FramelessDialog`, :py:obj:`Ui_MessageBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/index.rst:82
#: ********************************
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.dialog_box.mask_dialog_base.MaskDialogBase`,"
" :py:obj:`Ui_MessageBox`"
msgstr ""

