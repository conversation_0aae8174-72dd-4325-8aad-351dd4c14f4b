.. PyQt-Fluent-Widgets documentation master file, created by
   sphinx-quickstart on Tue Jan 17 15:31:54 2023.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

.. raw:: html

   <p align="center">
      <img width="18%" align="center" src="./_static/logo.png" alt="logo">
   </p>

   <h1 align="center">
      PyQt-Fluent-Widgets
   </h1>

   <hr/>

   <p align="center">
      A fluent design widgets library based on PyQt5
   </p>

   <p align="center">
      <a style="text-decoration:none">
         <img src="https://img.shields.io/badge/Platform-Win32%20|%20Linux%20|%20macOS-blue?color=#4ec820" alt="Platform Win32 | Linux | macOS"/>
      </a>

      <a style="text-decoration:none">
         <img src="https://static.pepy.tech/personalized-badge/pyqt-fluent-widgets?period=total&units=international_system&left_color=grey&right_color=brightgreen&left_text=Downloads" alt="Download"/>
      </a>

      <a style="text-decoration:none">
         <img src="https://img.shields.io/badge/License-GPLv3-blue?color=#4ec820" alt="GPLv3"/>
      </a>
   </p>

   <p align="center">
      <img src="./_static/Interface.jpg" alt="Interface">
   </p>


Welcome to PyQt-Fluent-Widgets's document!
===============================================
This document will show you all the features of PyQt-Fluent-Widgets and the best practice of it.

.. toctree::
   :maxdepth: 2
   :caption: Contents

   quick-start
   designer
   settings
   theme
   navigation
   icon
   support
   license
   gallery
   see-also
