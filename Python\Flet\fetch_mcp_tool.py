"""
Fetch MCP工具演示
专注于fetch功能的MCP工具实现，使用左右分栏布局
"""

import flet as ft
import requests
import json
import asyncio
from typing import Dict, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FetchMCPTool:
    def __init__(self, page: ft.Page):
        self.page = page
        self.page.title = "Fetch MCP工具演示"
        self.page.theme_mode = ft.ThemeMode.LIGHT
        self.page.window_width = 1200
        self.page.window_height = 700
        
        # API配置
        self.api_url = ""
        self.api_key = ""
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面 - 左右分栏布局"""
        
        # === 左侧配置面板 ===
        
        # 标题
        title = ft.Text(
            "Fetch MCP工具演示",
            size=24,
            weight=ft.FontWeight.BOLD,
            color=ft.Colors.BLUE_700
        )
        
        # LLM API配置
        self.api_url_field = ft.TextField(
            label="LLM API URL",
            hint_text="例如: https://api.openai.com/v1/chat/completions",
            width=450,
            value="https://api.openai.com/v1/chat/completions"
        )
        
        self.api_key_field = ft.TextField(
            label="API Key",
            hint_text="输入你的API密钥",
            password=True,
            width=450
        )
        
        # Fetch工具配置
        self.url_field = ft.TextField(
            label="目标URL",
            hint_text="例如: https://api.github.com/users/octocat",
            width=450,
            value="https://httpbin.org/json"
        )
        
        self.method_dropdown = ft.Dropdown(
            label="HTTP方法",
            width=150,
            value="GET",
            options=[
                ft.dropdown.Option("GET"),
                ft.dropdown.Option("POST"),
                ft.dropdown.Option("PUT"),
                ft.dropdown.Option("DELETE"),
            ]
        )
        
        self.headers_field = ft.TextField(
            label="请求头 (JSON格式)",
            hint_text='{"Content-Type": "application/json", "User-Agent": "MCP-Tool"}',
            multiline=True,
            min_lines=3,
            max_lines=4,
            width=450
        )
        
        self.body_field = ft.TextField(
            label="请求体 (JSON格式)",
            hint_text='{"key": "value", "message": "hello"}',
            multiline=True,
            min_lines=4,
            max_lines=5,
            width=450
        )
        
        # 执行按钮
        self.direct_button = ft.ElevatedButton(
            "🚀 直接执行Fetch",
            on_click=self.execute_direct_fetch,
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE,
            width=200
        )
        
        self.mcp_button = ft.ElevatedButton(
            "🤖 MCP协议调用",
            on_click=self.execute_mcp_fetch,
            bgcolor=ft.Colors.GREEN_600,
            color=ft.Colors.WHITE,
            width=200
        )
        
        # 左侧面板布局
        left_panel = ft.Container(
            content=ft.Column([
                title,
                ft.Divider(),
                
                ft.Text("🔧 LLM API 配置", size=16, weight=ft.FontWeight.BOLD),
                self.api_url_field,
                self.api_key_field,
                
                ft.Divider(),
                ft.Text("🌐 Fetch工具配置", size=16, weight=ft.FontWeight.BOLD),
                ft.Row([self.url_field]),
                ft.Row([self.method_dropdown]),
                self.headers_field,
                self.body_field,
                
                ft.Divider(),
                ft.Text("⚡ 执行操作", size=16, weight=ft.FontWeight.BOLD),
                ft.Column([
                    self.direct_button,
                    self.mcp_button
                ], spacing=10),
                
            ], spacing=10, scroll=ft.ScrollMode.AUTO),
            width=500,
            padding=20,
            bgcolor=ft.Colors.GREY_50,
            border_radius=10
        )
        
        # === 右侧结果面板 ===
        
        # 状态显示
        self.status_text = ft.Text(
            "🟢 就绪 - 请配置参数并执行",
            size=14,
            weight=ft.FontWeight.BOLD
        )
        
        # 结果显示区域
        self.result_text = ft.TextField(
            label="📊 执行结果",
            multiline=True,
            min_lines=25,
            max_lines=30,
            width=650,
            read_only=True,
            text_style=ft.TextStyle(font_family="Consolas")
        )
        
        # 右侧面板布局
        right_panel = ft.Container(
            content=ft.Column([
                ft.Text("📈 执行结果与输出", size=18, weight=ft.FontWeight.BOLD),
                self.status_text,
                ft.Divider(),
                self.result_text,
            ], spacing=10),
            width=680,
            padding=20,
            bgcolor=ft.Colors.WHITE,
            border_radius=10
        )
        
        # === 主布局 ===
        self.page.add(
            ft.Container(
                content=ft.Row([
                    left_panel,
                    ft.VerticalDivider(width=2, color=ft.Colors.GREY_300),
                    right_panel
                ], spacing=10, expand=True),
                padding=10
            )
        )
    
    async def execute_direct_fetch(self, e):
        """直接执行fetch请求"""
        try:
            self.status_text.value = "🔄 正在执行直接fetch请求..."
            self.status_text.color = ft.Colors.ORANGE_600
            self.page.update()
            
            # 获取配置参数
            url = self.url_field.value
            method = self.method_dropdown.value
            
            # 解析headers
            headers = {}
            if self.headers_field.value.strip():
                try:
                    headers = json.loads(self.headers_field.value)
                except json.JSONDecodeError:
                    raise ValueError("请求头格式错误，请使用有效的JSON格式")
            
            # 解析body
            data = None
            if self.body_field.value.strip() and method in ["POST", "PUT"]:
                try:
                    data = json.loads(self.body_field.value)
                except json.JSONDecodeError:
                    raise ValueError("请求体格式错误，请使用有效的JSON格式")
            
            # 执行HTTP请求
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                json=data,
                timeout=30
            )
            
            # 构建结果
            result = {
                "execution_type": "直接执行",
                "tool": "fetch",
                "timestamp": self.get_timestamp(),
                "request": {
                    "url": url,
                    "method": method,
                    "headers": headers,
                    "body": data
                },
                "response": {
                    "success": True,
                    "status_code": response.status_code,
                    "status_text": response.reason,
                    "headers": dict(response.headers),
                    "content_length": len(response.text),
                    "content": response.text[:1000] + "..." if len(response.text) > 1000 else response.text
                }
            }
            
            # 尝试解析JSON响应
            try:
                result["response"]["json"] = response.json()
            except:
                result["response"]["json"] = None
            
            self.result_text.value = json.dumps(result, indent=2, ensure_ascii=False)
            self.status_text.value = f"✅ 直接执行成功 - 状态码: {response.status_code}"
            self.status_text.color = ft.Colors.GREEN_600
            
        except Exception as ex:
            error_result = {
                "execution_type": "直接执行",
                "tool": "fetch",
                "timestamp": self.get_timestamp(),
                "request": {
                    "url": self.url_field.value,
                    "method": self.method_dropdown.value
                },
                "response": {
                    "success": False,
                    "error": str(ex),
                    "error_type": type(ex).__name__
                }
            }
            
            self.result_text.value = json.dumps(error_result, indent=2, ensure_ascii=False)
            self.status_text.value = f"❌ 直接执行失败: {str(ex)}"
            self.status_text.color = ft.Colors.RED_600
            logger.error(f"直接fetch执行失败: {ex}")
        
        self.page.update()
    
    async def execute_mcp_fetch(self, e):
        """通过MCP协议调用LLM执行fetch"""
        try:
            self.status_text.value = "🤖 正在通过MCP协议调用LLM..."
            self.status_text.color = ft.Colors.ORANGE_600
            self.page.update()
            
            if not self.api_url_field.value or not self.api_key_field.value:
                raise ValueError("请先配置LLM API URL和API Key")
            
            # 构建MCP工具调用的prompt
            mcp_prompt = self.build_mcp_prompt()
            
            # 调用LLM API
            llm_response = await self.call_llm_api(mcp_prompt)
            
            # 构建MCP调用结果
            result = {
                "execution_type": "MCP协议调用",
                "tool": "fetch",
                "timestamp": self.get_timestamp(),
                "mcp_request": {
                    "tool_name": "fetch",
                    "parameters": self.get_fetch_config(),
                    "prompt": mcp_prompt
                },
                "llm_response": llm_response,
                "mcp_workflow": [
                    "1. 用户配置fetch参数",
                    "2. 构建MCP工具调用prompt",
                    "3. 发送prompt到LLM API",
                    "4. LLM理解并模拟工具执行",
                    "5. 返回结构化响应"
                ]
            }
            
            self.result_text.value = json.dumps(result, indent=2, ensure_ascii=False)
            self.status_text.value = "✅ MCP协议调用成功"
            self.status_text.color = ft.Colors.GREEN_600
            
        except Exception as ex:
            error_result = {
                "execution_type": "MCP协议调用",
                "tool": "fetch",
                "timestamp": self.get_timestamp(),
                "error": {
                    "success": False,
                    "message": str(ex),
                    "type": type(ex).__name__
                }
            }
            
            self.result_text.value = json.dumps(error_result, indent=2, ensure_ascii=False)
            self.status_text.value = f"❌ MCP调用失败: {str(ex)}"
            self.status_text.color = ft.Colors.RED_600
            logger.error(f"MCP调用失败: {ex}")
        
        self.page.update()
    
    def get_fetch_config(self) -> Dict[str, Any]:
        """获取当前fetch配置"""
        config = {
            "url": self.url_field.value,
            "method": self.method_dropdown.value,
            "headers": self.headers_field.value,
            "body": self.body_field.value
        }
        return config
    
    def build_mcp_prompt(self) -> str:
        """构建MCP工具调用的prompt"""
        config = self.get_fetch_config()
        
        prompt = f"""
请使用fetch工具获取网络资源。以下是详细的配置信息：

工具名称: fetch
描述: 执行HTTP请求获取网络资源

参数配置:
- URL: {config['url']}
- HTTP方法: {config['method']}
- 请求头: {config['headers'] or '无'}
- 请求体: {config['body'] or '无'}

请执行fetch工具并返回详细的结果，包括：
1. 请求的详细信息
2. 响应状态码和状态文本
3. 响应头信息
4. 响应内容（如果是JSON，请解析并格式化）
5. 执行时间和其他元数据

请以结构化的JSON格式返回结果。
"""
        return prompt
    
    async def call_llm_api(self, prompt: str) -> str:
        """调用LLM API"""
        headers = {
            "Authorization": f"Bearer {self.api_key_field.value}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "gpt-3.5-turbo",
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 2000,
            "temperature": 0.1
        }
        
        response = requests.post(
            self.api_url_field.value,
            headers=headers,
            json=payload,
            timeout=60
        )
        
        if response.status_code != 200:
            raise Exception(f"LLM API调用失败: {response.status_code} - {response.text}")
        
        result = response.json()
        return result["choices"][0]["message"]["content"]
    
    def get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def main(page: ft.Page):
    FetchMCPTool(page)

if __name__ == "__main__":
    print("🚀 启动Fetch MCP工具演示...")
    print("📋 功能说明:")
    print("  1. 直接执行 - 本地调用fetch功能")
    print("  2. MCP协议调用 - 通过LLM API模拟MCP工具调用")
    print("  3. 左右分栏布局 - 配置在左，结果在右")
    print("\n🌐 应用正在启动，请在浏览器中查看界面...")
    
    ft.app(target=main)
