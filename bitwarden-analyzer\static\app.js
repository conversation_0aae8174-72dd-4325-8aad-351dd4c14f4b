// Bitwarden 数据筛查工具 - Python版前端
class BitwardenAnalyzerApp {
    constructor() {
        this.currentTaskId = null;
        this.uploadedFile = null;
        this.initializeEventListeners();
        this.loadTasks();
        
        // 定期刷新任务状态
        setInterval(() => {
            this.loadTasks();
            if (this.currentTaskId) {
                this.loadTaskDetails(this.currentTaskId);
            }
        }, 2000);
    }

    initializeEventListeners() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');

        // 文件上传事件
        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        fileInput.addEventListener('change', this.handleFileSelect.bind(this));
    }

    handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }

    handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            this.processFile(file);
        }
    }

    async processFile(file) {
        this.uploadedFile = file;
        this.showMessage(`文件 "${file.name}" 已选择，点击开始分析`, 'info');
        document.getElementById('startBtn').disabled = false;
    }

    async loadTasks() {
        try {
            const response = await fetch('/api/tasks');
            const tasks = await response.json();
            this.displayTasks(tasks);
        } catch (error) {
            console.error('加载任务失败:', error);
        }
    }

    displayTasks(tasks) {
        const taskList = document.getElementById('taskList');
        
        if (tasks.length === 0) {
            taskList.innerHTML = `
                <div style="padding: 40px; text-align: center; color: #666;">
                    暂无任务，请上传数据文件开始分析
                </div>
            `;
            return;
        }

        taskList.innerHTML = tasks.map(task => {
            const statusClass = this.getStatusClass(task.status);
            const statusText = this.getStatusText(task.status);

            return `
                <div class="task-item" onclick="app.selectTask('${task.id}')">
                    <div class="task-name">${task.name}</div>
                    <div class="task-status ${statusClass}">
                        ${statusText} • ${task.tested_items || 0}/${task.total_items || 0} 已测试
                    </div>
                    <div style="font-size: 0.8em; color: #999; margin-top: 5px;">
                        创建时间: ${new Date(task.created_at).toLocaleString()}
                    </div>
                    <div class="task-actions">
                        <button class="btn btn-small btn-delete" onclick="app.deleteTask('${task.id}'); event.stopPropagation();">🗑️ 删除</button>
                    </div>
                </div>
            `;
        }).join('');
    }

    getStatusClass(status) {
        const statusMap = {
            'created': '',
            'running': 'status-running',
            'completed': 'status-completed',
            'failed': 'status-failed',
            'paused': 'status-running'
        };
        return statusMap[status] || '';
    }

    getStatusText(status) {
        const statusMap = {
            'created': '已创建',
            'running': '运行中',
            'completed': '已完成',
            'failed': '失败',
            'paused': '已暂停'
        };
        return statusMap[status] || status;
    }

    async selectTask(taskId) {
        this.currentTaskId = taskId;
        await this.loadTaskDetails(taskId);

        // 高亮选中的任务
        document.querySelectorAll('.task-item').forEach(item => {
            item.style.backgroundColor = '';
        });

        // 找到对应的任务项并高亮
        const taskItems = document.querySelectorAll('.task-item');
        taskItems.forEach(item => {
            if (item.onclick && item.onclick.toString().includes(taskId)) {
                item.style.backgroundColor = '#e3f2fd';
            }
        });
    }

    async deleteTask(taskId) {
        if (!confirm('确定要删除这个任务吗？此操作不可恢复。')) {
            return;
        }

        try {
            const response = await fetch(`/api/tasks/${taskId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.showMessage('任务已删除', 'success');
                // 如果删除的是当前选中的任务，清空选择
                if (this.currentTaskId === taskId) {
                    this.currentTaskId = null;
                    document.getElementById('resultsSection').innerHTML = `
                        <div style="padding: 40px; text-align: center; color: #666;">
                            选择任务查看结果
                        </div>
                    `;
                }
                // 重新加载任务列表
                this.loadTasks();
            } else {
                this.showMessage('删除失败', 'error');
            }
        } catch (error) {
            console.error('删除任务失败:', error);
            this.showMessage('删除失败', 'error');
        }
    }

    async loadTaskDetails(taskId) {
        try {
            const response = await fetch(`/api/tasks/${taskId}`);
            const task = await response.json();
            
            this.updateStats(task);
            this.displayResults(task.results || []);
            
            // 更新按钮状态
            this.updateButtonStates(task.status);
            
        } catch (error) {
            console.error('加载任务详情失败:', error);
        }
    }

    updateStats(task) {
        document.getElementById('totalCount').textContent = task.total_items || 0;
        document.getElementById('testedCount').textContent = task.tested_items || 0;
        document.getElementById('successCount').textContent = task.success_count || 0;
        // 这里可以添加重复项统计
        document.getElementById('duplicateCount').textContent = 0;

        // 更新当前测试状态
        this.updateCurrentTestDisplay(task);
    }

    updateCurrentTestDisplay(task) {
        const currentTestSection = document.getElementById('currentTestSection');
        const currentSiteName = document.getElementById('currentSiteName');
        const currentUrl = document.getElementById('currentUrl');
        const currentStatus = document.getElementById('currentStatus');

        if (task.status === 'running' && task.current_testing_url) {
            currentTestSection.style.display = 'block';
            currentSiteName.textContent = task.current_testing_name || '未命名';
            currentUrl.textContent = task.current_testing_url || '无URL';
            currentStatus.textContent = task.current_testing_status || '准备中...';

            // 根据状态设置颜色
            currentStatus.className = 'test-status-value';
            if (task.current_testing_status?.includes('成功')) {
                currentStatus.style.color = '#28a745';
            } else if (task.current_testing_status?.includes('失败')) {
                currentStatus.style.color = '#dc3545';
            } else if (task.current_testing_status?.includes('测试')) {
                currentStatus.style.color = '#007bff';
            } else {
                currentStatus.style.color = '#6c757d';
            }
        } else {
            currentTestSection.style.display = 'none';
        }
    }

    updateButtonStates(status) {
        const startBtn = document.getElementById('startBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const exportBtn = document.getElementById('exportBtn');
        const currentTestSection = document.getElementById('currentTestSection');

        if (status === 'running') {
            startBtn.disabled = true;
            pauseBtn.disabled = false;
            exportBtn.disabled = true;
            if (currentTestSection) currentTestSection.style.display = 'block';
        } else if (status === 'completed') {
            startBtn.disabled = false;
            pauseBtn.disabled = true;
            exportBtn.disabled = false;
            if (currentTestSection) currentTestSection.style.display = 'none';
        } else {
            startBtn.disabled = !this.uploadedFile;
            pauseBtn.disabled = true;
            exportBtn.disabled = status !== 'completed';
            if (currentTestSection) currentTestSection.style.display = 'none';
        }
    }

    displayResults(results) {
        const resultsSection = document.getElementById('resultsSection');
        
        if (results.length === 0) {
            resultsSection.innerHTML = `
                <div style="padding: 40px; text-align: center; color: #666;">
                    暂无结果
                </div>
            `;
            return;
        }

        resultsSection.innerHTML = results.map(result => {
            const statusClass = `status-${result.status}`;
            const statusText = this.getResultStatusText(result.status);

            // 处理URL显示
            let urlDisplay = result.url || '无URL';
            if (result.url && (result.url.startsWith('http://') || result.url.startsWith('https://'))) {
                urlDisplay = `<span class="clickable-url" onclick="window.open('${result.url}', '_blank')">${result.url}</span>`;
            }

            return `
                <div class="result-item">
                    <div style="flex: 1;">
                        <strong>${result.name || '未命名'}</strong><br>
                        <small>${urlDisplay}</small><br>
                        <small style="color: #666;">${result.message || ''}</small>
                        ${result.status_code ? `<br><small style="color: #888;">状态码: ${result.status_code}</small>` : ''}
                        ${result.title ? `<br><small style="color: #555;" title="${result.title}">标题: ${result.title.length > 50 ? result.title.substring(0, 50) + '...' : result.title}</small>` : ''}
                    </div>
                    <div style="text-align: right; min-width: 120px;">
                        <span class="${statusClass}">${statusText}</span><br>
                        <small style="color: #999;">${new Date(result.timestamp).toLocaleTimeString()}</small>
                    </div>
                </div>
            `;
        }).join('');

        // 自动滚动到最后一项
        resultsSection.scrollTop = resultsSection.scrollHeight;
    }

    getResultStatusText(status) {
        const statusMap = {
            'success': '✅ 成功',
            'failed': '❌ 失败',
            'skipped': '⏭️ 跳过',
            'invalid': '🚫 无效',
            'internal': '🏠 内网'
        };
        return statusMap[status] || '❓ 未知';
    }

    async startAnalysis() {
        if (!this.uploadedFile) {
            this.showMessage('请先选择数据文件', 'error');
            return;
        }

        try {
            console.log('开始创建分析任务...');

            // 获取设置
            const settings = {
                skipInternalUrls: document.getElementById('skipInternalUrls').checked,
                enableDetailedLogging: document.getElementById('enableDetailedLogging').checked
            };

            // 创建任务
            const taskResponse = await fetch('/api/tasks', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: `分析_${this.uploadedFile.name}_${new Date().toLocaleString()}`,
                    settings: settings
                })
            });

            if (!taskResponse.ok) {
                throw new Error('创建任务失败');
            }

            const taskResult = await taskResponse.json();
            this.currentTaskId = taskResult.task_id;
            console.log('任务创建成功:', this.currentTaskId);

            // 上传文件并开始分析
            const formData = new FormData();
            formData.append('file', this.uploadedFile);
            formData.append('task_id', this.currentTaskId);

            console.log('开始上传文件...');
            const uploadResponse = await fetch('/api/upload', {
                method: 'POST',
                body: formData
            });

            if (uploadResponse.ok) {
                this.showMessage('分析已开始', 'success');
                console.log('文件上传成功，分析已开始');

                // 立即更新任务列表和选择当前任务
                setTimeout(() => {
                    this.loadTasks();
                    this.selectTask(this.currentTaskId);
                }, 1000);
            } else {
                const errorText = await uploadResponse.text();
                console.error('上传失败:', errorText);
                this.showMessage('上传文件失败', 'error');
            }

        } catch (error) {
            console.error('开始分析失败:', error);
            this.showMessage(`开始分析失败: ${error.message}`, 'error');
        }
    }

    async pauseAnalysis() {
        if (!this.currentTaskId) return;

        try {
            const response = await fetch(`/api/tasks/${this.currentTaskId}/pause`, {
                method: 'POST'
            });

            if (response.ok) {
                this.showMessage('分析已暂停', 'info');
            }
        } catch (error) {
            console.error('暂停分析失败:', error);
        }
    }

    async exportReport() {
        if (!this.currentTaskId) return;

        try {
            const response = await fetch(`/api/tasks/${this.currentTaskId}/export`);
            const blob = await response.blob();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `bitwarden-analysis-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            this.showMessage('报告已导出', 'success');
        } catch (error) {
            console.error('导出报告失败:', error);
            this.showMessage('导出报告失败', 'error');
        }
    }

    async skipCurrent() {
        if (!this.currentTaskId) {
            this.showMessage('请先选择任务', 'error');
            return;
        }

        try {
            const response = await fetch(`/api/tasks/${this.currentTaskId}/skip`, {
                method: 'POST'
            });

            if (response.ok) {
                this.showMessage('已跳过当前项目', 'info');
            } else {
                this.showMessage('跳过失败', 'error');
            }
        } catch (error) {
            console.error('跳过失败:', error);
            this.showMessage('跳过失败', 'error');
        }
    }

    async markInvalid() {
        if (!this.currentTaskId) {
            this.showMessage('请先选择任务', 'error');
            return;
        }

        try {
            const response = await fetch(`/api/tasks/${this.currentTaskId}/mark_invalid`, {
                method: 'POST'
            });

            if (response.ok) {
                this.showMessage('已标记为无效', 'info');
            } else {
                this.showMessage('标记失败', 'error');
            }
        } catch (error) {
            console.error('标记失败:', error);
            this.showMessage('标记失败', 'error');
        }
    }

    async markInternal() {
        if (!this.currentTaskId) {
            this.showMessage('请先选择任务', 'error');
            return;
        }

        try {
            const response = await fetch(`/api/tasks/${this.currentTaskId}/mark_internal`, {
                method: 'POST'
            });

            if (response.ok) {
                this.showMessage('已标记为内网', 'info');
            } else {
                this.showMessage('标记失败', 'error');
            }
        } catch (error) {
            console.error('标记失败:', error);
            this.showMessage('标记失败', 'error');
        }
    }

    showMessage(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// 全局函数
let app;

function startAnalysis() {
    app.startAnalysis();
}

function pauseAnalysis() {
    app.pauseAnalysis();
}

function exportReport() {
    app.exportReport();
}

function skipCurrent() {
    app.skipCurrent();
}

function markInvalid() {
    app.markInvalid();
}

function markInternal() {
    app.markInternal();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    app = new BitwardenAnalyzerApp();
});
