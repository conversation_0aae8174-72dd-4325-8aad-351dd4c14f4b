#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API脚本
"""

import requests
import json

def test_api():
    base_url = "http://localhost:5000"
    
    print("测试API连接...")
    
    # 1. 测试获取任务列表
    try:
        response = requests.get(f"{base_url}/api/tasks")
        print(f"获取任务列表: {response.status_code}")
        if response.status_code == 200:
            tasks = response.json()
            print(f"当前任务数量: {len(tasks)}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"连接失败: {e}")
        return
    
    # 2. 测试创建任务
    try:
        task_data = {
            "name": "API测试任务",
            "settings": {
                "skipInternalUrls": False,
                "enableDetailedLogging": True
            }
        }
        
        response = requests.post(f"{base_url}/api/tasks", 
                               json=task_data,
                               headers={'Content-Type': 'application/json'})
        print(f"创建任务: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['task_id']
            print(f"任务ID: {task_id}")
            
            # 3. 测试上传文件
            with open('test-python.csv', 'rb') as f:
                files = {'file': f}
                data = {'task_id': task_id}
                
                response = requests.post(f"{base_url}/api/upload", 
                                       files=files, 
                                       data=data)
                print(f"上传文件: {response.status_code}")
                print(f"响应: {response.text}")
                
        else:
            print(f"创建任务失败: {response.text}")
            
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == '__main__':
    test_api()
