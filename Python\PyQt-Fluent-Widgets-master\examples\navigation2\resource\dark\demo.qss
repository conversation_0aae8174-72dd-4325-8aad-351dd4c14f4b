Widget > Q<PERSON>abel {
    font: 24px 'Segoe UI', 'Microsoft YaHei';
}

Widget {
    border-left: 1px solid rgb(29, 29, 29);
    background-color: rgb(39, 39, 39);
}


Window {
    background-color: rgb(32, 32, 32);
}

CustomTitleBar {
    background-color: transparent;
}

CustomTitleBar > QLabel,
Widget > QLabel {
    color: white;
}


CustomTitleBar>QLabel#titleLabel {
    background: transparent;
    font: 13px 'Segoe UI';
    padding: 0 4px
}

MinimizeButton {
    qproperty-normalColor: white;
    qproperty-normalBackgroundColor: transparent;
    qproperty-hoverColor: white;
    qproperty-hoverBackgroundColor: rgba(255, 255, 255, 26);
    qproperty-pressedColor: white;
    qproperty-pressedBackgroundColor: rgba(255, 255, 255, 51)
}


MaximizeButton {
    qproperty-normalColor: white;
    qproperty-normalBackgroundColor: transparent;
    qproperty-hoverColor: white;
    qproperty-hoverBackgroundColor: rgba(255, 255, 255, 26);
    qproperty-pressedColor: white;
    qproperty-pressedBackgroundColor: rgba(255, 255, 255, 51)
}

CloseButton {
    qproperty-normalColor: white;
    qproperty-normalBackgroundColor: transparent;
}

