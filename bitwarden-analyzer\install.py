#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bitwarden 数据筛查工具安装脚本
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        print(f"当前版本: {platform.python_version()}")
        return False
    print(f"✅ Python版本: {platform.python_version()}")
    return True

def install_requirements():
    """安装依赖包"""
    print("📦 正在安装依赖包...")
    try:
        # 先升级pip
        print("🔄 升级pip...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'])

        # 安装依赖
        print("📦 安装项目依赖...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ 依赖包安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        print("🔧 尝试手动安装...")

        # 尝试逐个安装核心依赖
        core_packages = ['Flask', 'requests', 'Werkzeug']
        optional_packages = ['Flask-CORS']

        success = True
        for package in core_packages:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {package} 安装失败")
                success = False

        for package in optional_packages:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"⚠️ {package} 安装失败（可选依赖）")

        return success

def create_directories():
    """创建必要的目录"""
    directories = ['uploads', 'results', 'templates', 'static']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ 创建目录: {directory}")

def main():
    print("=" * 60)
    print("🔐 Bitwarden 数据筛查工具 - 安装程序")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    # 安装依赖
    if not install_requirements():
        sys.exit(1)
    
    print("-" * 60)
    print("🎉 安装完成!")
    print("📝 使用说明:")
    print("  1. 运行: python run.py")
    print("  2. 打开浏览器访问: http://localhost:5000")
    print("  3. 上传Bitwarden导出文件开始分析")
    print("-" * 60)

if __name__ == '__main__':
    main()
