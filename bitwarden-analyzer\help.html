<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>使用说明 - Bitwarden 数据筛查工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }

        .content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        h2 {
            color: #4facfe;
            margin-top: 30px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e0e0e0;
        }

        h3 {
            color: #666;
            margin-top: 20px;
            margin-bottom: 10px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #4facfe;
        }

        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .step {
            background: #f0f8ff;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #4facfe;
        }

        .step-number {
            background: #4facfe;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }

        .code-block {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .tip {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .format-example {
            background: white;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Bitwarden 数据筛查工具</h1>
            <p>使用说明与操作指南</p>
            <a href="index.html" class="btn">返回主页</a>
        </div>

        <div class="content">
            <h2>🎯 工具功能</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">🔍</div>
                    <h3>链接有效性检测</h3>
                    <p>自动测试每个保存的网站链接是否可访问，支持手动跳过和标记功能。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔄</div>
                    <h3>重复项检测</h3>
                    <p>检测重复的域名和密码，帮助您清理和整理密码库。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>详细报告</h3>
                    <p>生成完整的分析报告，提供清理建议和导出功能。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🛡️</div>
                    <h3>本地处理</h3>
                    <p>所有数据处理都在本地进行，确保您的密码信息安全。</p>
                </div>
            </div>

            <h2>📋 使用步骤</h2>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>导出 Bitwarden 数据</strong>
                <p>从 Bitwarden 应用中导出您的密码数据。支持 JSON、CSV 或 TXT 格式。</p>
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>上传数据文件</strong>
                <p>点击上传区域或拖拽文件到工具中。工具会自动解析数据并显示统计信息。</p>
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <strong>开始分析</strong>
                <p>点击"开始分析"按钮，工具会逐个测试每个链接的可访问性。</p>
            </div>

            <div class="step">
                <span class="step-number">4</span>
                <strong>手动操作</strong>
                <p>在测试过程中，您可以跳过、标记无效网站或标记内网网站。</p>
            </div>

            <div class="step">
                <span class="step-number">5</span>
                <strong>查看报告</strong>
                <p>分析完成后查看详细报告，并导出结果用于后续处理。</p>
            </div>

            <h2>📁 支持的文件格式</h2>

            <h3>Bitwarden JSON 格式</h3>
            <div class="format-example">
                <div class="code-block">
{
  "items": [
    {
      "name": "网站名称",
      "login": {
        "username": "用户名",
        "password": "密码",
        "uris": [{"uri": "https://example.com"}]
      }
    }
  ]
}
                </div>
            </div>

            <h3>CSV 格式</h3>
            <div class="format-example">
                <div class="code-block">
name,url,username,password
网站名称,https://example.com,用户名,密码
                </div>
            </div>

            <h3>TXT 格式</h3>
            <div class="format-example">
                <div class="code-block">
https://example.com
https://another-site.com
                </div>
            </div>

            <h2>⚡ 手动操作选项</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">⏭️</div>
                    <h3>跳过</h3>
                    <p>跳过当前网站的测试，继续下一个。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">❌</div>
                    <h3>标记无效</h3>
                    <p>将网站标记为无效，建议从密码库中删除。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🏠</div>
                    <h3>标记内网</h3>
                    <p>将网站标记为内网网站，建议单独管理。</p>
                </div>
            </div>

            <h2>🔒 安全说明</h2>
            
            <div class="warning">
                <strong>⚠️ 重要提醒</strong><br>
                • 所有数据处理都在本地浏览器中进行<br>
                • 不会将您的密码数据发送到任何服务器<br>
                • 建议在分析完成后及时删除上传的数据文件
            </div>

            <div class="tip">
                <strong>💡 使用建议</strong><br>
                • 在进行任何清理操作前，请备份您的 Bitwarden 数据<br>
                • 如果数据量很大，建议分批上传和分析<br>
                • 确保网络连接稳定，某些网站可能因防火墙限制无法访问
            </div>

            <h2>🛠️ 故障排除</h2>
            
            <h3>链接测试失败</h3>
            <ul>
                <li>某些网站可能阻止 HEAD 请求</li>
                <li>CORS 策略可能阻止跨域请求</li>
                <li>网络超时或连接问题</li>
            </ul>

            <h3>文件解析失败</h3>
            <ul>
                <li>检查文件格式是否正确</li>
                <li>确保 JSON 文件语法正确</li>
                <li>CSV 文件应包含正确的列标题</li>
            </ul>

            <div style="text-align: center; margin-top: 40px;">
                <a href="index.html" class="btn">开始使用工具</a>
            </div>
        </div>
    </div>
</body>
</html>
