#!/usr/bin/env python3
"""
Tailscale状态查看工具的本地HTTP服务器
用于调用系统命令并返回结果给前端页面

使用方法：
1. 运行此脚本：python tailscale-server.py
2. 打开 tailscale-status.html 页面
3. 点击执行按钮即可调用真实的tailscale命令

注意：需要确保系统已安装tailscale并且在PATH中可用
"""

import subprocess
import json
import sys
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import webbrowser
import os

class TailscaleHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/api/tailscale-status':
            self.handle_tailscale_status()
        else:
            self.send_error(404, "Not Found")
    
    def handle_tailscale_status(self):
        """处理tailscale status命令"""
        try:
            # 执行tailscale status命令
            result = subprocess.run(
                ['tailscale', 'status'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                output = result.stdout
                if not output.strip():
                    output = "Tailscale未运行或无设备连接"
            else:
                output = f"命令执行失败: {result.stderr}"
            
            # 返回JSON响应
            response_data = {
                'success': True,
                'output': output,
                'returncode': result.returncode
            }
            
        except subprocess.TimeoutExpired:
            response_data = {
                'success': False,
                'output': "命令执行超时",
                'error': 'timeout'
            }
        except FileNotFoundError:
            response_data = {
                'success': False,
                'output': "未找到tailscale命令，请确保已安装Tailscale并添加到PATH",
                'error': 'command_not_found'
            }
        except Exception as e:
            response_data = {
                'success': False,
                'output': f"执行命令时发生错误: {str(e)}",
                'error': 'unknown'
            }
        
        # 设置响应头
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        # 发送响应数据
        self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))
    
    def do_OPTIONS(self):
        """处理CORS预检请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{self.date_time_string()}] {format % args}")

def start_server(port=8080):
    """启动HTTP服务器"""
    server_address = ('localhost', port)
    httpd = HTTPServer(server_address, TailscaleHandler)
    
    print(f"🚀 Tailscale状态查看服务器已启动")
    print(f"📡 服务地址: http://localhost:{port}")
    print(f"🔗 API端点: http://localhost:{port}/api/tailscale-status")
    print(f"📄 前端页面: file://{os.path.abspath('tailscale-status.html')}")
    print("=" * 60)
    print("💡 使用说明:")
    print("1. 保持此服务器运行")
    print("2. 在浏览器中打开 tailscale-status.html")
    print("3. 点击执行按钮即可调用真实的tailscale命令")
    print("4. 按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.shutdown()

def open_browser_delayed():
    """延迟打开浏览器"""
    import time
    time.sleep(1)
    html_path = os.path.abspath('tailscale-status.html')
    if os.path.exists(html_path):
        webbrowser.open(f'file://{html_path}')
        print(f"🌐 已自动打开浏览器: {html_path}")

if __name__ == '__main__':
    port = 8080
    
    # 检查端口参数
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("错误: 端口号必须是数字")
            sys.exit(1)
    
    # 在后台线程中延迟打开浏览器
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 启动服务器
    start_server(port)
