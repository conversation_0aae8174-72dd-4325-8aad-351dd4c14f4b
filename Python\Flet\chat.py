import flet as ft
# 1.0
# def main(page):
#     chat = ft.Column()
#     new_message = ft.TextField()


#     def send_click(e):
#         chat.controls.append(ft.Text(new_message.value))
#         new_message.value=''
#         page.update()

#     page.add(
#         chat,
#         ft.Row(controls=[new_message, ft.ElevatedButton('send', on_click=send_click)]
#                )
#     )

def main(page):
    def send_click(e):
        chat.controls.append(ft.Text(new_message.value))
        new_message.value=''
        page.update()
        
    chat = ft.ListView(
        expand = True,
        spacing=10,
        auto_scroll=True
    )
    new_message = ft.TextField(
        hint_text="Write a message...",
        autofocus=True,
        shift_enter=True,
        min_lines=1,
        max_lines=5,
        filled=True,
        expand=True,
        on_submit=send_click,
    )

    


    page.add(
        ft.Container(
            content=chat,
            border=ft.border.all(1, ft.colors.OUTLINE),
            border_radius=5,
            padding=10,
            expand=True,
        ),
        ft.Row(
            [
                new_message,
                ft.IconButton(
                    icon=ft.icons.SEND_ROUNDED,
                    tooltip="Send message",
                    on_click=send_click,
                )
            ]
        )
    )

ft.app(target=main)