// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Form submission handlers
    const siliconForm = document.getElementById('silicon-form');
    const hyperbolicForm = document.getElementById('hyperbolic-form');

    siliconForm.addEventListener('submit', function(e) {
        e.preventDefault();
        checkBalance('silicon');
    });

    hyperbolicForm.addEventListener('submit', function(e) {
        e.preventDefault();
        checkBalance('hyperbolic');
    });
});

// API endpoint and configuration
const API_CONFIG = {
    silicon: {
        // API endpoint
        endpoint: 'https://api.siliconflow.cn/v1/user/info',
        // Method to format the request
        formatRequest: function(address) {
            return {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${address}`
                }
            };
        },
        // Method to parse the response
        parseResponse: function(data) {
            // Check if the API response is successful
            if (data.code !== 20000 || !data.status) {
                throw new Error(data.message || '获取余额失败');
            }

            // Extract balance information from the data object
            const balanceData = data.data;
            // Parse balance value
            const totalBalance = parseFloat(balanceData.totalBalance || 0);

            return {
                balance: totalBalance,
                userName: balanceData.name || '用户',
                userId: balanceData.id || ''
            };
        }
    },
    hyperbolic: {
        // Hyperbolic API endpoint
        endpoint: 'https://api.hyperbolic.xyz/billing/get_current_balance',
        // Method to format the request
        formatRequest: function(address) {
            return {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${address}`,
                    'Content-Type': 'application/json'
                }
            };
        },
        // Method to parse the response
        parseResponse: function(data) {
            // 根据实际API响应格式调整
            if (!data || data.error) {
                throw new Error(data.error || '获取余额失败');
            }

            // 假设API返回的余额在data.balance字段
            const balance = parseFloat(data.balance || 0);

            return {
                balance: balance,
                userName: data.user || '用户',
                userId: data.id || ''
            };
        }
    }
};

// Function to check API balance
function checkBalance(tokenType) {
    const addressInput = document.getElementById(`${tokenType}-address`);
    const resultContainer = document.getElementById(`${tokenType}-result`);
    const loader = document.getElementById(`${tokenType}-loader`);
    const balanceDisplay = document.getElementById(`${tokenType}-balance-display`);
    const address = addressInput.value.trim();

    // Validate token
    if (!validateAddress(address)) {
        showError(addressInput, '请输入有效的API令牌');
        return;
    }

    // Show result container and loader
    resultContainer.style.display = 'block';
    loader.style.display = 'flex';
    balanceDisplay.style.display = 'none';

    // Get API configuration
    const apiConfig = API_CONFIG[tokenType];
    const apiUrl = apiConfig.endpoint;

    // Make the API request
    fetch(apiUrl, apiConfig.formatRequest(address))
        .then(response => {
            if (!response.ok) {
                throw new Error(`请求失败，状态码: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Parse the response
            const result = apiConfig.parseResponse(data);

            // Hide loader and show balance
            loader.style.display = 'none';
            balanceDisplay.style.display = 'block';

            // Update UI
            document.getElementById(`${tokenType}-balance`).textContent = result.balance.toFixed(2);
            document.getElementById('silicon-user').textContent = result.userName;
            document.getElementById(`${tokenType}-last-updated`).textContent = getCurrentTime();

            // Add animation to balance display
            animateValue(document.getElementById(`${tokenType}-balance`), 0, result.balance, 1000);
        })
        .catch(error => {
            // Handle errors
            console.error('查询余额错误:', error);
            loader.style.display = 'none';
            showError(addressInput, '查询余额失败，请重试。');
        });
}

// Validate API token
function validateAddress(address) {
    if (!address) return false;

    // Simple validation for API token
    return address.length >= 10;
}

// Show error message
function showError(inputElement, message) {
    // Remove existing error message if any
    const parent = inputElement.parentElement;
    const existingError = parent.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }

    // Add error class to input
    inputElement.classList.add('error');

    // Create and append error message
    const errorElement = document.createElement('div');
    errorElement.className = 'error-message';
    errorElement.textContent = message;
    parent.appendChild(errorElement);

    // Remove error after 3 seconds
    setTimeout(() => {
        inputElement.classList.remove('error');
        errorElement.remove();
    }, 3000);
}

// Get current time formatted
function getCurrentTime() {
    const now = new Date();
    return now.toLocaleTimeString() + ' ' + now.toLocaleDateString();
}

// Animate value change
function animateValue(element, start, end, duration) {
    let startTimestamp = null;
    const step = (timestamp) => {
        if (!startTimestamp) startTimestamp = timestamp;
        const progress = Math.min((timestamp - startTimestamp) / duration, 1);
        const value = progress * (end - start) + start;
        element.textContent = value.toFixed(2);
        if (progress < 1) {
            window.requestAnimationFrame(step);
        }
    };
    window.requestAnimationFrame(step);
}

// Add input animations
const inputs = document.querySelectorAll('input');
inputs.forEach(input => {
    input.addEventListener('focus', () => {
        input.parentElement.classList.add('focused');
    });

    input.addEventListener('blur', () => {
        input.parentElement.classList.remove('focused');
    });
});
