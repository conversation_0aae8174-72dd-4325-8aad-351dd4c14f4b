'''
Cloudflare DNS 管理器
todo:
1、美化界面、字体
2、增加解析查重
3、增加历史记录修改
4、打包为exe
'''



import os
import flet as ft
import requests
from dotenv import load_dotenv

class CloudflareDNSManager:
    def __init__(self, page: ft.Page):
        self.page = page
        self.page.title = "Cloudflare DNS 管理器"
        self.page.window_width = 1000
        self.page.window_height = 800
        
        # Cloudflare 配置
        self.api_token = None
        self.zone_id = None
        self.zones = []
        
        # 界面元素
        self.api_token_input = ft.TextField(label="Cloudflare API Token", width=500)
        self.zones_dropdown = ft.Dropdown(width=500, on_change=self.on_zone_selected)
        self.dns_records_list = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("类型")),
                ft.DataColumn(ft.Text("名称")),
                ft.DataColumn(ft.Text("内容")),
                ft.DataColumn(ft.Text("操作"))
            ],
            rows=[],
            column_spacing=20,
            horizontal_lines=ft.border.BorderSide(1, "grey"),
            vertical_lines=ft.border.BorderSide(1, "grey"),
            show_bottom_border=True,
        )
        
        # 使用 ScrollView 包裹 DataTable
        self.records_scroll_view = ft.ListView(
            controls=[self.dns_records_list],
            width=950,
            height=450,
            spacing=10,
            padding=10,
            auto_scroll=True
        )
        
        self.record_type_dropdown = ft.Dropdown(
            width=100,
            options=[
                ft.dropdown.Option("A"),
                ft.dropdown.Option("AAAA"),
                ft.dropdown.Option("CNAME"),
                ft.dropdown.Option("MX"),
                ft.dropdown.Option("TXT")
            ]
        )
        self.name_input = ft.TextField(
            label="记录名称", 
            expand=True,
            height=50  # 确保高度一致
        )
        self.lookup_button = ft.IconButton(
            icon=ft.icons.SEARCH,
            tooltip="查找当前域名解析",
            on_click=self.lookup_dns
        )
        self.content_input = ft.TextField(
            label="记录内容", 
            expand=True,
            height=50  # 确保高度一致
        )
        self.filter_type_dropdown = ft.Dropdown(
            width=100,
            options=[
                ft.dropdown.Option("全部"),
                ft.dropdown.Option("A"),
                ft.dropdown.Option("AAAA"),
                ft.dropdown.Option("CNAME"),
                ft.dropdown.Option("MX"),
                ft.dropdown.Option("TXT")
            ],
            value="全部",
            on_change=self.filter_records
        )
        
        self.setup_ui()

    def setup_ui(self):
        # API Token 输入区域
        api_token_row = ft.Row([
            self.api_token_input,
            ft.ElevatedButton("验证并获取域名", on_click=self.validate_api_token)
        ])
        
        # 域名选择区域
        zone_row = ft.Row([
            self.zones_dropdown,
        ])
        
        # 添加记录区域
        add_record_row = ft.Row(
            controls=[
                self.record_type_dropdown,
                ft.Container(
                    content=ft.Row(
                        controls=[
                            self.name_input,
                            self.lookup_button
                        ],
                        spacing=0,
                        alignment=ft.MainAxisAlignment.CENTER
                    ),
                    expand=True
                ),
                self.content_input,
                ft.ElevatedButton("添加记录", on_click=self.add_dns_record)
            ],
            alignment=ft.MainAxisAlignment.START,
            spacing=10
        )
        
        # 过滤记录区域
        filter_row = ft.Row([
            ft.Text("记录类型筛选:"),
            self.filter_type_dropdown
        ])
        
        main_column = ft.Column([
            ft.Text("Cloudflare DNS 管理器", size=24, weight=ft.FontWeight.BOLD),
            api_token_row,
            zone_row,
            add_record_row,
            filter_row,
            ft.Container(content=self.records_scroll_view, padding=10)
        ])
        
        self.page.add(main_column)
        self.page.update()

    def validate_api_token(self, e):
        self.api_token = self.api_token_input.value
        headers = {
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        }
        
        url = 'https://api.cloudflare.com/client/v4/zones'
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            self.zones = response.json()['result']
            self.zones_dropdown.options = [
                ft.dropdown.Option(zone['name']) for zone in self.zones
            ]
            self.page.snack_bar = ft.SnackBar(content=ft.Text("API Token 验证成功"))
            self.page.snack_bar.open = True
            self.page.update()
        else:
            self.page.snack_bar = ft.SnackBar(content=ft.Text("API Token 验证失败"))
            self.page.snack_bar.open = True
            self.page.update()

    def on_zone_selected(self, e):
        selected_zone = next(
            (zone for zone in self.zones if zone['name'] == self.zones_dropdown.value),
            None
        )
        if selected_zone:
            self.zone_id = selected_zone['id']
            self.load_dns_records()

    def load_dns_records(self):
        headers = {
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        }
        
        url = f'https://api.cloudflare.com/client/v4/zones/{self.zone_id}/dns_records'
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            records = response.json()['result']
            self.update_records_table(records)

    def confirm_delete_record(self, record):
        def close_dlg(e):
            self.delete_confirm_dlg.open = False
            self.page.update()
        
        def confirm_delete(e):
            self.delete_dns_record(record['id'])
            self.delete_confirm_dlg.open = False
            self.page.update()
        
        self.delete_confirm_dlg = ft.AlertDialog(
            modal=True,
            title=ft.Text("确认删除"),
            content=ft.Text(f"确定要删除 {record['name']} 记录吗？"),
            actions=[
                ft.TextButton("取消", on_click=close_dlg),
                ft.TextButton("确定", on_click=confirm_delete)
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        self.page.dialog = self.delete_confirm_dlg
        self.delete_confirm_dlg.open = True
        self.page.update()

    def edit_dns_record(self, record):
        # 填充当前记录信息到编辑区域
        self.record_type_dropdown.value = record['type']
        self.name_input.value = record['name']
        self.content_input.value = record['content']
        
        # 创建一个保存按钮来更新记录
        save_button = ft.ElevatedButton(
            "保存修改", 
            on_click=lambda e: self.save_edited_record(record['id'])
        )
        
        # 临时替换添加按钮
        add_record_row = self.page.controls[0].controls[3]
        add_record_row.controls[3] = save_button
        self.page.update()

    def save_edited_record(self, record_id):
        name = self.name_input.value
        type = self.record_type_dropdown.value
        content = self.content_input.value
        
        headers = {
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'type': type,
            'name': name,
            'content': content,
            'proxied': False
        }
        
        url = f'https://api.cloudflare.com/client/v4/zones/{self.zone_id}/dns_records/{record_id}'
        response = requests.put(url, headers=headers, json=data)
        
        if response.status_code == 200:
            # 重置编辑状态
            self.name_input.value = ""
            self.content_input.value = ""
            
            # 恢复原始添加按钮
            add_record_row = self.page.controls[0].controls[3]
            add_record_row.controls[3] = ft.ElevatedButton("添加记录", on_click=self.add_dns_record)
            
            self.load_dns_records()
            
            self.page.snack_bar = ft.SnackBar(content=ft.Text("记录修改成功"))
            self.page.snack_bar.open = True
            self.page.update()
        else:
            self.page.snack_bar = ft.SnackBar(content=ft.Text("记录修改失败"))
            self.page.snack_bar.open = True
            self.page.update()

    def update_records_table(self, records):
        self.dns_records_list.rows.clear()
    
        for record in records:
            if (self.filter_type_dropdown.value == "全部" or 
                record['type'] == self.filter_type_dropdown.value):
                row = ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(record['type'])),
                        ft.DataCell(ft.Text(record['name'])),
                        ft.DataCell(ft.Text(record['content'])),
                        ft.DataCell(
                            ft.Row([
                                ft.IconButton(
                                    ft.icons.EDIT, 
                                    tooltip="修改记录",
                                    on_click=lambda e, r=record: self.edit_dns_record(r)
                                ),
                                ft.IconButton(
                                    ft.icons.DELETE, 
                                    tooltip="删除记录",
                                    on_click=lambda e, r=record: self.confirm_delete_record(r)
                                )
                            ])
                        )
                    ]
                )
                self.dns_records_list.rows.append(row)
        
        self.page.update()

    def filter_records(self, e):
        self.load_dns_records()

    def check_record_exists(self, name, type):
        headers = {
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        }
        
        url = f'https://api.cloudflare.com/client/v4/zones/{self.zone_id}/dns_records'
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            records = response.json()['result']
            return any(
                record['name'] == name and record['type'] == type 
                for record in records
            )
        return False

    def add_dns_record(self, e):
        if not self.zone_id:
            self.page.snack_bar = ft.SnackBar(content=ft.Text("请先选择域名"))
            self.page.snack_bar.open = True
            self.page.update()
            return
        
        name = self.name_input.value
        type = self.record_type_dropdown.value
        content = self.content_input.value
        
        if not all([name, type, content]):
            self.page.snack_bar = ft.SnackBar(content=ft.Text("请填写完整信息"))
            self.page.snack_bar.open = True
            self.page.update()
            return
        
        if self.check_record_exists(name, type):
            self.page.snack_bar = ft.SnackBar(content=ft.Text("该记录已存在"))
            self.page.snack_bar.open = True
            self.page.update()
            return
        
        headers = {
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'type': type,
            'name': name,
            'content': content,
            'proxied': False
        }
        
        url = f'https://api.cloudflare.com/client/v4/zones/{self.zone_id}/dns_records'
        response = requests.post(url, headers=headers, json=data)
        
        if response.status_code == 200:
            self.load_dns_records()
            self.name_input.value = ""
            self.content_input.value = ""
            self.page.snack_bar = ft.SnackBar(content=ft.Text("记录添加成功"))
            self.page.snack_bar.open = True
            self.page.update()
        else:
            self.page.snack_bar = ft.SnackBar(content=ft.Text("记录添加失败"))
            self.page.snack_bar.open = True
            self.page.update()

    def delete_dns_record(self, record_id):
        headers = {
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        }
        
        url = f'https://api.cloudflare.com/client/v4/zones/{self.zone_id}/dns_records/{record_id}'
        response = requests.delete(url, headers=headers)
        
        if response.status_code == 200:
            self.load_dns_records()
            self.page.snack_bar = ft.SnackBar(content=ft.Text("记录删除成功"))
            self.page.snack_bar.open = True
            self.page.update()

    def lookup_dns(self, e):
        subdomain = self.name_input.value
        if not subdomain or not self.zone_id or not self.api_token:
            self.page.snack_bar = ft.SnackBar(content=ft.Text("请确保已输入域名且已选择区域"))
            self.page.snack_bar.open = True
            self.page.update()
            return
        
        # 获取当前选择的域名
        selected_zone = next(
            (zone for zone in self.zones if zone['name'] == self.zones_dropdown.value),
            None
        )
        if not selected_zone:
            self.page.snack_bar = ft.SnackBar(content=ft.Text("请先选择域名"))
            self.page.snack_bar.open = True
            self.page.update()
            return
            
        # 构建完整的域名
        full_domain = f"{subdomain}.{selected_zone['name']}"
        
        headers = {
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        }
        
        # 构建 API URL，查询指定域名的记录
        url = f'https://api.cloudflare.com/client/v4/zones/{self.zone_id}/dns_records'
        params = {'name': full_domain}  # 按完整域名过滤
        
        try:
            response = requests.get(url, headers=headers, params=params)
            if response.status_code == 200:
                records = response.json()['result']
                if records:
                    # 找到记录，填充第一个匹配记录的内容
                    self.content_input.value = records[0]['content']
                    # 同时更新记录类型
                    self.record_type_dropdown.value = records[0]['type']
                    self.page.snack_bar = ft.SnackBar(content=ft.Text("已找到记录并填充"))
                else:
                    self.content_input.value = ""
                    self.page.snack_bar = ft.SnackBar(content=ft.Text("未找到相关记录"))
            else:
                self.page.snack_bar = ft.SnackBar(content=ft.Text("查询失败"))
        except Exception as ex:
            self.page.snack_bar = ft.SnackBar(content=ft.Text(f"发生错误: {str(ex)}"))
        
        self.page.snack_bar.open = True
        self.page.update()

    def show_image_dialog(self):
        dialog = ImageDialog(self.page)
        dialog.dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("图片预览"),
            content=dialog.build(),
        )
        self.page.dialog = dialog.dialog
        dialog.dialog.open = True
        self.page.update()

class ImageDialog(ft.UserControl):
    def __init__(self, page):
        super().__init__()
        self.page = page
        self.dialog = None
        self.image_preview = ft.Image(
            src=None,
            width=200,
            height=200,
            fit=ft.ImageFit.CONTAIN,
        )
        
    def build(self):
        self.text_field = ft.TextField(
            multiline=True,
            min_lines=3,
            max_lines=3,
            width=400,
            label="请粘贴图片",
            on_change=self.handle_paste
        )
        
        return ft.AlertDialog(
            title=ft.Text("图片预览"),
            content=ft.Column([
                self.text_field,
                self.image_preview,
            ], tight=True),
            actions=[
                ft.TextButton("取消", on_click=self.close_dlg),
                ft.TextButton("确定", on_click=self.submit),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )

    def handle_paste(self, e):
        try:
            text = self.text_field.value
            if text.startswith(('http://', 'https://')):
                self.image_preview.src = text
                self.image_preview.visible = True
                self.page.update()
        except Exception as ex:
            print(f"Error handling paste: {ex}")

    def close_dlg(self, e):
        self.dialog.open = False
        self.page.update()

    def submit(self, e):
        # 处理确定按钮的逻辑
        self.close_dlg(e)

def main(page: ft.Page):
    CloudflareDNSManager(page)

ft.app(target=main)