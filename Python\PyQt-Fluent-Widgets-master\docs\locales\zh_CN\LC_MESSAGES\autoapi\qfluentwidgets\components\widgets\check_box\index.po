# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON><PERSON>
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-22 20:49+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/widgets/check_box/index.rst:2
#: ********************************
msgid "check_box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/check_box/index.rst:8
#: ********************************
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/check_box/index.rst:19:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`CheckBoxIcon "
"<qfluentwidgets.components.widgets.check_box.CheckBoxIcon>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/check_box/index.rst:24
#: ../../source/autoapi/qfluentwidgets/components/widgets/check_box/index.rst:19:<autosummary>:1
#: ******************************** d84e348b46b54693b6f38647d67c8475
msgid "CheckBoxIcon"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/check_box/index.rst:19:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`CheckBox "
"<qfluentwidgets.components.widgets.check_box.CheckBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/check_box/index.rst:54
#: ../../source/autoapi/qfluentwidgets/components/widgets/check_box/index.rst:19:<autosummary>:1
#: ******************************** 53cd4fdd1703441c9dd1c84adb8e1d3c
msgid "Check box"
msgstr "复选框"

#: ../../source/autoapi/qfluentwidgets/components/widgets/check_box/index.rst:22
#: ********************************
msgid ""
"Bases: :py:obj:`qfluentwidgets.common.icon.FluentIconBase`, "
":py:obj:`enum.Enum`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/check_box/index.rst:38
#: ********************************
msgid "get the path of icon"
msgstr "返回图标路径"

#: ../../source/autoapi/qfluentwidgets/components/widgets/check_box/index.rst:41
#: ********************************
msgid "Parameters"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/check_box/index.rst:45
#: ********************************
msgid "theme: Theme"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/check_box/index.rst:43
#: ********************************
msgid ""
"the theme of icon * `Theme.Light`: black icon * `Theme.DARK`: white icon "
"* `Theme.AUTO`: icon color depends on `config.theme`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/check_box/index.rst:52
#: ********************************
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QCheckBox`"
msgstr ""

