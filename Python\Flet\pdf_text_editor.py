#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文本替换编辑工具
基于flet框架开发的GUI应用程序，支持PDF文件的文本查找和替换功能
"""

import flet as ft
import os
import tempfile
from pathlib import Path
import traceback

try:
    import PyPDF2
    import pdfplumber
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.lib.styles import getSampleStyleSheet
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.units import inch
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    print("请安装: pip install PyPDF2 pdfplumber reportlab")


class PDFTextEditor:
    def __init__(self):
        self.current_pdf_path = None
        self.pdf_text = ""
        self.modified_text = ""
        self.replacement_pairs = []
        
    def extract_text_from_pdf(self, pdf_path):
        """从PDF文件中提取文本"""
        try:
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            return text
        except Exception as e:
            raise Exception(f"PDF文本提取失败: {str(e)}")
    
    def replace_text(self, original_text, find_text, replace_text):
        """执行文本替换"""
        if not find_text:
            return original_text
        return original_text.replace(find_text, replace_text)
    
    def create_pdf_from_text(self, text, output_path):
        """从文本创建PDF文件"""
        try:
            # 创建PDF文档
            doc = SimpleDocTemplate(output_path, pagesize=A4)
            
            # 尝试注册中文字体
            try:
                # Windows系统中文字体路径
                font_paths = [
                    "C:/Windows/Fonts/simsun.ttc",
                    "C:/Windows/Fonts/msyh.ttc",
                    "C:/Windows/Fonts/simhei.ttf"
                ]
                
                font_registered = False
                for font_path in font_paths:
                    if os.path.exists(font_path):
                        try:
                            pdfmetrics.registerFont(TTFont('Chinese', font_path))
                            font_registered = True
                            break
                        except:
                            continue
                
                if not font_registered:
                    # 如果没有找到中文字体，使用默认字体
                    font_name = 'Helvetica'
                else:
                    font_name = 'Chinese'
                    
            except Exception:
                font_name = 'Helvetica'
            
            # 创建样式
            styles = getSampleStyleSheet()
            normal_style = styles['Normal']
            normal_style.fontName = font_name
            normal_style.fontSize = 12
            normal_style.leading = 14
            
            # 将文本分段并创建段落
            story = []
            paragraphs = text.split('\n')
            
            for para_text in paragraphs:
                if para_text.strip():
                    try:
                        para = Paragraph(para_text, normal_style)
                        story.append(para)
                    except Exception:
                        # 如果段落创建失败，尝试使用纯文本
                        para = Paragraph(para_text.encode('ascii', 'ignore').decode(), normal_style)
                        story.append(para)
                else:
                    story.append(Spacer(1, 0.2*inch))
            
            # 构建PDF
            doc.build(story)
            return True
            
        except Exception as e:
            raise Exception(f"PDF创建失败: {str(e)}")


def main(page: ft.Page):
    page.title = "PDF文本替换编辑工具"
    page.window_width = 1200
    page.window_height = 800
    page.theme_mode = ft.ThemeMode.LIGHT
    
    # 初始化PDF编辑器
    pdf_editor = PDFTextEditor()
    
    # 控件定义
    file_path_text = ft.Text("未选择文件", size=14, color=ft.colors.GREY_600)
    
    # 预览区域
    preview_text = ft.TextField(
        multiline=True,
        min_lines=20,
        max_lines=20,
        read_only=True,
        hint_text="PDF内容预览将在这里显示...",
        expand=True
    )
    
    # 替换控件
    find_text_field = ft.TextField(
        label="原文",
        hint_text="输入需要替换的原始文字",
        multiline=True,
        min_lines=2,
        max_lines=5
    )
    
    replace_text_field = ft.TextField(
        label="变更为",
        hint_text="输入替换后的新文字",
        multiline=True,
        min_lines=2,
        max_lines=5
    )
    
    # 状态显示
    status_text = ft.Text("就绪", size=12, color=ft.colors.GREEN)
    
    def pick_file_result(e: ft.FilePickerResultEvent):
        """文件选择回调"""
        try:
            if e.files:
                file_path = e.files[0].path
                pdf_editor.current_pdf_path = file_path
                file_path_text.value = f"已选择: {Path(file_path).name}"
                
                # 提取PDF文本
                status_text.value = "正在提取PDF文本..."
                status_text.color = ft.colors.BLUE
                page.update()
                
                pdf_text = pdf_editor.extract_text_from_pdf(file_path)
                pdf_editor.pdf_text = pdf_text
                pdf_editor.modified_text = pdf_text
                
                preview_text.value = pdf_text
                status_text.value = f"PDF加载成功，共提取 {len(pdf_text)} 个字符"
                status_text.color = ft.colors.GREEN
                
            else:
                file_path_text.value = "未选择文件"
                preview_text.value = ""
                status_text.value = "未选择文件"
                status_text.color = ft.colors.GREY_600
                
        except Exception as ex:
            status_text.value = f"错误: {str(ex)}"
            status_text.color = ft.colors.RED
            
        page.update()
    
    # 文件选择器
    file_picker = ft.FilePicker(on_result=pick_file_result)
    page.overlay.append(file_picker)
    
    def select_file_click(e):
        """选择文件按钮点击"""
        file_picker.pick_files(
            dialog_title="选择PDF文件",
            file_type=ft.FilePickerFileType.CUSTOM,
            allowed_extensions=["pdf"]
        )
    
    def replace_text_click(e):
        """执行替换操作"""
        try:
            if not pdf_editor.current_pdf_path:
                status_text.value = "请先选择PDF文件"
                status_text.color = ft.colors.RED
                page.update()
                return
            
            find_text = find_text_field.value
            replace_text = replace_text_field.value
            
            if not find_text:
                status_text.value = "请输入要替换的原文"
                status_text.color = ft.colors.RED
                page.update()
                return
            
            # 执行替换
            status_text.value = "正在执行文本替换..."
            status_text.color = ft.colors.BLUE
            page.update()
            
            pdf_editor.modified_text = pdf_editor.replace_text(
                pdf_editor.modified_text, find_text, replace_text
            )
            
            # 更新预览
            preview_text.value = pdf_editor.modified_text
            
            # 记录替换操作
            replacement_info = f"'{find_text}' → '{replace_text}'"
            if replacement_info not in [pair[2] for pair in pdf_editor.replacement_pairs]:
                pdf_editor.replacement_pairs.append((find_text, replace_text, replacement_info))
            
            status_text.value = f"替换完成: {replacement_info}"
            status_text.color = ft.colors.GREEN
            
        except Exception as ex:
            status_text.value = f"替换失败: {str(ex)}"
            status_text.color = ft.colors.RED
            
        page.update()
    
    def save_pdf_result(e: ft.FilePickerResultEvent):
        """保存文件回调"""
        try:
            if e.path:
                output_path = e.path
                if not output_path.endswith('.pdf'):
                    output_path += '.pdf'
                
                status_text.value = "正在生成PDF文件..."
                status_text.color = ft.colors.BLUE
                page.update()
                
                pdf_editor.create_pdf_from_text(pdf_editor.modified_text, output_path)
                
                status_text.value = f"PDF已保存至: {Path(output_path).name}"
                status_text.color = ft.colors.GREEN
                
        except Exception as ex:
            status_text.value = f"保存失败: {str(ex)}"
            status_text.color = ft.colors.RED
            
        page.update()
    
    # 保存文件选择器
    save_file_picker = ft.FilePicker(on_result=save_pdf_result)
    page.overlay.append(save_file_picker)
    
    def save_pdf_click(e):
        """保存PDF按钮点击"""
        if not pdf_editor.modified_text:
            status_text.value = "没有可保存的内容"
            status_text.color = ft.colors.RED
            page.update()
            return
            
        save_file_picker.save_file(
            dialog_title="保存PDF文件",
            file_name="modified_document.pdf",
            file_type=ft.FilePickerFileType.CUSTOM,
            allowed_extensions=["pdf"]
        )
    
    # 构建界面
    page.add(
        ft.Container(
            content=ft.Column([
                # 标题
                ft.Container(
                    content=ft.Text("PDF文本替换编辑工具", size=24, weight=ft.FontWeight.BOLD),
                    padding=ft.padding.only(bottom=20)
                ),
                
                # 导入区域
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("文件导入", size=16, weight=ft.FontWeight.BOLD),
                            ft.Row([
                                ft.ElevatedButton("选择PDF文件", on_click=select_file_click),
                                file_path_text
                            ]),
                        ]),
                        padding=15
                    )
                ),
                
                # 主要内容区域
                ft.Row([
                    # 左侧：预览区域
                    ft.Container(
                        content=ft.Column([
                            ft.Text("内容预览", size=16, weight=ft.FontWeight.BOLD),
                            preview_text
                        ]),
                        width=600,
                        padding=10
                    ),
                    
                    # 右侧：修改区域
                    ft.Container(
                        content=ft.Column([
                            ft.Text("文本替换", size=16, weight=ft.FontWeight.BOLD),
                            find_text_field,
                            replace_text_field,
                            ft.Row([
                                ft.ElevatedButton("执行替换", on_click=replace_text_click),
                                ft.ElevatedButton("保存PDF", on_click=save_pdf_click),
                            ]),
                        ]),
                        width=500,
                        padding=10
                    )
                ], expand=True),
                
                # 状态栏
                ft.Container(
                    content=ft.Row([
                        ft.Text("状态: "),
                        status_text
                    ]),
                    padding=ft.padding.only(top=10)
                )
            ]),
            padding=20
        )
    )


if __name__ == "__main__":
    ft.app(target=main)
