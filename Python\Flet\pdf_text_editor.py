#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文本替换编辑工具
基于flet框架开发的GUI应用程序，支持PDF文件的文本查找和替换功能
"""

import flet as ft
import os
import tempfile
from pathlib import Path
import traceback
import re

try:
    import PyPDF2
    import pdfplumber
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.lib.styles import getSampleStyleSheet
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.units import inch
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    print("请安装: pip install PyPDF2 pdfplumber reportlab")


class PDFTextEditor:
    def __init__(self):
        self.current_pdf_path = None
        self.pdf_text = ""
        self.modified_text = ""
        self.replacement_pairs = []
        self.page_contents = []  # 存储每页的文本内容
        
    def extract_text_from_pdf(self, pdf_path):
        """从PDF文件中提取文本"""
        try:
            text = ""
            self.page_contents = []
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
                        self.page_contents.append(page_text)
            return text
        except Exception as e:
            raise Exception(f"PDF文本提取失败: {str(e)}")
    
    def replace_text(self, original_text, find_text, replace_text, is_regex=False):
        """执行文本替换，支持正则表达式"""
        if not find_text:
            return original_text
        
        if is_regex:
            try:
                return re.sub(find_text, replace_text, original_text)
            except re.error as e:
                raise Exception(f"正则表达式错误: {str(e)}")
        else:
            return original_text.replace(find_text, replace_text)
    
    def create_pdf_from_text(self, text, output_path):
        """从文本创建PDF文件"""
        try:
            # 创建PDF文档
            doc = SimpleDocTemplate(output_path, pagesize=A4)
            
            # 尝试注册中文字体
            try:
                # Windows系统中文字体路径
                font_paths = [
                    "C:/Windows/Fonts/simsun.ttc",
                    "C:/Windows/Fonts/msyh.ttc",
                    "C:/Windows/Fonts/simhei.ttf"
                ]
                
                # Linux系统中文字体路径
                if os.name != 'nt':
                    font_paths.extend([
                        "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
                        "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
                        "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc"
                    ])
                
                font_registered = False
                for font_path in font_paths:
                    if os.path.exists(font_path):
                        try:
                            pdfmetrics.registerFont(TTFont('Chinese', font_path))
                            font_registered = True
                            break
                        except:
                            continue
                
                if not font_registered:
                    # 如果没有找到中文字体，使用默认字体
                    font_name = 'Helvetica'
                else:
                    font_name = 'Chinese'
                    
            except Exception:
                font_name = 'Helvetica'
            
            # 创建样式
            styles = getSampleStyleSheet()
            normal_style = styles['Normal']
            normal_style.fontName = font_name
            normal_style.fontSize = 12
            normal_style.leading = 14
            
            # 将文本分段并创建段落
            story = []
            paragraphs = text.split('\n')
            
            for para_text in paragraphs:
                if para_text.strip():
                    try:
                        para = Paragraph(para_text, normal_style)
                        story.append(para)
                    except Exception:
                        # 如果段落创建失败，尝试使用纯文本
                        para = Paragraph(para_text.encode('ascii', 'ignore').decode(), normal_style)
                        story.append(para)
                else:
                    story.append(Spacer(1, 0.2*inch))
            
            # 构建PDF
            doc.build(story)
            return True
            
        except Exception as e:
            raise Exception(f"PDF创建失败: {str(e)}")
    
    def get_replacements_summary(self):
        """获取替换操作的摘要统计"""
        original_count = len(self.pdf_text)
        modified_count = len(self.modified_text)
        change_count = original_count - modified_count
        replacement_count = len(self.replacement_pairs)
        
        return {
            'original_count': original_count,
            'modified_count': modified_count,
            'change_count': change_count,
            'replacement_count': replacement_count
        }


def main(page: ft.Page):
    page.title = "PDF文本替换编辑工具"
    page.window_width = 1200
    page.window_height = 800
    page.window_min_width = 800
    page.window_min_height = 600
    page.theme_mode = ft.ThemeMode.LIGHT
    page.scroll = ft.ScrollMode.AUTO
    page.padding = 20
    
    # 初始化PDF编辑器
    pdf_editor = PDFTextEditor()
    
    # 控件定义
    file_path_text = ft.Text("未选择文件", size=14, color=ft.colors.GREY_600)
    
    # 预览区域
    preview_text = ft.TextField(
        multiline=True,
        min_lines=15,
        max_lines=15,
        read_only=True,
        hint_text="PDF内容预览将在这里显示...",
        width=580,
        height=400
    )
    
    # 替换控件
    find_text_field = ft.TextField(
        label="原文",
        hint_text="输入需要替换的原始文字",
        multiline=True,
        min_lines=3,
        max_lines=6,
        width=480,
        height=100
    )

    replace_text_field = ft.TextField(
        label="变更为",
        hint_text="输入替换后的新文字",
        multiline=True,
        min_lines=3,
        max_lines=6,
        width=480,
        height=100
    )
    
    # 替换选项
    is_case_sensitive = ft.Checkbox(label="区分大小写", value=True)
    use_regex = ft.Checkbox(label="使用正则表达式", value=False)
    
    # 替换历史记录
    replacements_history = ft.ListView(
        expand=1,
        spacing=5,
        padding=10,
        auto_scroll=True,
        height=150
    )
    
    # 统计信息
    stats_text = ft.Text("替换统计: 0次替换, 0个字符变更", size=12, color=ft.colors.GREY_700)
    
    # 状态显示
    status_text = ft.Text("就绪", size=12, color=ft.colors.GREEN)
    
    def pick_file_result(e: ft.FilePickerResultEvent):
        """文件选择回调"""
        try:
            if e.files:
                file_path = e.files[0].path
                pdf_editor.current_pdf_path = file_path
                file_path_text.value = f"已选择: {Path(file_path).name}"
                
                # 提取PDF文本
                status_text.value = "正在提取PDF文本..."
                status_text.color = ft.colors.BLUE
                page.update()
                
                pdf_text = pdf_editor.extract_text_from_pdf(file_path)
                pdf_editor.pdf_text = pdf_text
                pdf_editor.modified_text = pdf_text
                
                preview_text.value = pdf_text[:10000]  # 限制预览长度，避免性能问题
                status_text.value = f"PDF加载成功，共提取 {len(pdf_text)} 个字符"
                status_text.color = ft.colors.GREEN
                
                # 清空替换历史
                replacements_history.controls.clear()
                pdf_editor.replacement_pairs.clear()
                update_stats()
                
            else:
                file_path_text.value = "未选择文件"
                preview_text.value = ""
                status_text.value = "未选择文件"
                status_text.color = ft.colors.GREY_600
                
        except Exception as ex:
            status_text.value = f"错误: {str(ex)}"
            status_text.color = ft.colors.RED
            
        page.update()
    
    # 文件选择器
    file_picker = ft.FilePicker(on_result=pick_file_result)
    page.overlay.append(file_picker)
    
    def select_file_click(e):
        """选择文件按钮点击"""
        file_picker.pick_files(
            dialog_title="选择PDF文件",
            file_type=ft.FilePickerFileType.CUSTOM,
            allowed_extensions=["pdf"]
        )
    
    def replace_text_click(e):
        """执行替换操作"""
        try:
            if not pdf_editor.current_pdf_path:
                status_text.value = "请先选择PDF文件"
                status_text.color = ft.colors.RED
                page.update()
                return
            
            find_text = find_text_field.value
            replace_text = replace_text_field.value
            
            if not find_text:
                status_text.value = "请输入要替换的原文"
                status_text.color = ft.colors.RED
                page.update()
                return
            
            # 执行替换
            status_text.value = "正在执行文本替换..."
            status_text.color = ft.colors.BLUE
            page.update()
            
            # 根据选项处理文本
            if not is_case_sensitive.value:
                find_text_lower = find_text.lower()
                modified_text_lower = pdf_editor.modified_text.lower()
                
                # 记录所有匹配位置
                matches = []
                start_pos = 0
                while True:
                    pos = modified_text_lower.find(find_text_lower, start_pos)
                    if pos == -1:
                        break
                    matches.append(pos)
                    start_pos = pos + len(find_text_lower)
                
                # 不区分大小写替换
                if matches:
                    modified_text = list(pdf_editor.modified_text)
                    for pos in reversed(matches):
                        # 保持原字符大小写
                        for i in range(len(find_text)):
                            if i < len(replace_text):
                                modified_text[pos + i] = replace_text[i]
                    pdf_editor.modified_text = ''.join(modified_text)
            else:
                # 区分大小写替换，支持正则表达式
                pdf_editor.modified_text = pdf_editor.replace_text(
                    pdf_editor.modified_text, find_text, replace_text, use_regex.value
                )
            
            # 更新预览
            preview_text.value = pdf_editor.modified_text[:10000]  # 限制预览长度
            
            # 记录替换操作
            replacement_info = f"'{find_text}' → '{replace_text}'"
            if replacement_info not in [pair[2] for pair in pdf_editor.replacement_pairs]:
                pdf_editor.replacement_pairs.append((find_text, replace_text, replacement_info))
                
                # 添加到替换历史
                replacements_history.controls.append(
                    ft.Text(f"• {replacement_info}", size=12)
                )
            
            status_text.value = f"替换完成: {replacement_info}"
            status_text.color = ft.colors.GREEN
            
            # 更新统计信息
            update_stats()
            
        except Exception as ex:
            status_text.value = f"替换失败: {str(ex)}"
            status_text.color = ft.colors.RED
            
        page.update()
    
    def update_stats():
        """更新替换统计信息"""
        stats = pdf_editor.get_replacements_summary()
        stats_text.value = (
            f"替换统计: {stats['replacement_count']}次替换, "
            f"{abs(stats['change_count'])}个字符变更"
        )
    
    def save_pdf_result(e: ft.FilePickerResultEvent):
        """保存文件回调"""
        try:
            if e.path:
                output_path = e.path
                if not output_path.endswith('.pdf'):
                    output_path += '.pdf'
                
                status_text.value = "正在生成PDF文件..."
                status_text.color = ft.colors.BLUE
                page.update()
                
                pdf_editor.create_pdf_from_text(pdf_editor.modified_text, output_path)
                
                status_text.value = f"PDF已保存至: {Path(output_path).name}"
                status_text.color = ft.colors.GREEN
                
        except Exception as ex:
            status_text.value = f"保存失败: {str(ex)}"
            status_text.color = ft.colors.RED
            
        page.update()
    
    # 保存文件选择器
    save_file_picker = ft.FilePicker(on_result=save_pdf_result)
    page.overlay.append(save_file_picker)
    
    def save_pdf_click(e):
        """保存PDF按钮点击"""
        if not pdf_editor.modified_text:
            status_text.value = "没有可保存的内容"
            status_text.color = ft.colors.RED
            page.update()
            return
            
        save_file_picker.save_file(
            dialog_title="保存PDF文件",
            file_name="modified_document.pdf",
            file_type=ft.FilePickerFileType.CUSTOM,
            allowed_extensions=["pdf"]
        )
    
    def clear_replacements_click(e):
        """清除替换历史"""
        pdf_editor.replacement_pairs.clear()
        replacements_history.controls.clear()
        pdf_editor.modified_text = pdf_editor.pdf_text
        preview_text.value = pdf_editor.modified_text[:10000]
        update_stats()
        status_text.value = "已重置所有替换操作"
        status_text.color = ft.colors.GREEN
        page.update()
    
    # 构建界面
    page.add(
        ft.Column([
            # 标题
            ft.Container(
                content=ft.Text("PDF文本替换编辑工具", size=24, weight=ft.FontWeight.BOLD),
                padding=ft.padding.only(bottom=20),
                alignment=ft.alignment.center
            ),

            # 导入区域
            ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text("📁 文件导入", size=16, weight=ft.FontWeight.BOLD),
                        ft.Row([
                            ft.ElevatedButton(
                                "选择PDF文件",
                                on_click=select_file_click,
                                icon=ft.icons.UPLOAD_FILE
                            ),
                            ft.Container(
                                content=file_path_text,
                                expand=True,
                                padding=ft.padding.only(left=10)
                            )
                        ]),
                    ]),
                    padding=15
                ),
                margin=ft.margin.only(bottom=10)
            ),

            # 主要内容区域
            ft.Row([
                # 左侧：预览区域
                ft.Container(
                    content=ft.Column([
                        ft.Text("👀 内容预览", size=16, weight=ft.FontWeight.BOLD),
                        ft.Container(
                            content=preview_text,
                            padding=ft.padding.only(top=10)
                        ),
                        ft.Container(
                            content=stats_text,
                            padding=ft.padding.only(top=5)
                        )
                    ]),
                    expand=True,
                    padding=ft.padding.only(right=10)
                ),

                # 右侧：修改区域
                ft.Container(
                    content=ft.Column([
                        ft.Text("🔄 文本替换", size=16, weight=ft.FontWeight.BOLD),
                        ft.Container(
                            content=find_text_field,
                            padding=ft.padding.only(top=10, bottom=10)
                        ),
                        ft.Container(
                            content=replace_text_field,
                            padding=ft.padding.only(bottom=10)
                        ),
                        ft.Row([
                            is_case_sensitive,
                            use_regex
                        ]),
                        ft.Row([
                            ft.ElevatedButton(
                                "执行替换",
                                on_click=replace_text_click,
                                icon=ft.icons.FIND_REPLACE,
                                style=ft.ButtonStyle(
                                    bgcolor=ft.colors.BLUE,
                                    color=ft.colors.WHITE
                                )
                            ),
                            ft.ElevatedButton(
                                "保存PDF",
                                on_click=save_pdf_click,
                                icon=ft.icons.SAVE,
                                style=ft.ButtonStyle(
                                    bgcolor=ft.colors.GREEN,
                                    color=ft.colors.WHITE
                                )
                            ),
                            ft.ElevatedButton(
                                "清除替换",
                                on_click=clear_replacements_click,
                                icon=ft.icons.REFRESH,
                                style=ft.ButtonStyle(
                                    bgcolor=ft.colors.YELLOW,
                                    color=ft.colors.BLACK
                                )
                            )
                        ], spacing=10, alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        ft.Container(
                            content=ft.Column([
                                ft.Text("替换历史", size=14, weight=ft.FontWeight.BOLD),
                                replacements_history
                            ]),
                            padding=ft.padding.only(top=20),
                            expand=True
                        )
                    ]),
                    width=500,
                    padding=ft.padding.only(left=10)
                )
            ], expand=True),

            # 状态栏
            ft.Container(
                content=ft.Row([
                    ft.Icon(ft.icons.INFO, size=16),
                    ft.Text("状态: ", weight=ft.FontWeight.BOLD),
                    status_text
                ]),
                bgcolor=ft.colors.GREY_100,
                border_radius=5,
                padding=10,
                margin=ft.margin.only(top=15)
            )
        ],
        expand=True,
        spacing=0
        )
    )


if __name__ == "__main__":
    ft.app(target=main)    