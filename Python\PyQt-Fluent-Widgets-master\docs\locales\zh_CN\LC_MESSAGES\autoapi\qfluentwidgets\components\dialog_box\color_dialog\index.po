# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON>o
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-23 13:30+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:2
#: ********************************
msgid "color_dialog"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:8
#: ********************************
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:24:<autosummary>:1
#: 5df87324928c4ff19d9c3ba318c8968c
msgid ""
":py:obj:`HuePanel "
"<qfluentwidgets.components.dialog_box.color_dialog.HuePanel>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:29
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:24:<autosummary>:1
#: 35713682e1fb4ae8bcbf414cf231ec33 5963190d4dca41b78bcff394c534b531
msgid "Hue panel"
msgstr "色调面板"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:24:<autosummary>:1
#: 226612e9a57b4e0281e047700b005ab6
msgid ""
":py:obj:`BrightnessSlider "
"<qfluentwidgets.components.dialog_box.color_dialog.BrightnessSlider>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:65
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:24:<autosummary>:1
#: 649efaaee48b4a18a16ef5ee7c53016b 7771d231c12a4a24a75cb53c2c3ac4f0
msgid "Brightness slider"
msgstr "亮度滑动条"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:24:<autosummary>:1
#: 58e4a0f13d8648beae6d834d3210b155
msgid ""
":py:obj:`ColorCard "
"<qfluentwidgets.components.dialog_box.color_dialog.ColorCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:81
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:24:<autosummary>:1
#: 91a78ec1101646e792a9d71e8d03735c d760d63a963145aaa07a39a3b35fe0ac
msgid "Color card"
msgstr "颜色卡"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:24:<autosummary>:1
#: a5bf2529f47c46eca9384ed0a3bf1686
msgid ""
":py:obj:`ColorLineEdit "
"<qfluentwidgets.components.dialog_box.color_dialog.ColorLineEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:96
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:24:<autosummary>:1
#: 83b44190194548efb7fb776107861423 913abfab523f444984493a417b76a535
msgid "Color line edit"
msgstr "颜色编辑框"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:24:<autosummary>:1
#: 56292f207bae43e792b731208b319bcf
msgid ""
":py:obj:`HexColorLineEdit "
"<qfluentwidgets.components.dialog_box.color_dialog.HexColorLineEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:107
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:24:<autosummary>:1
#: 293c5d0018e44093aa21343415c8952a 7e3acdf29d73414bbb9739563630883f
msgid "Hex color line edit"
msgstr "十六进制颜色编辑框"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:24:<autosummary>:1
#: d2d57b7fadc44a96a554190f9620eaf5
msgid ""
":py:obj:`OpacityLineEdit "
"<qfluentwidgets.components.dialog_box.color_dialog.OpacityLineEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:119
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:24:<autosummary>:1
#: 7e3acdf29d73414bbb9739563630883f
#, fuzzy
msgid "Opacity line edit"
msgstr "颜色编辑框"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:24:<autosummary>:1
#: d2d57b7fadc44a96a554190f9620eaf5
msgid ""
":py:obj:`ColorDialog "
"<qfluentwidgets.components.dialog_box.color_dialog.ColorDialog>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:129
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:24:<autosummary>:1
#: 1e66ed96cd5944f48725566e2cc358e0
msgid "Color dialog"
msgstr "颜色对话框"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:27
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:79
#: ********************************
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:49
#: ********************************
msgid "set the position of"
msgstr "设置拾色器的位置"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:54
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:73
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:111
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:137
#: ********************************
msgid "set color"
msgstr "设置颜色"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:63
#: ********************************
msgid "Bases: :py:obj:`qfluentwidgets.components.widgets.Slider`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:85
#: ********************************
msgid "set the color of card"
msgstr "设置卡片的颜色"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:94
#: ********************************
msgid "Bases: :py:obj:`qfluentwidgets.components.widgets.line_edit.LineEdit`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:105
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:117
#: ********************************
msgid "Bases: :py:obj:`ColorLineEdit`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:127
#: ********************************
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.dialog_box.mask_dialog_base.MaskDialogBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:142
#: ********************************
msgid "update style sheet"
msgstr "更新样式表"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/color_dialog/index.rst:147
#: ********************************
msgid "fade in"
msgstr "淡入"

