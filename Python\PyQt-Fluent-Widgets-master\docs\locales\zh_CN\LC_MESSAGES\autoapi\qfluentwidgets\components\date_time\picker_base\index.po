# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON>o
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-22 20:49+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:2
#: a0383fc65cb243039aae3b1c6521eed2
msgid "picker_base"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:8
#: 2ab836cd9eeb4af1aa2d523488304d33
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:26:<autosummary>:1
#: a852507788c54f739399c324903faae4
msgid ""
":py:obj:`SeparatorWidget "
"<qfluentwidgets.components.date_time.picker_base.SeparatorWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:37
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:26:<autosummary>:1
#: 8cce2f7231374573ae466ff902be911a e6006f75bcc744ba800f06851cadcc8c
msgid "Separator widget"
msgstr "分割符小部件"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:26:<autosummary>:1
#: e81f68865731492cb5d2f522ae5f049f
msgid ""
":py:obj:`ItemMaskWidget "
"<qfluentwidgets.components.date_time.picker_base.ItemMaskWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:44
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:26:<autosummary>:1
#: c7720b51720f4f468ce334492b0c7107 f8320a4da73b4faa96be62457d33e848
msgid "Item mask widget"
msgstr "遮罩部件"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:26:<autosummary>:1
#: 5df21faea6664949bc4c494e9e2e2ae2
msgid ""
":py:obj:`PickerColumnFormatter "
"<qfluentwidgets.components.date_time.picker_base.PickerColumnFormatter>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:54
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:26:<autosummary>:1
#: 861de0d2bd3c46b9b7023174f82e4f84 dc75e1a3a8fd4d2e88c956e66f3a408c
msgid "Picker column formatter"
msgstr "选择器列格式化器"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:26:<autosummary>:1
#: f5de8e644e324f539c635ed0a7332200
msgid ""
":py:obj:`DigitFormatter "
"<qfluentwidgets.components.date_time.picker_base.DigitFormatter>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:71
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:26:<autosummary>:1
#: 2539ea73ce8840509b44e113f093da91 6c66d10f5a4841359140d00dce5af853
msgid "Digit formatter"
msgstr "数字格式化器"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:26:<autosummary>:1
#: 521122586c72467aa4934ee865b5ee8c
msgid ""
":py:obj:`PickerColumnButton "
"<qfluentwidgets.components.date_time.picker_base.PickerColumnButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:83
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:26:<autosummary>:1
#: 498a036bd9d24eea8b3ed7e7accab20d d7de3203098d4740a86f974d8bf1d745
msgid "Picker column button"
msgstr "选择器列按钮"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:26:<autosummary>:1
#: 9be0bfbf80864dbca31ed16f2c95389b
msgid ""
":py:obj:`PickerBase "
"<qfluentwidgets.components.date_time.picker_base.PickerBase>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:127
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:26:<autosummary>:1
#: 4819da9c47a346768c1a4513f91cefc6 e034aceba5ff4d04bb568c42cd696658
msgid "Picker base class"
msgstr "选择器基类"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:26:<autosummary>:1
#: 2401cfaa87a5452594a06ffaf82062e3
msgid ""
":py:obj:`PickerToolButton "
"<qfluentwidgets.components.date_time.picker_base.PickerToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:248
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:26:<autosummary>:1
#: 219eaad93b51433e9a7a3b31c0d618ad 54e399553076488ab9e8c24b1e3336b0
msgid "Picker tool button"
msgstr "选择器工具按钮"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:26:<autosummary>:1
#: 6019a5a9245f4ac4914360d4e79c0cef
msgid ""
":py:obj:`PickerPanel "
"<qfluentwidgets.components.date_time.picker_base.PickerPanel>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:255
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:26:<autosummary>:1
#: 87d729ab3c3d4977aad27218d7cf9762 d6819251ed3f409483d4a9453ed1f8d2
msgid "picker panel"
msgstr "选择器面板"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:32:<autosummary>:1
#: 893d928009814030b1ea7c31d76fa125
msgid ""
":py:obj:`checkColumnIndex "
"<qfluentwidgets.components.date_time.picker_base.checkColumnIndex>`\\ "
"\\(func\\)"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:120
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:32:<autosummary>:1
#: 22a230ad2bd74adf884d99d2d15d8cc7 f758c323b2864d7faf4f110f0caa9cf5
msgid "check whether the index is out of range"
msgstr "检查索引是否越界装饰器"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:35
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:42
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:253
#: 2c9b101ca8894d95a096e8802fe7fa32 3a757d6dc4db4ea8afadda9a72601b1d
#: 9165ecdefcec472197faea02b172c722
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:52
#: 5ec23595475548ccad8e76b734ff7b4d
msgid "Bases: :py:obj:`PyQt5.QtCore.QObject`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:58
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:191
#: 2d40091006de48a384957e2608840e67 4ac62f772edd482fa798e3baaf519c87
msgid "convert original value to formatted value"
msgstr "将原始值转换为格式化后的字符串"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:63
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:75
#: 124108c625d746f8b17f8d80c7e1b976 1f2d20a1ddc84aabb2b8f51f174c0f8e
msgid "convert formatted value to original value"
msgstr "将格式化后的字符串转换为原始值"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:69
#: 55747a8720b14352b9bb5120c43ca55e
msgid "Bases: :py:obj:`PickerColumnFormatter`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:81
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:125
#: 1f3ad100339b473d8a82e4b5c727e828 edbe48729e04467f894b3f75ce6ad470
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QPushButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:90
#: 128cdf50fd3c46ff8c3e7eee0132bb74
msgid "set the text alignment"
msgstr "设置文本对齐方式"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:131
#: d5682da9e9f84e818c56f006b7db61ff
msgid "add column"
msgstr "添加列"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:134
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:204
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:275
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:319
#: 76bb76a6134f4a54b5d4ed6a8eccfdc6 ae88c22735ab4bf39d5f50a18bc1fe67
#: c04ea35ec9cb43e6bab844ef1f5c3ce2 dd492371ab80492baaaa8406f4c36a11
msgid "Parameters"
msgstr "参数"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:136
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:209
#: 7d4cdc4fe98e4987b28846de420bf18b 847596d34bb144a5a89b16547da9262d
msgid "name: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:136
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:209
#: 94e82ebba72b47dba0e81b5daae15360 f19a089572ef4def8b422e3f7b47af48
msgid "the name of column"
msgstr "列的名字"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:139
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:212
#: 461fab65530b4f1ebc3d5f85d83aa01a 7929934175d047d6a637732590cb7b38
msgid "items: Iterable"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:139
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:212
#: 95e21418f52d498ba408341213034835 d92e1a007f224a97808cf6c8b90a35e2
msgid "the items of column"
msgstr "列的选项列表"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:142
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:215
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:280
#: 1694a5befc6a4d9c80b9a554c68aea31 367af086b33648088548cb06408996e0
#: b6f5d57b1a8947f2b4711c9b726c1419
msgid "width: int"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:142
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:215
#: 8633961938eb44f583e715a204d64564 997e5d6a0e7a4a89aeaaff21e4163fb0
msgid "the width of column"
msgstr "列的宽度"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:145
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:217
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:282
#: a9d2c722525841eb850f773354caea9a c562ed87ee404942ae22657170144cd8
#: e81e60ddb93c4aa28c22ad8609cfb557
msgid "align: Qt.AlignmentFlag"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:145
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:218
#: 1cbf9acd32524ed984ca8b105dd43ccc c3129064657843edbab5e1ffd1715aaf
msgid "the text alignment of button"
msgstr "文本对齐方式"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:147
#: 95dc0b8fb65a486baeec40a2add2093c
msgid "formatter: PickerColumnFormatter"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:148
#: 15b4646c11b647fc8ab3aabef2ea576a
msgid "the formatter of column"
msgstr ""列的格式化器

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:153
#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:168
#: e7530fce57bb4811b2c5c5024c998008 f42505e37e05449683acd3a6d8931bbd
msgid "set the text alignment of specified column"
msgstr "设置指定列的文本对齐方式"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:158
#: 0bb4f0970a934ff2b0ad0a5056c5dadf
msgid "set the width of specified column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:163
#: 21ddff1d2a9e4e419207fc7efbca22d2
msgid "make the specified column to be tight"
msgstr "紧凑布局指定列"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:196
#: 5e665c34347140e7ba924a8f146d12a2
msgid "convert formatted value to origin value"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:201
#: 040aba18bba44de7a5b0d696f0343f95
msgid "set column"
msgstr "设置列"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:206
#: 9c77266a93a149f1a83f3235d3fb6dd9
msgid "index: int"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:206
#: 384363994b82409c817a3a2a8cc610ba
msgid "the index of column"
msgstr "列索引"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:223
#: e6e28f44cac2450680aa86bd4f064392
msgid "clear columns"
msgstr "清空列"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:240
#: 1bb2b7f19ac24b04ab09c9dc238e38ab
msgid "initial value of panel"
msgstr "面板的初始值"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:246
#: 829887056af74d519c15e03f8aae7662
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.widgets.button.TransparentToolButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:267
#: c6aa217f135042e1bb77aa343a8e969b
msgid "add shadow to dialog"
msgstr "给面板添加阴影"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:272
#: 29f27234ce6341229f74ee5c7327e78a
msgid "add one column to view"
msgstr "添加列"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:277
#: 034a20bb756c4d22b4772bd988062f14
msgid "items: Iterable[Any]"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:277
#: e82273003f544b35a24dc270680d922f
msgid "the items to be added"
msgstr "列的选项"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:280
#: 67357023f7dc4a8d986d7b97fa24b1eb
msgid "the width of item"
msgstr "列的宽度"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:283
#: 855479968c63477198d611e57eb5fda1
msgid "the text alignment of item"
msgstr "列的文本对齐方式"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:291
#: 6c988d4ba24d494f828cd53a3ad109ee
msgid "return the value of columns"
msgstr "返回所有列的值组成的列表"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:296
#: 340f767b501f4fa592ca2fb6f88953ef
msgid "set the value of columns"
msgstr "设置所有列的值"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:301
#: 8cf19942084342ab88c0a67710fbf355
msgid "return the value of specified column"
msgstr "返回指定列的值"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:306
#: 6b38b4cac22145d4b8fffc64d3089f45
msgid "set the value of specified column"
msgstr "设置指定列的值"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:311
#: c538ea73c40b4dba8d18243e96cf6762
msgid "return the list widget of specified column"
msgstr "返回指定列的列表控件"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:316
#: 156b68d5867c41609649b48917490dac
msgid "show panel"
msgstr "显示面板"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:321
#: 41ce50a58b6643d285664a978bfbf50f
msgid "pos: QPoint"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:321
#: 64310c1500024eea8954cb4f8ee1f3f4
msgid "pop-up position"
msgstr "弹出位置"

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:323
#: 8e6e5308acbb45ebbb433482813f8f41
msgid "ani: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/date_time/picker_base/index.rst:324
#: 749dd09f74b84eefbd4b3f33fb584b9a
msgid "Whether to show pop-up animation"
msgstr "弹出时是否使用动画"

