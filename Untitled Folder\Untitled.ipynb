{"cells": [{"cell_type": "code", "execution_count": null, "id": "f05f5fbf", "metadata": {}, "outputs": [], "source": ["import xlwings as xw\n", "\n", "file = \"E:\\OneDrive-Ozec\\OneDrive - App By Cc\\新建文件夹\\SJ-2022714.xlsx\"\n", "wb = xw.Book()\n", "wb.save('1.xlsx')\n", "wb.close()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.5"}}, "nbformat": 4, "nbformat_minor": 5}