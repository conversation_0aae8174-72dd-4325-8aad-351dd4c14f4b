# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON>o
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-04 00:19+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:2
#: ********************************
msgid "combo_box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:8
#: ********************************
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:23:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`ComboItem "
"<qfluentwidgets.components.widgets.combo_box.ComboItem>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:26
#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:23:<autosummary>:1
#: ******************************** e5f9f2e685604c50a4588b0155ef9ad4
msgid "Combo box item"
msgstr "下拉框项"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:23:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`ComboBoxBase "
"<qfluentwidgets.components.widgets.combo_box.ComboBoxBase>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:36
#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:23:<autosummary>:1
#: ******************************** 653ed84d99b145468064add32cd4c366
msgid "Combo box base"
msgstr "下拉框基类"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:23:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`ComboBox "
"<qfluentwidgets.components.widgets.combo_box.ComboBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:183
#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:23:<autosummary>:1
#: ******************************** 2790d8b406c2429ca1c8971af8a53f13
msgid "Combo box"
msgstr "下拉框"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:23:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`EditableComboBox "
"<qfluentwidgets.components.widgets.combo_box.EditableComboBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:207
#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:23:<autosummary>:1
#: ******************************** a09cf2c8916a4ef382c73b86b8a5c8a8
msgid "Editable combo box"
msgstr "可编辑下拉框"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:23:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`ComboMenuItemDelegate "
"<qfluentwidgets.components.widgets.combo_box.ComboMenuItemDelegate>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:225
#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:23:<autosummary>:1
#: ******************************** c49cb7c5c0da426fbf5cbce77948c671
msgid "Combo box drop menu item delegate"
msgstr "下拉菜单项委托类"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:23:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`ComboBoxMenu "
"<qfluentwidgets.components.widgets.combo_box.ComboBoxMenu>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:235
#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:23:<autosummary>:1
#: ******************************** cb404fe7e95441b796f8b17383f9abef
msgid "Combo box menu"
msgstr "下拉菜单"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:34
#: ********************************
msgid "Bases: :py:obj:`PyQt5.QtCore.QObject`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:51
#: ********************************
msgid "add item"
msgstr "添加选项"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:54
#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:66
#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:85
#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:105
#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:115
#: ******************************** 857780ba76244e3c966be94e2a7019a9
#: a859facfb05747f1968ed305d32bb1d6 bb3b29c3e1b6431fb6b46f8d86a0be15
#: d309d18371e346df9da05f51d50c783c
msgid "Parameters"
msgstr "参数"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:56
#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:106
#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:119
#: ******************************** 803924e4dd1545e68b6c067b31f58a7a
#: c7130e6d766c4e0398ac414f585b4314
msgid "text: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:56
#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:68
#: ******************************** 4047d08d01954d8ca32f3bebf646a0e3
msgid "the text of item"
msgstr "选项的文本"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:58
#: ********************************
msgid "icon: str | QIcon | FluentIconBase"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:63
#: ********************************
msgid "add items"
msgstr "添加多个选项"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:67
#: ********************************
msgid "text: Iterable[str]"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:73
#: ********************************
msgid ""
"Removes the item at the given index from the combobox. This will update "
"the current index if the index is removed."
msgstr "移除指定索引处的选项。如果当前项被移除，该操作会更新当前选中的选项"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:82
#: ********************************
msgid "set current index"
msgstr "设置当前索引"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:86
#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:117
#: ******************************** 634739910725432583dca487abc981ee
msgid "index: int"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:87
#: ********************************
msgid "current index"
msgstr "返回当前索引"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:101
#: ********************************
msgid ""
"set the current text displayed in combo box, text should be in the item "
"list"
msgstr "设置当前选项，`text` 需要在选项中"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:107
#: ********************************
msgid "text displayed in combo box"
msgstr "当前选项"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:112
#: ********************************
msgid "set the text of item"
msgstr "设置选项的文本"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:117
#: ********************************
msgid "the index of item"
msgstr "选项的索引"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:120
#: ********************************
msgid "new text of item"
msgstr "选项的新文本"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:125
#: ********************************
msgid "Returns the data in the given index"
msgstr "返回指定索引处的数据"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:130
#: ********************************
msgid "Returns the text in the given index"
msgstr "返回指定索引处的文本"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:135
#: ********************************
msgid "Returns the icon in the given index"
msgstr "返回指定索引处的图标"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:140
#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:145
#: ******************************** 7f7f26ea3f294bc5831fe2454c87f35e
msgid "Sets the data role for the item on the given index"
msgstr "设置选项的数据"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:150
#: ********************************
msgid ""
"Returns the index of the item containing the given data, otherwise "
"returns -1"
msgstr "返回含有该数据的选项索引，如果找不到匹配的选项就返回 -1"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:155
#: ********************************
msgid ""
"Returns the index of the item containing the given text; otherwise "
"returns -1."
msgstr "返回含有该文本的选项索引，如果找不到匹配的选项就返回 -1"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:160
#: ********************************
msgid "Clears the combobox, removing all items."
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:165
#: ********************************
msgid "Returns the number of items in the combobox"
msgstr "返回选项数量"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:170
#: ********************************
msgid "Inserts item into the combobox at the given index."
msgstr "在指定索引处插入选项"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:175
#: ********************************
msgid "Inserts items into the combobox, starting at the index specified."
msgstr "在指定索引处插入多个选项"

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:181
#: ********************************
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QPushButton`, :py:obj:`ComboBoxBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:205
#: ********************************
msgid ""
"Bases: :py:obj:`qfluentwidgets.components.widgets.line_edit.LineEdit`, "
":py:obj:`ComboBoxBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:223
#: ********************************
msgid "Bases: :py:obj:`qfluentwidgets.components.widgets.menu.MenuItemDelegate`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/combo_box/index.rst:233
#: ********************************
msgid "Bases: :py:obj:`qfluentwidgets.components.widgets.menu.RoundMenu`"
msgstr ""

#~ msgid "Bases: :py:obj:`PyQt5.QtWidgets.QStyledItemDelegate`"
#~ msgstr ""

