# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON><PERSON>
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-22 20:49+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/widgets/icon_widget/index.rst:2
#: 828133972c86491e965ac53d4100b6a3
msgid "icon_widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/icon_widget/index.rst:8
#: ce81d1752d94443ba9a059b1c37a8264
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/icon_widget/index.rst:18:<autosummary>:1
#: 520a1c91de8f4901ab9b834374bb2b33
msgid ""
":py:obj:`IconWidget "
"<qfluentwidgets.components.widgets.icon_widget.IconWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/icon_widget/index.rst:23
#: ../../source/autoapi/qfluentwidgets/components/widgets/icon_widget/index.rst:18:<autosummary>:1
#: 5e6d42cdfc1e461bbc0a319d18e9d0d3 9f98fec42f5947b1959816467ccca097
msgid "Icon widget"
msgstr "图标部件"

#: ../../source/autoapi/qfluentwidgets/components/widgets/icon_widget/index.rst:21
#: b7fe2d87bf2a4342b89c2adc5cb967cd
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QWidget`"
msgstr ""

