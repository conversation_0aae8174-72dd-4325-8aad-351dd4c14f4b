<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP (Model Context Protocol) 学习演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .nav-tab:hover {
            background: #e9ecef;
        }

        .nav-tab.active {
            background: white;
            border-bottom-color: #3498db;
            color: #3498db;
        }

        .content {
            padding: 30px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .architecture-diagram {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
        }

        .component {
            display: inline-block;
            background: white;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 15px 20px;
            margin: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .component:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.15);
        }

        .component.host {
            background: #e74c3c;
            color: white;
            border-color: #c0392b;
        }

        .component.client {
            background: #f39c12;
            color: white;
            border-color: #e67e22;
        }

        .component.server {
            background: #27ae60;
            color: white;
            border-color: #229954;
        }

        .arrow {
            font-size: 24px;
            color: #3498db;
            margin: 0 10px;
        }

        .workflow-step {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 20px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .workflow-step:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .workflow-step h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .code-example {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }

        .interactive-demo {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .demo-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .demo-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .demo-btn:hover {
            background: #2980b9;
        }

        .demo-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .demo-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            min-height: 200px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-y: auto;
        }

        .message-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .message-box {
            background: white;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 15px;
            max-width: 200px;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .message-arrow {
            font-size: 30px;
            color: #3498db;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .feature-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.15);
        }

        .feature-card h3 {
            color: #3498db;
            margin-bottom: 15px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-connected {
            background: #27ae60;
        }

        .status-disconnected {
            background: #e74c3c;
        }

        .status-processing {
            background: #f39c12;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        @media (max-width: 768px) {
            .nav-tabs {
                flex-direction: column;
            }
            
            .message-flow {
                flex-direction: column;
                gap: 15px;
            }
            
            .message-arrow {
                transform: rotate(90deg);
            }
            
            .demo-controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 MCP 学习演示</h1>
            <p>Model Context Protocol - 连接AI模型与数据源的标准化协议</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview')">概述</button>
            <button class="nav-tab" onclick="showTab('architecture')">架构</button>
            <button class="nav-tab" onclick="showTab('workflow')">工作流程</button>
            <button class="nav-tab" onclick="showTab('examples')">示例</button>
            <button class="nav-tab" onclick="showTab('interactive')">交互演示</button>
        </div>

        <div class="content">
            <!-- 概述标签页 -->
            <div id="overview" class="tab-content active">
                <h2>什么是 MCP？</h2>
                <p style="font-size: 1.1em; line-height: 1.6; margin: 20px 0;">
                    Model Context Protocol (MCP) 是一个开放标准，用于标准化AI助手与数据源之间的连接方式。
                    就像USB-C为设备提供标准化连接一样，MCP为AI模型提供了连接不同数据源和工具的标准化方式。
                </p>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>🔌 标准化连接</h3>
                        <p>提供统一的协议接口，让AI模型能够连接到各种数据源和服务，无需为每个数据源开发专门的集成。</p>
                    </div>
                    <div class="feature-card">
                        <h3>🔄 灵活切换</h3>
                        <p>支持在不同的LLM提供商和供应商之间灵活切换，而无需重新开发集成代码。</p>
                    </div>
                    <div class="feature-card">
                        <h3>🛡️ 安全保障</h3>
                        <p>提供在您的基础设施内保护数据安全的最佳实践，确保敏感信息不会泄露。</p>
                    </div>
                    <div class="feature-card">
                        <h3>🚀 预构建集成</h3>
                        <p>提供不断增长的预构建集成列表，您的LLM可以直接插入使用，加速开发过程。</p>
                    </div>
                </div>

                <h3>核心优势</h3>
                <ul style="font-size: 1.1em; line-height: 1.8; margin: 20px 0; padding-left: 30px;">
                    <li><strong>简化集成：</strong>一次开发，多处使用</li>
                    <li><strong>提高安全性：</strong>数据保留在您的控制范围内</li>
                    <li><strong>增强灵活性：</strong>轻松添加新的数据源和工具</li>
                    <li><strong>促进创新：</strong>专注于核心功能而非集成细节</li>
                </ul>
            </div>

            <!-- 架构标签页 -->
            <div id="architecture" class="tab-content">
                <h2>MCP 架构组件</h2>

                <div class="architecture-diagram">
                    <h3>核心架构图</h3>
                    <div style="margin: 30px 0;">
                        <div class="component host" onclick="showComponentInfo('host')">
                            🖥️ MCP Host<br><small>(Claude Desktop, IDE)</small>
                        </div>
                        <span class="arrow">→</span>
                        <div class="component client" onclick="showComponentInfo('client')">
                            🔗 MCP Client<br><small>(协议客户端)</small>
                        </div>
                        <span class="arrow">→</span>
                        <div class="component server" onclick="showComponentInfo('server')">
                            🛠️ MCP Server<br><small>(数据/工具提供者)</small>
                        </div>
                    </div>
                    <div id="component-info" style="margin-top: 20px; padding: 15px; background: white; border-radius: 8px; display: none;">
                        <h4 id="info-title"></h4>
                        <p id="info-description"></p>
                    </div>
                </div>

                <h3>通信层次</h3>
                <div class="workflow-step">
                    <h3>🔄 协议层 (Protocol Layer)</h3>
                    <p>处理消息框架、请求/响应链接和高级通信模式。负责确保消息的正确传递和处理。</p>
                </div>

                <div class="workflow-step">
                    <h3>🚀 传输层 (Transport Layer)</h3>
                    <p>处理客户端和服务器之间的实际通信。支持多种传输机制：</p>
                    <ul style="margin-top: 10px; padding-left: 20px;">
                        <li><strong>Stdio传输：</strong>使用标准输入/输出，适用于本地进程</li>
                        <li><strong>HTTP传输：</strong>使用HTTP协议，支持远程通信</li>
                    </ul>
                </div>

                <h3>消息类型</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>📤 请求 (Requests)</h3>
                        <p>期望从另一方获得响应的消息</p>
                        <div class="code-example">
{
  "method": "list_resources",
  "params": { ... }
}</div>
                    </div>
                    <div class="feature-card">
                        <h3>📥 响应 (Results)</h3>
                        <p>对请求的成功响应</p>
                        <div class="code-example">
{
  "resources": [
    {
      "uri": "file://example.txt",
      "name": "示例文件"
    }
  ]
}</div>
                    </div>
                    <div class="feature-card">
                        <h3>⚠️ 错误 (Errors)</h3>
                        <p>表示请求失败的消息</p>
                        <div class="code-example">
{
  "code": -32602,
  "message": "Invalid params",
  "data": { ... }
}</div>
                    </div>
                    <div class="feature-card">
                        <h3>📢 通知 (Notifications)</h3>
                        <p>不期望响应的单向消息</p>
                        <div class="code-example">
{
  "method": "progress",
  "params": {
    "progress": 50,
    "total": 100
  }
}</div>
                    </div>
                </div>
            </div>

            <!-- 工作流程标签页 -->
            <div id="workflow" class="tab-content">
                <h2>MCP 工作流程详解</h2>

                <h3>连接生命周期</h3>

                <div class="workflow-step" onclick="showWorkflowDetail('init')">
                    <h3>1️⃣ 初始化阶段</h3>
                    <p>客户端发送初始化请求，协商协议版本和能力</p>
                </div>

                <div class="workflow-step" onclick="showWorkflowDetail('exchange')">
                    <h3>2️⃣ 消息交换阶段</h3>
                    <p>正常的请求-响应和通知消息交换</p>
                </div>

                <div class="workflow-step" onclick="showWorkflowDetail('terminate')">
                    <h3>3️⃣ 终止阶段</h3>
                    <p>清理连接并释放资源</p>
                </div>

                <div id="workflow-detail" style="margin-top: 30px; display: none;">
                    <h3 id="detail-title"></h3>
                    <div id="detail-content"></div>
                </div>

                <h3>典型交互流程</h3>
                <div class="message-flow">
                    <div class="message-box">
                        <strong>🤖 AI模型</strong><br>
                        <small>需要访问文件系统</small>
                    </div>
                    <div class="message-arrow">→</div>
                    <div class="message-box">
                        <strong>🔗 MCP客户端</strong><br>
                        <small>转发请求</small>
                    </div>
                    <div class="message-arrow">→</div>
                    <div class="message-box">
                        <strong>📁 文件系统服务器</strong><br>
                        <small>执行操作</small>
                    </div>
                </div>

                <div class="message-flow">
                    <div class="message-box">
                        <strong>📄 文件内容</strong><br>
                        <small>返回结果</small>
                    </div>
                    <div class="message-arrow">←</div>
                    <div class="message-box">
                        <strong>🔗 MCP客户端</strong><br>
                        <small>处理响应</small>
                    </div>
                    <div class="message-arrow">←</div>
                    <div class="message-box">
                        <strong>🤖 AI模型</strong><br>
                        <small>获得上下文</small>
                    </div>
                </div>

                <h3>实际使用场景</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>📊 数据分析</h3>
                        <p>AI模型通过MCP连接到数据库，获取数据进行分析和可视化</p>
                    </div>
                    <div class="feature-card">
                        <h3>📝 文档处理</h3>
                        <p>访问文件系统，读取、编辑和创建文档</p>
                    </div>
                    <div class="feature-card">
                        <h3>🌐 Web集成</h3>
                        <p>通过API连接到外部服务，获取实时信息</p>
                    </div>
                    <div class="feature-card">
                        <h3>🔧 开发工具</h3>
                        <p>集成Git、GitHub等开发工具，协助代码管理</p>
                    </div>
                </div>
            </div>

            <!-- 示例标签页 -->
            <div id="examples" class="tab-content">
                <h2>MCP 实际示例</h2>

                <h3>文件系统服务器示例</h3>
                <div class="code-example">
// TypeScript MCP 服务器示例
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";

const server = new Server({
  name: "filesystem-server",
  version: "1.0.0"
}, {
  capabilities: {
    resources: {},
    tools: {}
  }
});

// 处理资源列表请求
server.setRequestHandler(ListResourcesRequestSchema, async () => {
  return {
    resources: [
      {
        uri: "file:///home/<USER>/documents/",
        name: "用户文档目录",
        description: "用户的文档文件夹"
      }
    ]
  };
});

// 处理工具调用
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  if (request.params.name === "read_file") {
    const filePath = request.params.arguments.path;
    const content = await fs.readFile(filePath, 'utf-8');
    return {
      content: [{
        type: "text",
        text: content
      }]
    };
  }
});

// 连接传输层
const transport = new StdioServerTransport();
await server.connect(transport);
                </div>

                <h3>客户端配置示例</h3>
                <div class="code-example">
// Claude Desktop 配置文件 (claude_desktop_config.json)
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/Users/<USER>/Documents"
      ]
    },
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"]
    },
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "your_token_here"
      }
    }
  }
}
                </div>

                <h3>常用MCP服务器</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>📁 文件系统</h3>
                        <p><strong>功能：</strong>安全的文件操作，支持读取、写入和目录浏览</p>
                        <p><strong>用途：</strong>文档处理、代码分析、内容管理</p>
                    </div>
                    <div class="feature-card">
                        <h3>🧠 内存系统</h3>
                        <p><strong>功能：</strong>基于知识图谱的持久化内存</p>
                        <p><strong>用途：</strong>长期对话记忆、知识积累</p>
                    </div>
                    <div class="feature-card">
                        <h3>🌐 网络获取</h3>
                        <p><strong>功能：</strong>获取网页内容并转换为LLM友好格式</p>
                        <p><strong>用途：</strong>实时信息获取、网页分析</p>
                    </div>
                    <div class="feature-card">
                        <h3>🗄️ 数据库</h3>
                        <p><strong>功能：</strong>连接PostgreSQL、SQLite等数据库</p>
                        <p><strong>用途：</strong>数据查询、商业智能分析</p>
                    </div>
                </div>
            </div>

            <!-- 交互演示标签页 -->
            <div id="interactive" class="tab-content">
                <h2>MCP 交互演示</h2>
                <p>通过下面的模拟演示了解MCP的工作原理</p>

                <div class="interactive-demo">
                    <h3>🔄 连接状态</h3>
                    <div style="margin: 15px 0;">
                        <span class="status-indicator" id="connection-status"></span>
                        <span id="connection-text">未连接</span>
                    </div>

                    <div class="demo-controls">
                        <button class="demo-btn" onclick="simulateConnection()">建立连接</button>
                        <button class="demo-btn" onclick="simulateListResources()" id="list-btn" disabled>列出资源</button>
                        <button class="demo-btn" onclick="simulateReadFile()" id="read-btn" disabled>读取文件</button>
                        <button class="demo-btn" onclick="simulateToolCall()" id="tool-btn" disabled>调用工具</button>
                        <button class="demo-btn" onclick="simulateDisconnect()" id="disconnect-btn" disabled>断开连接</button>
                        <button class="demo-btn" onclick="clearOutput()">清空输出</button>
                    </div>

                    <div class="demo-output" id="demo-output">
点击"建立连接"开始MCP演示...
                    </div>
                </div>

                <h3>📋 MCP消息示例</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>初始化请求</h3>
                        <div class="code-example">
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "initialize",
  "params": {
    "protocolVersion": "2024-11-05",
    "capabilities": {
      "roots": {
        "listChanged": true
      },
      "sampling": {}
    },
    "clientInfo": {
      "name": "Claude Desktop",
      "version": "0.7.0"
    }
  }
}
                        </div>
                    </div>
                    <div class="feature-card">
                        <h3>服务器响应</h3>
                        <div class="code-example">
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "protocolVersion": "2024-11-05",
    "capabilities": {
      "resources": {
        "subscribe": true,
        "listChanged": true
      },
      "tools": {
        "listChanged": true
      }
    },
    "serverInfo": {
      "name": "filesystem-server",
      "version": "1.0.0"
    }
  }
}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局状态
        let isConnected = false;
        let currentStep = 0;

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // 移除所有标签页的active类
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // 组件信息显示
        function showComponentInfo(type) {
            const infoDiv = document.getElementById('component-info');
            const titleEl = document.getElementById('info-title');
            const descEl = document.getElementById('info-description');

            const componentInfo = {
                host: {
                    title: 'MCP Host (主机)',
                    description: 'MCP主机是启动连接的LLM应用程序，如Claude Desktop、IDE或AI工具。它们需要访问外部数据和工具来增强AI的能力。'
                },
                client: {
                    title: 'MCP Client (客户端)',
                    description: 'MCP客户端维护与服务器的1:1连接，位于主机应用程序内部。它负责协议通信、消息路由和错误处理。'
                },
                server: {
                    title: 'MCP Server (服务器)',
                    description: 'MCP服务器是轻量级程序，通过标准化的模型上下文协议暴露特定功能。每个服务器可以提供资源、工具和提示。'
                }
            };

            const info = componentInfo[type];
            titleEl.textContent = info.title;
            descEl.textContent = info.description;
            infoDiv.style.display = 'block';
        }

        // 工作流程详情显示
        function showWorkflowDetail(phase) {
            const detailDiv = document.getElementById('workflow-detail');
            const titleEl = document.getElementById('detail-title');
            const contentEl = document.getElementById('detail-content');

            const phaseDetails = {
                init: {
                    title: '初始化阶段详解',
                    content: `
                        <div class="code-example">
1. 客户端发送 initialize 请求
   → 包含协议版本和客户端能力

2. 服务器响应 initialize 结果
   → 返回服务器能力和协议版本

3. 客户端发送 initialized 通知
   → 确认初始化完成

4. 开始正常消息交换
                        </div>
                        <p><strong>关键点：</strong>这个阶段确保客户端和服务器能够兼容，并协商双方支持的功能。</p>
                    `
                },
                exchange: {
                    title: '消息交换阶段详解',
                    content: `
                        <div class="code-example">
支持的消息模式：

• 请求-响应模式
  客户端 → 服务器：请求
  服务器 → 客户端：响应/错误

• 通知模式
  任一方 → 另一方：通知（无需响应）

• 进度报告
  长时间操作可发送进度通知
                        </div>
                        <p><strong>关键点：</strong>这是MCP的核心工作阶段，所有的数据交换和工具调用都在这里进行。</p>
                    `
                },
                terminate: {
                    title: '终止阶段详解',
                    content: `
                        <div class="code-example">
终止方式：

1. 正常关闭
   → 调用 close() 方法
   → 清理资源和连接

2. 传输断开
   → 网络连接中断
   → 进程终止

3. 错误条件
   → 协议错误
   → 超时或异常
                        </div>
                        <p><strong>关键点：</strong>正确的终止处理确保资源得到释放，避免内存泄漏和连接残留。</p>
                    `
                }
            };

            const detail = phaseDetails[phase];
            titleEl.textContent = detail.title;
            contentEl.innerHTML = detail.content;
            detailDiv.style.display = 'block';
        }

        // 模拟连接
        function simulateConnection() {
            const output = document.getElementById('demo-output');
            const statusEl = document.getElementById('connection-status');
            const statusText = document.getElementById('connection-text');

            output.textContent = '';
            appendOutput('🔄 正在建立MCP连接...\n');

            setTimeout(() => {
                appendOutput('📤 发送初始化请求:\n');
                appendOutput(JSON.stringify({
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "initialize",
                    "params": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {
                            "roots": { "listChanged": true },
                            "sampling": {}
                        },
                        "clientInfo": {
                            "name": "MCP Demo Client",
                            "version": "1.0.0"
                        }
                    }
                }, null, 2) + '\n\n');

                setTimeout(() => {
                    appendOutput('📥 收到服务器响应:\n');
                    appendOutput(JSON.stringify({
                        "jsonrpc": "2.0",
                        "id": 1,
                        "result": {
                            "protocolVersion": "2024-11-05",
                            "capabilities": {
                                "resources": { "subscribe": true },
                                "tools": { "listChanged": true }
                            },
                            "serverInfo": {
                                "name": "demo-server",
                                "version": "1.0.0"
                            }
                        }
                    }, null, 2) + '\n\n');

                    appendOutput('✅ MCP连接建立成功！\n');

                    // 更新状态
                    isConnected = true;
                    statusEl.className = 'status-indicator status-connected';
                    statusText.textContent = '已连接';

                    // 启用按钮
                    document.getElementById('list-btn').disabled = false;
                    document.getElementById('read-btn').disabled = false;
                    document.getElementById('tool-btn').disabled = false;
                    document.getElementById('disconnect-btn').disabled = false;
                }, 1000);
            }, 500);
        }

        // 模拟列出资源
        function simulateListResources() {
            if (!isConnected) return;

            appendOutput('\n🔍 请求资源列表...\n');
            appendOutput('📤 发送请求:\n');
            appendOutput(JSON.stringify({
                "jsonrpc": "2.0",
                "id": 2,
                "method": "resources/list"
            }, null, 2) + '\n\n');

            setTimeout(() => {
                appendOutput('📥 收到资源列表:\n');
                appendOutput(JSON.stringify({
                    "jsonrpc": "2.0",
                    "id": 2,
                    "result": {
                        "resources": [
                            {
                                "uri": "file:///home/<USER>/documents/report.pdf",
                                "name": "项目报告",
                                "description": "2024年度项目总结报告",
                                "mimeType": "application/pdf"
                            },
                            {
                                "uri": "file:///home/<USER>/documents/data.csv",
                                "name": "数据文件",
                                "description": "销售数据统计表",
                                "mimeType": "text/csv"
                            }
                        ]
                    }
                }, null, 2) + '\n\n');
            }, 800);
        }

        // 模拟读取文件
        function simulateReadFile() {
            if (!isConnected) return;

            appendOutput('\n📖 读取文件内容...\n');
            appendOutput('📤 发送读取请求:\n');
            appendOutput(JSON.stringify({
                "jsonrpc": "2.0",
                "id": 3,
                "method": "resources/read",
                "params": {
                    "uri": "file:///home/<USER>/documents/data.csv"
                }
            }, null, 2) + '\n\n');

            setTimeout(() => {
                appendOutput('📥 收到文件内容:\n');
                appendOutput(JSON.stringify({
                    "jsonrpc": "2.0",
                    "id": 3,
                    "result": {
                        "contents": [
                            {
                                "uri": "file:///home/<USER>/documents/data.csv",
                                "mimeType": "text/csv",
                                "text": "日期,销售额,客户数\n2024-01-01,15000,25\n2024-01-02,18000,30\n2024-01-03,12000,20"
                            }
                        ]
                    }
                }, null, 2) + '\n\n');
            }, 1000);
        }

        // 模拟工具调用
        function simulateToolCall() {
            if (!isConnected) return;

            appendOutput('\n🔧 调用分析工具...\n');
            appendOutput('📤 发送工具调用请求:\n');
            appendOutput(JSON.stringify({
                "jsonrpc": "2.0",
                "id": 4,
                "method": "tools/call",
                "params": {
                    "name": "analyze_data",
                    "arguments": {
                        "file_path": "/home/<USER>/documents/data.csv",
                        "analysis_type": "summary"
                    }
                }
            }, null, 2) + '\n\n');

            setTimeout(() => {
                appendOutput('📥 收到分析结果:\n');
                appendOutput(JSON.stringify({
                    "jsonrpc": "2.0",
                    "id": 4,
                    "result": {
                        "content": [
                            {
                                "type": "text",
                                "text": "数据分析结果：\n- 总销售额: 45,000元\n- 平均销售额: 15,000元\n- 总客户数: 75人\n- 平均客户数: 25人/天"
                            }
                        ]
                    }
                }, null, 2) + '\n\n');
            }, 1200);
        }

        // 模拟断开连接
        function simulateDisconnect() {
            if (!isConnected) return;

            appendOutput('\n🔌 断开MCP连接...\n');

            setTimeout(() => {
                appendOutput('✅ 连接已安全断开\n');

                // 更新状态
                isConnected = false;
                const statusEl = document.getElementById('connection-status');
                const statusText = document.getElementById('connection-text');
                statusEl.className = 'status-indicator status-disconnected';
                statusText.textContent = '未连接';

                // 禁用按钮
                document.getElementById('list-btn').disabled = true;
                document.getElementById('read-btn').disabled = true;
                document.getElementById('tool-btn').disabled = true;
                document.getElementById('disconnect-btn').disabled = true;
            }, 500);
        }

        // 清空输出
        function clearOutput() {
            document.getElementById('demo-output').textContent = '点击"建立连接"开始MCP演示...';
        }

        // 添加输出文本
        function appendOutput(text) {
            const output = document.getElementById('demo-output');
            output.textContent += text;
            output.scrollTop = output.scrollHeight;
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 设置初始连接状态
            const statusEl = document.getElementById('connection-status');
            statusEl.className = 'status-indicator status-disconnected';

            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === '1') {
                    e.preventDefault();
                    showTab('overview');
                }
                if (e.ctrlKey && e.key === '2') {
                    e.preventDefault();
                    showTab('architecture');
                }
                if (e.ctrlKey && e.key === '3') {
                    e.preventDefault();
                    showTab('workflow');
                }
                if (e.ctrlKey && e.key === '4') {
                    e.preventDefault();
                    showTab('examples');
                }
                if (e.ctrlKey && e.key === '5') {
                    e.preventDefault();
                    showTab('interactive');
                }
            });
        });
    </script>
</body>
</html>
