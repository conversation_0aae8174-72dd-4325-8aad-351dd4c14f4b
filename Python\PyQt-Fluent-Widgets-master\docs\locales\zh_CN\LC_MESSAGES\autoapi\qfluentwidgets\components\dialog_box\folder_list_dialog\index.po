# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON><PERSON>
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-22 20:49+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:2
#: ********************************
msgid "folder_list_dialog"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:8
#: ********************************
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:21:<autosummary>:1
#: cefee160ea7a4bc0b2657c25c45b1cdd
msgid ""
":py:obj:`FolderListDialog "
"<qfluentwidgets.components.dialog_box.folder_list_dialog.FolderListDialog>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:26
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:21:<autosummary>:1
#: 511aaf0109184f8a884a48e54a0dbab2 87f411d23dd742278b2d31cf4f1ce23b
msgid "Folder list dialog box"
msgstr "文件夹列表对话框"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:21:<autosummary>:1
#: fb819482d9b144fdbba4853640436b1a
msgid ""
":py:obj:`ClickableWindow "
"<qfluentwidgets.components.dialog_box.folder_list_dialog.ClickableWindow>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:37
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:21:<autosummary>:1
#: 8595e14087ff44bea467c481ac5a1c01 b9845c0a834343e8bea6405a414843df
msgid "Clickable window"
msgstr "可点击窗口"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:21:<autosummary>:1
#: 1acefdf8f1754ca1b15bd5da74b17876
msgid ""
":py:obj:`FolderCard "
"<qfluentwidgets.components.dialog_box.folder_list_dialog.FolderCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:65
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:21:<autosummary>:1
#: 06f7ef8390764adaa0b241e2e728a158 b34c193dd2ee40f3aa20e173c9e66369
msgid "Folder card"
msgstr "文件夹卡片"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:21:<autosummary>:1
#: 792ba91264264c22a5e27bc9c274034a
msgid ""
":py:obj:`AddFolderCard "
"<qfluentwidgets.components.dialog_box.folder_list_dialog.AddFolderCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:77
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:21:<autosummary>:1
#: 05b8c67641a44a54ba3b2540e63231e2 4c1adfc5edac4707a292c88200d67323
msgid "Add folder card"
msgstr "添加文件夹卡片"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:24
#: ********************************
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.dialog_box.mask_dialog_base.MaskDialogBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:35
#: ********************************
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:57
#: ********************************
msgid "paint window"
msgstr "绘制窗口"

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:63
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:75
#: ******************************** 5a8bae3534844816affc6508e87055e7
msgid "Bases: :py:obj:`ClickableWindow`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:69
#: ../../source/autoapi/qfluentwidgets/components/dialog_box/folder_list_dialog/index.rst:81
#: ******************************** 7a4d0cd7741545b2a53133e075d3cf3e
msgid "paint card"
msgstr "绘制卡片"

