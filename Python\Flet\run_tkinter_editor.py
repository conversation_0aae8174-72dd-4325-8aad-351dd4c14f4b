#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动tkinter版本的PDF编辑工具
"""

import sys
import warnings

# 抑制所有字体相关的警告
warnings.filterwarnings("ignore", message=".*FontBBox.*")
warnings.filterwarnings("ignore", message=".*font descriptor.*")
warnings.filterwarnings("ignore", category=UserWarning)

def check_dependencies():
    """检查依赖库"""
    missing_deps = []
    
    try:
        import tkinter
        print("✅ tkinter - OK")
    except ImportError:
        missing_deps.append("tkinter")
    
    try:
        import PyPDF2
        print("✅ PyPDF2 - OK")
    except ImportError:
        missing_deps.append("PyPDF2")
    
    try:
        import pdfplumber
        print("✅ pdfplumber - OK")
    except ImportError:
        missing_deps.append("pdfplumber")
    
    try:
        import reportlab
        print("✅ reportlab - OK")
    except ImportError:
        missing_deps.append("reportlab")
    
    if missing_deps:
        print(f"\n❌ 缺少依赖库: {', '.join(missing_deps)}")
        print("请运行: pip install PyPDF2 pdfplumber reportlab")
        return False
    
    return True

def main():
    print("=== PDF文本替换编辑工具 (tkinter版本) ===")
    print("正在检查依赖库...")
    
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    print("\n✅ 所有依赖库检查完成")
    print("正在启动应用程序...")
    
    try:
        from pdf_editor_tkinter import main as run_app
        run_app()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
