#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文本替换编辑工具 - CustomTkinter版本
基于CustomTkinter框架开发的现代化GUI应用程序
"""

import os
import warnings
from pathlib import Path

# 抑制字体相关警告
warnings.filterwarnings("ignore", message=".*FontBBox.*")
warnings.filterwarnings("ignore", message=".*font descriptor.*")

try:
    import customtkinter as ctk
    from tkinter import filedialog, messagebox
except ImportError:
    print("请安装CustomTkinter: pip install customtkinter")
    exit(1)

try:
    import PyPDF2
    import pdfplumber
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import A4
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.lib.styles import getSampleStyleSheet
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.units import inch
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    print("请安装: pip install PyPDF2 pdfplumber reportlab")


class PDFTextEditor:
    def __init__(self):
        self.current_pdf_path = None
        self.pdf_text = ""
        self.modified_text = ""
        self.replacement_pairs = []
        
    def extract_text_from_pdf(self, pdf_path):
        """从PDF文件中提取文本"""
        try:
            text = ""
            
            # 首先尝试使用pdfplumber
            try:
                with pdfplumber.open(pdf_path) as pdf:
                    for page_num, page in enumerate(pdf.pages):
                        try:
                            page_text = page.extract_text()
                            if page_text:
                                text += page_text + "\n"
                        except Exception:
                            continue
                            
                if text.strip():
                    return text
                    
            except Exception:
                pass
            
            # 如果pdfplumber失败，尝试PyPDF2
            try:
                with open(pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page in pdf_reader.pages:
                        try:
                            page_text = page.extract_text()
                            if page_text:
                                text += page_text + "\n"
                        except Exception:
                            continue
                            
                if text.strip():
                    return text
                    
            except Exception:
                pass
            
            if not text.strip():
                raise Exception("无法从PDF中提取文本")
                
            return text
            
        except Exception as e:
            raise Exception(f"PDF文本提取失败: {str(e)}")
    
    def replace_text(self, original_text, find_text, replace_text):
        """执行文本替换"""
        if not find_text:
            return original_text
        return original_text.replace(find_text, replace_text)
    
    def create_pdf_from_text(self, text, output_path):
        """从文本创建PDF文件"""
        try:
            doc = SimpleDocTemplate(output_path, pagesize=A4)
            
            # 尝试注册中文字体
            font_name = 'Helvetica'
            try:
                font_paths = [
                    "C:/Windows/Fonts/msyh.ttc",
                    "C:/Windows/Fonts/simsun.ttc",
                    "C:/Windows/Fonts/simhei.ttf"
                ]
                
                for font_path in font_paths:
                    if os.path.exists(font_path):
                        try:
                            pdfmetrics.registerFont(TTFont('Chinese', font_path))
                            font_name = 'Chinese'
                            break
                        except:
                            continue
                            
            except Exception:
                pass
            
            # 创建样式
            styles = getSampleStyleSheet()
            normal_style = styles['Normal']
            normal_style.fontName = font_name
            normal_style.fontSize = 12
            normal_style.leading = 14
            
            # 将文本分段并创建段落
            story = []
            paragraphs = text.split('\n')
            
            for para_text in paragraphs:
                if para_text.strip():
                    try:
                        clean_text = para_text.replace('\x00', '').replace('\ufffd', '')
                        
                        if font_name == 'Helvetica':
                            clean_text = ''.join(char if ord(char) < 128 else '?' for char in clean_text)
                        
                        para = Paragraph(clean_text, normal_style)
                        story.append(para)
                        
                    except Exception:
                        try:
                            safe_text = para_text.encode('ascii', 'ignore').decode()
                            if safe_text.strip():
                                para = Paragraph(safe_text, normal_style)
                                story.append(para)
                        except:
                            continue
                else:
                    story.append(Spacer(1, 0.2*inch))
            
            if story:
                doc.build(story)
                return True
            else:
                raise Exception("没有有效内容可以创建PDF")
            
        except Exception as e:
            raise Exception(f"PDF创建失败: {str(e)}")


class PDFEditorApp:
    def __init__(self):
        # 设置外观模式和颜色主题
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title("PDF文本替换编辑工具")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 初始化PDF编辑器
        self.pdf_editor = PDFTextEditor()
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面控件"""
        # 主框架
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame, 
            text="PDF文本替换编辑工具", 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # 文件导入区域
        import_frame = ctk.CTkFrame(main_frame)
        import_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        import_label = ctk.CTkLabel(import_frame, text="📁 文件导入", font=ctk.CTkFont(size=16, weight="bold"))
        import_label.pack(pady=(15, 10))
        
        file_frame = ctk.CTkFrame(import_frame)
        file_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        self.select_button = ctk.CTkButton(
            file_frame, 
            text="选择PDF文件", 
            command=self.select_file,
            width=120
        )
        self.select_button.pack(side="left", padx=(10, 20), pady=10)
        
        self.file_path_label = ctk.CTkLabel(file_frame, text="未选择文件", text_color="gray")
        self.file_path_label.pack(side="left", pady=10)
        
        # 主要内容区域
        content_frame = ctk.CTkFrame(main_frame)
        content_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # 左侧：预览区域
        preview_frame = ctk.CTkFrame(content_frame)
        preview_frame.pack(side="left", fill="both", expand=True, padx=(20, 10), pady=20)
        
        preview_label = ctk.CTkLabel(preview_frame, text="👀 内容预览", font=ctk.CTkFont(size=16, weight="bold"))
        preview_label.pack(pady=(15, 10))
        
        self.preview_text = ctk.CTkTextbox(
            preview_frame, 
            width=500, 
            height=400,
            font=ctk.CTkFont(family="Consolas", size=12)
        )
        self.preview_text.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        # 右侧：修改区域
        edit_frame = ctk.CTkFrame(content_frame)
        edit_frame.pack(side="right", fill="y", padx=(10, 20), pady=20)
        
        edit_label = ctk.CTkLabel(edit_frame, text="🔄 文本替换", font=ctk.CTkFont(size=16, weight="bold"))
        edit_label.pack(pady=(15, 20))
        
        # 原文输入
        find_label = ctk.CTkLabel(edit_frame, text="原文:", font=ctk.CTkFont(size=14, weight="bold"))
        find_label.pack(pady=(0, 5))
        
        self.find_text = ctk.CTkTextbox(edit_frame, width=350, height=100)
        self.find_text.pack(pady=(0, 15))
        
        # 替换文本输入
        replace_label = ctk.CTkLabel(edit_frame, text="变更为:", font=ctk.CTkFont(size=14, weight="bold"))
        replace_label.pack(pady=(0, 5))
        
        self.replace_text = ctk.CTkTextbox(edit_frame, width=350, height=100)
        self.replace_text.pack(pady=(0, 20))
        
        # 按钮区域
        button_frame = ctk.CTkFrame(edit_frame)
        button_frame.pack(fill="x", padx=10, pady=(0, 20))
        
        self.replace_button = ctk.CTkButton(
            button_frame, 
            text="执行替换", 
            command=self.replace_text_action,
            fg_color="#ff9800",
            hover_color="#f57c00"
        )
        self.replace_button.pack(pady=(10, 5))
        
        self.save_button = ctk.CTkButton(
            button_frame, 
            text="保存PDF", 
            command=self.save_pdf,
            fg_color="#4caf50",
            hover_color="#45a049"
        )
        self.save_button.pack(pady=5)
        
        # 状态栏
        self.status_frame = ctk.CTkFrame(main_frame)
        self.status_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        self.status_label = ctk.CTkLabel(
            self.status_frame, 
            text="状态: 就绪", 
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(pady=10)
        
    def update_status(self, message, color="black"):
        """更新状态信息"""
        self.status_label.configure(text=f"状态: {message}", text_color=color)
        self.root.update()
        
    def select_file(self):
        """选择PDF文件"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择PDF文件",
                filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
            )
            
            if file_path:
                self.pdf_editor.current_pdf_path = file_path
                self.file_path_label.configure(text=f"已选择: {Path(file_path).name}", text_color="black")
                
                # 提取PDF文本
                self.update_status("正在提取PDF文本...", "blue")
                
                pdf_text = self.pdf_editor.extract_text_from_pdf(file_path)
                self.pdf_editor.pdf_text = pdf_text
                self.pdf_editor.modified_text = pdf_text
                
                # 更新预览
                self.preview_text.delete("1.0", "end")
                self.preview_text.insert("1.0", pdf_text)
                
                self.update_status(f"PDF加载成功，共提取 {len(pdf_text)} 个字符", "green")
                
        except Exception as e:
            self.update_status(f"错误: {str(e)}", "red")
            messagebox.showerror("错误", f"文件加载失败:\n{str(e)}")
    
    def replace_text_action(self):
        """执行文本替换"""
        try:
            if not self.pdf_editor.current_pdf_path:
                self.update_status("请先选择PDF文件", "red")
                messagebox.showwarning("警告", "请先选择PDF文件")
                return
            
            find_text = self.find_text.get("1.0", "end").strip()
            replace_text = self.replace_text.get("1.0", "end").strip()
            
            if not find_text:
                self.update_status("请输入要替换的原文", "red")
                messagebox.showwarning("警告", "请输入要替换的原文")
                return
            
            # 执行替换
            self.update_status("正在执行文本替换...", "blue")
            
            self.pdf_editor.modified_text = self.pdf_editor.replace_text(
                self.pdf_editor.modified_text, find_text, replace_text
            )
            
            # 更新预览
            self.preview_text.delete("1.0", "end")
            self.preview_text.insert("1.0", self.pdf_editor.modified_text)
            
            # 记录替换操作
            replacement_info = f"'{find_text}' → '{replace_text}'"
            if replacement_info not in [pair[2] for pair in self.pdf_editor.replacement_pairs]:
                self.pdf_editor.replacement_pairs.append((find_text, replace_text, replacement_info))
            
            self.update_status(f"替换完成: {replacement_info}", "green")
            
        except Exception as e:
            self.update_status(f"替换失败: {str(e)}", "red")
            messagebox.showerror("错误", f"文本替换失败:\n{str(e)}")
    
    def save_pdf(self):
        """保存PDF文件"""
        try:
            if not self.pdf_editor.modified_text:
                self.update_status("没有可保存的内容", "red")
                messagebox.showwarning("警告", "没有可保存的内容")
                return
            
            file_path = filedialog.asksaveasfilename(
                title="保存PDF文件",
                defaultextension=".pdf",
                filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")],
                initialfile="modified_document.pdf"
            )
            
            if file_path:
                self.update_status("正在生成PDF文件...", "blue")
                
                self.pdf_editor.create_pdf_from_text(self.pdf_editor.modified_text, file_path)
                
                self.update_status(f"PDF已保存至: {Path(file_path).name}", "green")
                messagebox.showinfo("成功", f"PDF文件已保存至:\n{file_path}")
                
        except Exception as e:
            self.update_status(f"保存失败: {str(e)}", "red")
            messagebox.showerror("错误", f"PDF保存失败:\n{str(e)}")
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    app = PDFEditorApp()
    app.run()


if __name__ == "__main__":
    main()
