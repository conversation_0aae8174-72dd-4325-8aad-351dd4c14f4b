# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON><PERSON>
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-22 20:49+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/index.rst:2 b5145793d0214379acbea23b4fa42866
msgid "API Reference"
msgstr ""

#: ../../source/autoapi/index.rst:4 8e4a7c0d07be498284f87e427478cc60
msgid "This page contains auto-generated API reference documentation [#f1]_."
msgstr ""

#: ../../source/autoapi/index.rst:11 83f60176b364453dba4edcbb08cd6c54
msgid ""
"Created with `sphinx-autoapi <https://github.com/readthedocs/sphinx-"
"autoapi>`_"
msgstr ""

