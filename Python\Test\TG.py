import requests
from bs4 import BeautifulSoup

# token = "1236955140:AAEA45sSDP4thmK7vEPxnIdQE4THeIgILt0"
# url = ""https://api.telegram.org/bot" + token + "/sendMessage"


# # res_send = requests.post(url, params=params )
# while True:
#   print('___' * 10)
#   localtime = time.asctime( time.localtime(time.time()))
#   params = {
#   ('chat_id', 617924457),
#   ('text', localtime + '    Yes, go')
#   }
#   res = requests.get('requests.get('https://coalcloud.coalcloud.net/activity/1111/#')
#   if "NAT 双11特惠" in res.text:
#     requests.post(url, params=params )
#   else:
#     print(localtime,' : No')
#   print('Wait 10 seconds')
#   time.sleep(10)

def tg_send(text):

    proxies = { "http": "http://127.0.0.1:1080", "https": "http://127.0.0.1:1080", } 

    bot_token = 'bot' + '2121710944:AAErqi24J4H-x8Cms9rcAgEn-gxooPFS7E8'
    bot_url = 'https://api.telegram.org/' + bot_token + '/sendMessage'


    params = {
        'chat_id' : 617924457 , 
        'text' : text    
    }
    send_result = requests.get(bot_url, params=params, proxies=proxies)
    return send_result.text

check_url = 'https://www.inkisp.com/cart.php?gid=56'
resp = requests.get(check_url)
soup = BeautifulSoup(resp.text, 'html.parser')
div_info = soup.find_all('div', class_='col-md-6')
for i in div_info:
    print(i.text.split())