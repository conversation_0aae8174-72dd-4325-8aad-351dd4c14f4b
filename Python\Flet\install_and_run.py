#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文本替换编辑工具 - 安装和运行脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def install_dependencies():
    """安装必要的依赖库"""
    print("正在安装依赖库...")
    
    requirements = [
        "flet>=0.21.0",
        "PyPDF2>=3.0.0", 
        "pdfplumber>=0.10.0",
        "reportlab>=4.0.0"
    ]
    
    for requirement in requirements:
        try:
            print(f"安装 {requirement}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", requirement])
            print(f"✓ {requirement} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"✗ {requirement} 安装失败: {e}")
            return False
    
    print("所有依赖库安装完成！")
    return True

def check_dependencies():
    """检查依赖库是否已安装"""
    required_modules = ["flet", "PyPDF2", "pdfplumber", "reportlab"]
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module} 已安装")
        except ImportError:
            print(f"✗ {module} 未安装")
            missing_modules.append(module)
    
    return len(missing_modules) == 0, missing_modules

def run_app():
    """运行PDF文本替换编辑工具"""
    try:
        print("启动PDF文本替换编辑工具...")
        script_dir = Path(__file__).parent
        app_script = script_dir / "pdf_text_editor.py"
        
        if not app_script.exists():
            print(f"错误: 找不到应用程序文件 {app_script}")
            return False
        
        subprocess.run([sys.executable, str(app_script)])
        return True
        
    except Exception as e:
        print(f"启动应用程序失败: {e}")
        return False

def main():
    print("=" * 50)
    print("PDF文本替换编辑工具 - 安装和运行脚本")
    print("=" * 50)
    
    # 检查依赖
    deps_ok, missing = check_dependencies()
    
    if not deps_ok:
        print(f"\n缺少依赖库: {', '.join(missing)}")
        install_choice = input("是否自动安装缺少的依赖库? (y/n): ").lower().strip()
        
        if install_choice in ['y', 'yes', '是']:
            if not install_dependencies():
                print("依赖库安装失败，请手动安装后重试。")
                return
        else:
            print("请手动安装依赖库后重试。")
            print("安装命令: pip install flet PyPDF2 pdfplumber reportlab")
            return
    
    # 运行应用程序
    print("\n准备启动应用程序...")
    input("按回车键继续...")
    
    if not run_app():
        print("应用程序启动失败。")
    else:
        print("应用程序已关闭。")

if __name__ == "__main__":
    main()
