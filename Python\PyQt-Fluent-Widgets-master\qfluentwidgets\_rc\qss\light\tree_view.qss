QTreeView {
    background-color: transparent;
    border: none;
    border-radius: 5px;
    outline: 0;
    padding-right: 5px;
    /* font: 13px 'Segoe UI', 'Microsoft YaHei'; */
    selection-background-color: transparent;
}

QTreeView::item {
    padding: 4px;
    margin-top: 2px;
    margin-bottom: 2px;
    padding-left: 20px;
    border-radius: 5px;
    color: black;
}

QTreeView::branch:has-children:!has-siblings:closed,
QTreeView::branch:closed:has-children:has-siblings {
    image: url(:/qfluentwidgets/images/tree_view/TreeViewClose_black.svg);
}

QTreeView::branch:open:has-children:!has-siblings,
QTreeView::branch:open:has-children:has-siblings {
    image: url(:/qfluentwidgets/images/tree_view/TreeViewOpen_black.svg);
}

QTreeView:disabled {
    color: rgba(0, 0, 0, 110);
}

QTreeView::indicator {
    width: 18px;
    height: 18px;
    border-radius: 5px;
    border: 1px solid rgba(0, 0, 0, 0.48);
    background-color: rgba(0, 0, 0, 0.022);
}

QTreeView::indicator:hover {
    border: 1px solid rgba(0, 0, 0, 0.56);
    background-color: rgba(0, 0, 0, 0.05);
}

QTreeView::indicator:pressed {
    border: 1px solid rgba(0, 0, 0, 0.27);
    background-color: rgba(0, 0, 0, 0.12);
}

QTreeView::indicator:checked,
QTreeView::indicator:indeterminate {
    border: 1px solid --ThemeColorPrimary;
    background-color: --ThemeColorPrimary;
}

QTreeView::indicator:checked {
    image: url(:/qfluentwidgets/images/check_box/Accept_white.svg);
}

QTreeView::indicator:indeterminate {
    image: url(:/qfluentwidgets/images/check_box/PartialAccept_white.svg);
}

QTreeView::indicator:checked:hover,
QTreeView::indicator:indeterminate:hover {
    border: 1px solid --ThemeColorLight1;
    background-color: --ThemeColorLight1;
}

QTreeView::indicator:checked:pressed,
QTreeView::indicator:indeterminate:pressed {
    border: 1px solid --ThemeColorLight3;
    background-color: --ThemeColorLight3;
}

QTreeView::indicator:disabled {
    border: 1px solid rgba(0, 0, 0, 0.27);
    background-color: transparent;
}

QTreeView::indicator:checked:disabled,
QTreeView::indicator:indeterminate:disabled {
    border: 1px solid rgb(199, 199, 199);
    background-color: rgb(199, 199, 199);
}
