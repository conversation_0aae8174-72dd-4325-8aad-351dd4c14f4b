from pathlib import Path

# 1 - 指定路径
path = Path(r'H:\视频\HTML\01-HTML\day01-HTML35课')

# 2 - 遍历文件
# for i in path.iterdir():
#     print(i)

for i in path.glob('**/*'):
    # if i.is_file() and i.suffix=='.md':
    if i.is_file() :
    # if i.is_file() :
        print('Name: ',i.name)
        # i.rename(path.joinpath( i.name[7:]) )
        print('目录： ',i.parent)
        print('---' * 20)
        # break
# print('==' * 20)
# for i in path.rglob('*'):
#     if i.is_file() and i.suffix=='.ok':

#         print(i.name)
# print('==' * 20)


# for i in path.iterdir():
#     print(i)
#     # print(i)
# # 3 - 判断是否文件
# # 4 - 重命名



print(path)
