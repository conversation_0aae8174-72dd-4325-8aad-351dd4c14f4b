// 存储书籍信息的缓存
let bookInfoCache = {};
let chapterInfoCache = {};

// 监听来自弹出窗口的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === "getBookmarks") {
        fetchBookmarks()
            .then(data => {
                sendResponse({ success: true, data: data });
            })
            .catch(error => {
                console.error("获取笔记失败:", error);
                sendResponse({ success: false, message: error.message });
            });
        return true; // 异步响应
    }
    
    if (request.action === "getBookInfo") {
        const bookIds = request.bookIds || [];
        fetchBookInfo(bookIds)
            .then(data => {
                sendResponse({ success: true, data: data });
            })
            .catch(error => {
                console.error("获取书籍信息失败:", error);
                sendResponse({ success: false, message: error.message });
            });
        return true; // 异步响应
    }
    
    if (request.action === "openBookPage") {
        const bookId = request.bookId;
        const chapterUid = request.chapterUid;
        const range = request.range;
        
        if (bookId) {
            // 计算书籍URL
            calculateBookUrl(bookId)
                .then(url => {
                    // 打开微信读书页面
                    chrome.tabs.create({ url: url });
                })
                .catch(error => {
                    console.error("计算书籍URL失败:", error);
                });
        }
    }
});

// 获取微信读书笔记
async function fetchBookmarks() {
    try {
        const response = await fetch('https://i.weread.qq.com/book/bookmarklist', {
            method: 'GET',
            credentials: 'include'
        });
        
        if (!response.ok) {
            throw new Error(`请求失败，状态码: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error("获取笔记失败:", error);
        throw error;
    }
}

// 获取书籍信息
async function fetchBookInfo(bookIds) {
    try {
        const result = {};
        
        // 获取每本书的信息
        const promises = bookIds.map(async bookId => {
            // 如果缓存中已有该书信息，直接使用缓存
            if (bookInfoCache[bookId]) {
                result[bookId] = bookInfoCache[bookId];
                return;
            }
            
            try {
                // 获取书籍基本信息
                const bookResponse = await fetch(`https://i.weread.qq.com/book/info?bookId=${bookId}`, {
                    method: 'GET',
                    credentials: 'include'
                });
                
                if (!bookResponse.ok) {
                    throw new Error(`获取书籍信息失败，状态码: ${bookResponse.status}`);
                }
                
                const bookData = await bookResponse.json();
                
                // 获取章节信息
                let chapters = {};
                if (!chapterInfoCache[bookId]) {
                    try {
                        const chapterResponse = await fetch('https://i.weread.qq.com/book/chapterInfos', {
                            method: 'POST',
                            credentials: 'include',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                bookIds: [bookId],
                                synckeys: [0],
                                teenmode: 0
                            })
                        });
                        
                        if (chapterResponse.ok) {
                            const chapterData = await chapterResponse.json();
                            if (chapterData.data && chapterData.data.length > 0 && chapterData.data[0].updated) {
                                chapterData.data[0].updated.forEach(chapter => {
                                    chapters[chapter.chapterUid] = chapter.title;
                                });
                                
                                // 缓存章节信息
                                chapterInfoCache[bookId] = chapters;
                            }
                        }
                    } catch (chapterError) {
                        console.error(`获取章节信息失败 ${bookId}:`, chapterError);
                    }
                } else {
                    chapters = chapterInfoCache[bookId];
                }
                
                // 构建书籍信息对象
                const bookInfo = {
                    title: bookData.title || "未知书籍",
                    author: bookData.author || "未知作者",
                    cover: bookData.cover || "",
                    intro: bookData.intro || "",
                    chapters: chapters
                };
                
                // 缓存书籍信息
                bookInfoCache[bookId] = bookInfo;
                result[bookId] = bookInfo;
            } catch (error) {
                console.error(`获取书籍信息失败 ${bookId}:`, error);
                result[bookId] = {
                    title: "未知书籍",
                    author: "未知作者",
                    chapters: {}
                };
            }
        });
        
        // 等待所有请求完成
        await Promise.all(promises);
        
        return result;
    } catch (error) {
        console.error("获取书籍信息失败:", error);
        throw error;
    }
}

// 计算书籍URL
async function calculateBookUrl(bookId) {
    // 使用微信读书的URL格式
    const strId = await calculateBookStrId(bookId);
    return `https://weread.qq.com/web/reader/${strId}`;
}

// 计算书籍字符串ID（参考weread_api.py中的实现）
async function calculateBookStrId(bookId) {
    // 创建MD5哈希
    const md5Digest = await createMD5Hash(bookId);
    
    // 获取前3位和后2位
    const prefix = md5Digest.substring(0, 3);
    const suffix = md5Digest.substring(md5Digest.length - 2);
    
    // 转换ID
    const [code, transformedIds] = transformId(bookId);
    
    // 构建结果
    let result = prefix + code + "2" + suffix;
    
    for (let i = 0; i < transformedIds.length; i++) {
        const hexLengthStr = transformedIds[i].length.toString(16).padStart(2, '0');
        result += hexLengthStr + transformedIds[i];
        
        if (i < transformedIds.length - 1) {
            result += "g";
        }
    }
    
    // 如果结果长度小于20，用MD5前缀补齐
    if (result.length < 20) {
        result += md5Digest.substring(0, 20 - result.length);
    }
    
    // 添加MD5后缀
    const finalMd5 = await createMD5Hash(result);
    result += finalMd5.substring(0, 3);
    
    return result;
}

// 转换ID
function transformId(bookId) {
    const idLength = bookId.length;
    
    // 检查是否为纯数字
    if (/^\d*$/.test(bookId)) {
        const ary = [];
        for (let i = 0; i < idLength; i += 9) {
            ary.push(parseInt(bookId.substring(i, Math.min(i + 9, idLength))).toString(16));
        }
        return ["3", ary];
    }
    
    // 非纯数字ID
    let result = "";
    for (let i = 0; i < idLength; i++) {
        result += bookId.charCodeAt(i).toString(16);
    }
    return ["4", [result]];
}

// 创建MD5哈希
async function createMD5Hash(message) {
    // 使用Web Crypto API计算MD5
    const msgUint8 = new TextEncoder().encode(message);
    const hashBuffer = await crypto.subtle.digest('SHA-256', msgUint8);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    
    // 由于Web Crypto API不直接支持MD5，我们使用SHA-256的前32位作为替代
    // 在实际应用中，可能需要引入专门的MD5库
    return hashHex.substring(0, 32);
}
