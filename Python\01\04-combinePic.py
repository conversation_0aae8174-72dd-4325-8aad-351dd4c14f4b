import flet as ft
import io
from PIL import Image, ImageGrab
import pyperclip  # 仅用于复制合并后的图片
from io import BytesIO
import base64
import tkinter as tk
from tkinter import filedialog
import win32clipboard

class ImageCombiner:
    def __init__(self):
        ft.app(target=self.main)
        
    def main(self, page: ft.Page):
        page.title = "图片合并工具"
        page.window_width = 1000
        page.window_height = 800
        page.padding = 20
        
        # 存储所有图片
        self.images = []
        
        # 清除所有数据的函数
        def clear_all(e):
            self.images = []  # 清空图片列表
            chat_column.controls = []  # 清空聊天区域
            preview_column.controls = []  # 清空预览区域
            page.update()
        
        # 顶部工具栏
        toolbar = ft.Row(
            [
                ft.ElevatedButton(
                    "清除所有",
                    icon=ft.icons.CLEAR_ALL,
                    on_click=clear_all,
                    style=ft.ButtonStyle(
                        color=ft.colors.RED,
                    )
                )
            ],
            alignment=ft.MainAxisAlignment.END
        )
        
        # 添加键盘事件监听函数（移到这里）
        def handle_keyboard(e: ft.KeyboardEvent):
            if e.control and e.key == "V":  # 检测 Ctrl+V
                on_paste(None)
        
        # 设置页面级别的键盘事件监听
        page.on_keyboard_event = handle_keyboard
        
        # 聊天区域
        chat_column = ft.Column(
            scroll=ft.ScrollMode.AUTO,
            spacing=10,
            height=600,
            expand=True,
        )
        
        # 预览区域
        preview_column = ft.Column(
            scroll=ft.ScrollMode.AUTO,
            spacing=10,
            height=600,
            expand=True,
        )
        
        # 输入框
        input_field = ft.TextField(
            hint_text="按Ctrl+V粘贴图片",
            expand=True,
        )
        
        # 从剪贴板获取图片
        def get_clipboard_image():
            try:
                from PIL import ImageGrab
                img = ImageGrab.grabclipboard()
                if img:
                    return img
            except Exception as e:
                print(f"获取剪贴板图片失败: {e}")
                return None
            return None
            
        # 将图片复制到剪贴板
        def copy_to_clipboard(img):
            try:
                # 保存为临时文件
                temp_path = "temp_clipboard.png"
                img.save(temp_path)
                
                # 使用 win32clipboard 复制图片
                from PIL import Image

                image = Image.open(temp_path)
                output = BytesIO()
                image.convert('RGB').save(output, 'BMP')
                data = output.getvalue()[14:]  # 去掉 BMP 文件头
                
                win32clipboard.OpenClipboard()
                win32clipboard.EmptyClipboard()
                win32clipboard.SetClipboardData(win32clipboard.CF_DIB, data)
                win32clipboard.CloseClipboard()
                
                # 删除临时文件
                import os
                os.remove(temp_path)
                
                # 提示用户
                page.show_snack_bar(ft.SnackBar(content=ft.Text("图片已复制到剪贴板")))
                page.update()
            except Exception as e:
                print(f"复制到剪贴板失败: {e}")
                page.show_snack_bar(ft.SnackBar(content=ft.Text("复制失败")))
                page.update()
            
        # 处理粘贴事件
        def on_paste(e):
            img = get_clipboard_image()
            if img:
                try:
                    # 调整预览图大小
                    preview_size = (100, int(100 * img.size[1] / img.size[0]))
                    preview_img = img.resize(preview_size)
                    
                    # 保存原图
                    self.images.append(img)
                    
                    # 转换预览图为base64
                    img_bytes = io.BytesIO()
                    preview_img.save(img_bytes, format='PNG')
                    img_base64 = base64.b64encode(img_bytes.getvalue()).decode()
                    
                    # 删除图片的处理函数
                    def delete_image(e, container):
                        # 找到当前容器在聊天区域中的索引
                        if container in chat_column.controls:
                            index = chat_column.controls.index(container)
                            # 从原图列表和聊天区域同时删除
                            if 0 <= index < len(self.images):
                                self.images.pop(index)
                                chat_column.controls.remove(container)
                                page.update()
                    
                    # 先创建行布局
                    row_content = ft.Row(
                        [
                            ft.Image(
                                src_base64=img_base64,
                                width=100,
                                fit=ft.ImageFit.CONTAIN
                            ),
                        ],
                        alignment=ft.MainAxisAlignment.START,
                    )
                    
                    # 创建消息气泡容器
                    message_bubble = ft.Container(
                        content=row_content,
                        bgcolor=ft.colors.BLUE_50,
                        border_radius=10,
                        padding=10,
                    )
                    
                    # 添加删除按钮
                    row_content.controls.append(
                        ft.IconButton(
                            icon=ft.icons.DELETE,
                            icon_color="red",
                            tooltip="删除图片",
                            on_click=lambda e: delete_image(e, message_bubble)
                        )
                    )
                    
                    # 添加到聊天区域
                    chat_column.controls.append(message_bubble)
                    
                    page.update()
                except Exception as e:
                    print(f"处理图片失败: {e}")
                
        # 定义保存文件的回调函数
        def save_file_result(e: ft.FilePickerResultEvent):
            try:
                if e.path and hasattr(self, 'combined_image'):
                    self.combined_image.save(e.path)
                    page.snack_bar = ft.SnackBar(content=ft.Text("图片已保存"))
                    page.snack_bar.open = True
            except Exception as e:
                print(f"保存图片失败: {e}")
                page.snack_bar = ft.SnackBar(content=ft.Text("保存失败"))
                page.snack_bar.open = True
            page.update()

        # 创建文件选择器
        save_file_dialog = ft.FilePicker(
            on_result=save_file_result
        )
        page.overlay.append(save_file_dialog)
        page.update()

        # 合并图片
        def combine_images(e):
            if not self.images:
                return
                
            # 算合并后的尺寸
            total_height = sum(img.size[1] for img in self.images)
            max_width = max(img.size[0] for img in self.images)
            
            # 创建新图片
            self.combined_image = Image.new('RGB', (max_width, total_height), 'white')
            
            # 从上到下粘贴图片
            y_offset = 0
            for img in self.images:
                self.combined_image.paste(img, (0, y_offset))
                y_offset += img.size[1]
                
            # 显示合并结果
            result_bytes = io.BytesIO()
            self.combined_image.save(result_bytes, format='PNG')
            result_base64 = base64.b64encode(result_bytes.getvalue()).decode()
            
            preview_column.controls = [
                ft.Image(
                    src_base64=result_base64,
                    width=400,
                    fit=ft.ImageFit.CONTAIN
                ),
                ft.Row([
                    ft.ElevatedButton("复制到剪贴板", 
                        on_click=lambda _: copy_to_clipboard(self.combined_image)),
                    ft.ElevatedButton("保存到本地", 
                        on_click=lambda _: save_file_dialog.save_file(
                            file_type=ft.FilePickerFileType.CUSTOM,
                            allowed_extensions=["png"],
                            file_name="combined_image.png"
                        ))
                ])
            ]
            page.update()
            
        # 修改主界面布局
        page.add(
            ft.Column([
                # 顶部工具栏
                ft.Container(
                    content=ft.Row(
                        [
                            ft.ElevatedButton(
                                "选择文件夹",
                                icon=ft.icons.FOLDER_OPEN,
                                on_click=lambda _: folder_picker.get_directory_path()
                            ),
                            ft.ElevatedButton(
                                "清除所有",
                                icon=ft.icons.CLEAR_ALL,
                                on_click=clear_all,
                                style=ft.ButtonStyle(
                                    color=ft.colors.RED,
                                )
                            )
                        ],
                        alignment=ft.MainAxisAlignment.END
                    ),
                    padding=ft.padding.only(bottom=10),
                ),
                
                # 主要内容区域
                ft.Row(
                    [
                        # 左侧聊天区域
                        ft.Container(
                            content=ft.Column(
                                [
                                    ft.Text("聊天区域", size=20, weight=ft.FontWeight.BOLD),
                                    chat_column,
                                    ft.Row(
                                        [ft.ElevatedButton("合并图片", on_click=combine_images)],
                                        alignment=ft.MainAxisAlignment.CENTER,
                                    ),
                                ],
                                spacing=10,
                            ),
                            padding=10,
                            border=ft.border.all(1, ft.colors.OUTLINE),
                            border_radius=10,
                            expand=True,
                        ),
                        
                        # 右侧预览区域
                        ft.Container(
                            content=ft.Column(
                                [
                                    ft.Text("预览区域", size=20, weight=ft.FontWeight.BOLD),
                                    preview_column,
                                ],
                                spacing=10,
                            ),
                            padding=10,
                            border=ft.border.all(1, ft.colors.OUTLINE),
                            border_radius=10,
                            expand=True,
                        )
                    ],
                    spacing=20,
                    expand=True,
                ),
            ],
            expand=True,
            spacing=0,
            )
        )

        # 创建文件夹选择器
        folder_picker = ft.FilePicker(
            on_result=self.handle_folder_select
        )
        page.overlay.append(folder_picker)

    def handle_folder_select(self, e: ft.FilePickerResultEvent):
        if e.path:
            try:
                # 支持的图片格式
                valid_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.gif')
                
                # 获取文件夹中的所有图片
                import os
                image_files = [
                    os.path.join(e.path, f) for f in os.listdir(e.path)
                    if f.lower().endswith(valid_extensions)
                ]
                
                # 按文件名排序
                image_files.sort()
                
                # 清空现有图片
                self.images = []
                
                # 加载所有图片
                for img_path in image_files:
                    try:
                        img = Image.open(img_path)
                        # 调整预览图大小
                        preview_size = (100, int(100 * img.size[1] / img.size[0]))
                        preview_img = img.resize(preview_size)
                        
                        # 保存原图
                        self.images.append(img)
                        
                        # 转换预览图为base64
                        img_bytes = io.BytesIO()
                        preview_img.save(img_bytes, format='PNG')
                        img_base64 = base64.b64encode(img_bytes.getvalue()).decode()
                        
                        # 创建行布局
                        row_content = ft.Row(
                            [
                                ft.Image(
                                    src_base64=img_base64,
                                    width=100,
                                    fit=ft.ImageFit.CONTAIN
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.START,
                        )
                        
                        # 创建消息气泡容器
                        message_bubble = ft.Container(
                            content=row_content,
                            bgcolor=ft.colors.BLUE_50,
                            border_radius=10,
                            padding=10,
                        )
                        
                        # 添加删除按钮
                        row_content.controls.append(
                            ft.IconButton(
                                icon=ft.icons.DELETE,
                                icon_color="red",
                                tooltip="删除图片",
                                on_click=lambda e, container=message_bubble: self.delete_image(e, container)
                            )
                        )
                        
                        # 添加到聊天区域
                        chat_column.controls.append(message_bubble)
                        
                    except Exception as e:
                        print(f"处理图片失败 {img_path}: {e}")
                
                page.update()
                
            except Exception as e:
                print(f"处理文件夹失败: {e}")
                page.show_snack_bar(ft.SnackBar(content=ft.Text("处理文件夹失败")))
                page.update()

    # 将删除图片的方法移到类级别
    def delete_image(self, e, container):
        # 找到当前容器在聊天区域中的索引
        if container in chat_column.controls:
            index = chat_column.controls.index(container)
            # 从原图列表和聊天区域同时删除
            if 0 <= index < len(self.images):
                self.images.pop(index)
                chat_column.controls.remove(container)
                page.update()

if __name__ == "__main__":
    ImageCombiner()
