ExpandSettingCard {
    border: 1px solid rgb(230, 230, 230);
    border-radius: 6px;
    background-color: rgb(252, 252, 252);
}

#view {
    background: transparent;
    border: none;
}

ExpandSettingCard[isExpand=false]>SettingCard {
    border: 1px solid rgb(230, 230, 230);
    border-radius: 6px;
    background-color: rgb(253, 253, 253);
}

ExpandSettingCard[isExpand=true]>SettingCard {
    border: 1px solid rgb(230, 230, 230);
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    background-color: rgb(253, 253, 253);
}

QLabel {
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: black;
    padding: 0;
    background: transparent;
    border: none;
}

QLabel#contentLabel {
    font: 11px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: rgb(96, 96, 96);
    padding: 0;
}

FolderItem>QLabel {
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
}

/* QPushButton */
QPushButton {
    border: 1px solid rgb(238, 239, 238);
    border-radius: 5px;
    border-bottom: 1px solid rgb(212, 213, 212);
    padding: 5px 12px 5px 36px;
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: black;
    background-color: rgb(254, 254, 254);
    outline: none;
}

QPushButton:hover {
    background-color: rgb(251, 251, 251);
}

QPushButton:pressed {
    background-color: rgb(252, 252, 252);
    border-bottom: 1px solid rgb(238, 239, 238);
    color: rgba(0, 0, 0, 0.63);
}

QPushButton:disabled {
    color: rgb(162, 162, 162);
    background: rgb(252, 252, 252);
    border: 1px solid rgb(223, 223, 223);
}


/* Tool button */
ToolButton {
    border: 1px solid rgb(238, 239, 238);
    border-radius: 5px;
    border-bottom: 1px solid rgb(212, 213, 212);
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: black;
    background-color: rgb(254, 254, 254);
    outline: none;
}

ToolButton:hover {
    background-color: rgb(251, 251, 251);
}

ToolButton:pressed {
    background-color: rgb(252, 252, 252);
    border-bottom: 1px solid rgb(238, 239, 238);
}


#chooseColorButton {
    padding: 5px 17px 5px 17px;
}
