QTableView {
    background: transparent;
    outline: none;
    border: none;
    /* font: 13px 'Segoe UI', 'Microsoft YaHei'; */
    selection-background-color: transparent;
    alternate-background-color: transparent;
}

QTableView::item {
    background: transparent;
    border: 0px;
    padding-left: 16px;
    padding-right: 16px;
    height: 35px;
}


QTableView::indicator {
    width: 18px;
    height: 18px;
    border-radius: 5px;
    border: 1px solid rgba(0, 0, 0, 0.48);
    background-color: rgba(0, 0, 0, 0.022);
}

QTableView::indicator:hover {
    border: 1px solid rgba(0, 0, 0, 0.56);
    background-color: rgba(0, 0, 0, 0.05);
}

QTableView::indicator:pressed {
    border: 1px solid rgba(0, 0, 0, 0.27);
    background-color: rgba(0, 0, 0, 0.12);
}

QTableView::indicator:checked,
QTableView::indicator:indeterminate {
    border: 1px solid --ThemeColorPrimary;
    background-color: --ThemeColorPrimary;
}

QTableView::indicator:checked {
    image: url(:/qfluentwidgets/images/check_box/Accept_white.svg);
}

QTableView::indicator:indeterminate {
    image: url(:/qfluentwidgets/images/check_box/PartialAccept_white.svg);
}

QTableView::indicator:checked:hover,
QTableView::indicator:indeterminate:hover {
    border: 1px solid --ThemeColorLight1;
    background-color: --ThemeColorLight1;
}

QTableView::indicator:checked:pressed,
QTableView::indicator:indeterminate:pressed {
    border: 1px solid --ThemeColorLight3;
    background-color: --ThemeColorLight3;
}

QTableView::indicator:disabled {
    border: 1px solid rgba(0, 0, 0, 0.27);
    background-color: transparent;
}

QTableView::indicator:checked:disabled,
QTableView::indicator:indeterminate:disabled {
    border: 1px solid rgb(199, 199, 199);
    background-color: rgb(199, 199, 199);
}


QHeaderView {
    background-color: transparent;
}

QHeaderView::section {
    background-color: transparent;
    color: black;
    padding-left: 5px;
    padding-right: 5px;
    border: 1px solid rgba(0, 0, 0, 19);
    font: 13px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
}

QHeaderView::section:horizontal {
    border-bottom: 1px solid rgba(0, 0, 0, 7);
    border-left: none;
    height: 40px;
}

QHeaderView::section:horizontal:last {
    border-right: none;
}

QHeaderView::section:vertical {
    border-top: none;
}

QHeaderView::section:checked {
    background-color: transparent;
}

QHeaderView::down-arrow {
    subcontrol-origin: padding;
    subcontrol-position: center right;
    margin-right: 6px;
    image: url(:/qfluentwidgets/images/table_view/Down_black.svg);
}

QHeaderView::up-arrow {
    subcontrol-origin: padding;
    subcontrol-position: center right;
    margin-right: 6px;
    image: url(:/qfluentwidgets/images/table_view/Up_black.svg);
}
QTableCornerButton::section {
    background-color: transparent;
    border: 1px solid rgba(0, 0, 0, 19);
}

QTableCornerButton::section:pressed {
    background-color: rgba(0, 0, 0, 12);
}