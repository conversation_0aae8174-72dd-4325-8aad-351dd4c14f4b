import socket
import subprocess
import sys
import time
from datetime import datetime

def mtr(target, max_ttl=30):
    print(f"Traceroute to {target} with a maximum TTL of {max_ttl}")
    try:
        target_ip = socket.gethostbyname(target)
    except socket.gaierror:
        print(f"Could not resolve hostname: {target}")
        return

    reached_target = False
    for ttl in range(1, max_ttl + 1):
        if reached_target:
            break

        # 使用ping命令并设置TTL
        cmd = ['ping', '-c', '1', '-m', str(ttl), target]
        start_time = datetime.now()
        
        try:
            # 执行ping命令
            result = subprocess.run(cmd, capture_output=True, text=True)
            output = result.stdout + result.stderr
            
            # 解析输出
            if "Time to live exceeded" in output:
                # 从输出中提取IP
                for line in output.split('\n'):
                    if "Time to live exceeded" in line:
                        ip = line.split("from ")[1].split(":")[0].strip()
                        latency = (datetime.now() - start_time).total_seconds() * 1000
                        print(f"{ttl}\t{ip}\t{latency:.1f} ms")
                        break
            elif "bytes from" in output:
                # 到达目标
                latency = (datetime.now() - start_time).total_seconds() * 1000
                print(f"{ttl}\t{target_ip}\t{latency:.1f} ms (reached destination)")
                reached_target = True
            else:
                print(f"{ttl}\t* * *")
        except Exception as e:
            print(f"{ttl}\t* * *")
        
        time.sleep(0.1)  # 短暂延迟，避免发送太快

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python mtr.py <target>")
        sys.exit(1)
    target = sys.argv[1]
    mtr(target)