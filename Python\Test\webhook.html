// 文件名：server.js
const express = require('express');
const cors = require('cors');
const app = express();
app.use(cors());
const port = 3000;

// 存储接收到的Webhook数据
let webhookData = null;

// Webhook接收端点
app.post('/webhook', express.json(), (req, res) => {
  webhookData = req.body;
  console.log('收到Webhook数据:', webhookData);
  res.status(200).send('OK');
});

// 提供前端页面
app.get('/', (req, res) => {
  res.sendFile(__dirname + '/index.html');
});

// 前端轮询获取数据的接口
app.get('/get-data', (req, res) => {
  res.json(webhookData);
});

app.listen(port, () => {
  console.log(`服务运行在 http://localhost:${port}`);
});
<!-- 文件名：index.html -->
<!DOCTYPE html>
<html>
<head>
  <title>Webhook演示</title>
</head>
<body>
  <h1>最新Webhook数据：</h1>
  <pre id="data"></pre>

  <script>
    // 轮询获取数据
    function fetchData() {
      fetch('http://localhost:3000/get-data')
        .then(response => response.json())
        .then(data => {
          document.getElementById('data').textContent = 
            data ? JSON.stringify(data, null, 2) : "等待数据中...";
        });
    }

    // 每2秒更新一次
    setInterval(fetchData, 2000);
    fetchData(); // 初始加载
  </script>
</body>
</html>
