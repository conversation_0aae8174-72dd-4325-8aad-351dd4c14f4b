/* Base Styles */
:root {
    --primary: #3a86ff;
    --secondary: #8ecae6;
    --hyperbolic: #ff006e;
    --dark-bg: #0f172a;
    --card-bg: #1e293b;
    --text-primary: #f8fafc;
    --text-secondary: #94a3b8;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
}



/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1rem;
}

a {
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--silicon-primary);
}

/* Layout */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 5%;
    background-color: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(10px);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
}

.logo-icon {
    font-size: 1.5rem;
    color: var(--primary);
}

nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

nav a {
    font-weight: 500;
    padding: 0.5rem 0;
    position: relative;
}

nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary);
    transition: var(--transition);
}

nav a:hover::after,
nav a.active::after {
    width: 100%;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 5rem 1rem;
    max-width: 800px;
    margin: 0 auto;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: var(--primary);
    animation: fadeInUp 1s ease;
}

.hero p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease 0.2s both;
}

/* Balance Cards */
.balance-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    justify-content: center;
    padding: 0 1rem 4rem;
}

.balance-card {
    background: var(--card-bg);
    border-radius: 1rem;
    overflow: hidden;
    width: 100%;
    max-width: 500px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
    animation: fadeInUp 1s ease;
}

.balance-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.silicon {
    border-top: 4px solid var(--primary);
}

.hyperbolic {
    border-top: 4px solid var(--hyperbolic);
}

.card-header {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.silicon .card-header h2 {
    color: var(--primary);
}

.hyperbolic .card-header h2 {
    color: var(--hyperbolic);
}

.token-icon {
    font-size: 1.5rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.silicon .token-icon {
    background: rgba(58, 134, 255, 0.1);
    color: var(--primary);
}

.hyperbolic .token-icon {
    background: rgba(255, 0, 110, 0.1);
    color: var(--hyperbolic);
}

.card-body {
    padding: 1.5rem;
}

/* API Notice */
.api-notice {
    background-color: rgba(245, 158, 11, 0.1);
    border-left: 3px solid var(--warning);
    padding: 1rem;
    margin-bottom: 1.5rem;
    border-radius: 0.25rem;
}

.api-notice p {
    color: var(--warning);
    margin: 0;
    font-size: 0.9rem;
}

.api-notice-info {
    background-color: rgba(58, 134, 255, 0.1);
    border-left: 3px solid var(--primary);
}

.api-notice-info p {
    color: var(--primary);
}

/* Form Styles */
.balance-form {
    margin-bottom: 2rem;
}

.input-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-secondary);
}

input {
    width: 100%;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition);
}

input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(58, 134, 255, 0.2);
}

.check-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.silicon .check-button {
    background: var(--primary);
    color: white;
}

.hyperbolic .check-button {
    background: var(--hyperbolic);
    color: white;
}

.check-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.silicon .check-button:hover {
    background: #2a75ff;
}

.hyperbolic .check-button:hover {
    background: #e6005f;
}

/* Result Container */
.result-container {
    display: none;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    padding: 1.5rem;
    position: relative;
}

.loader {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.silicon .spinner {
    border-top-color: var(--primary);
}

.hyperbolic .spinner {
    border-top-color: var(--hyperbolic);
}

.balance-display {
    display: none;
    text-align: center;
}

.balance-amount {
    font-size: 3rem;
    font-weight: 700;
    margin: 1.5rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.silicon .balance-amount {
    color: var(--primary);
}

.hyperbolic .balance-amount {
    color: var(--hyperbolic);
}

.token-symbol {
    font-size: 1.5rem;
    opacity: 0.7;
}

.balance-chart-container {
    margin: 2rem 0;
    height: 200px;
}

.balance-info {
    display: flex;
    justify-content: space-between;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.info-value {
    font-weight: 600;
}

/* Features Section */
.features {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
    padding: 4rem 1rem;
    background: rgba(30, 41, 59, 0.5);
}

.feature {
    text-align: center;
    max-width: 300px;
    padding: 2rem;
    border-radius: 1rem;
    background: var(--card-bg);
    transition: var(--transition);
}

.feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: var(--primary);
    color: white;
    margin: 0 auto 1.5rem;
}

/* Footer */
footer {
    background: var(--card-bg);
    padding: 3rem 1rem 1.5rem;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.footer-links ul {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--primary);
    transform: translateY(-3px);
}

.copyright {
    text-align: center;
    padding-top: 1.5rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        gap: 1rem;
    }

    nav ul {
        gap: 1rem;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .balance-cards {
        flex-direction: column;
        align-items: center;
    }

    .features {
        flex-direction: column;
        align-items: center;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .footer-links ul {
        justify-content: center;
    }

    .social-links {
        justify-content: center;
    }
}
