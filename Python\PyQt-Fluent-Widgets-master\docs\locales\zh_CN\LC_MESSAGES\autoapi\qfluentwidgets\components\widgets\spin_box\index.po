# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON>o
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-22 20:49+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:2
#: ********************************
msgid "spin_box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:8
#: ********************************
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:25:<autosummary>:1
#: ********************************
msgid ":py:obj:`SpinIcon <qfluentwidgets.components.widgets.spin_box.SpinIcon>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:30
#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:25:<autosummary>:1
#: ******************************** 76107aee14384d769bb783aa655bdfc1
msgid "Spin icon"
msgstr "微调框图标"

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:25:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`SpinButton "
"<qfluentwidgets.components.widgets.spin_box.SpinButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:25:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`Ui_SpinBox "
"<qfluentwidgets.components.widgets.spin_box.Ui_SpinBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:72
#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:25:<autosummary>:1
#: ******************************** 95f2e181e5e8454faa723c2f2dff20d6
msgid "Spin box ui"
msgstr "微调框 Ui"

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:25:<autosummary>:1
#: ********************************
msgid ":py:obj:`SpinBox <qfluentwidgets.components.widgets.spin_box.SpinBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:79
#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:25:<autosummary>:1
#: ******************************** 9076cbdb399248d9b972176c71839118
msgid "Spin box"
msgstr "微调框"

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:25:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`DoubleSpinBox "
"<qfluentwidgets.components.widgets.spin_box.DoubleSpinBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:89
#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:25:<autosummary>:1
#: ******************************** a613780db19b4160adde579d3091abc7
msgid "Double spin box"
msgstr "浮点数微调框"

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:25:<autosummary>:1
#: ********************************
msgid ":py:obj:`TimeEdit <qfluentwidgets.components.widgets.spin_box.TimeEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:99
#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:25:<autosummary>:1
#: ******************************** 6da70eed8d804e3691138f63663ad30a
msgid "Time edit"
msgstr "时间编辑框"

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:25:<autosummary>:1
#: ********************************
msgid ""
":py:obj:`DateTimeEdit "
"<qfluentwidgets.components.widgets.spin_box.DateTimeEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:109
#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:25:<autosummary>:1
#: ******************************** 70b3c37fda2c4ee6aeb8b1d89e119895
msgid "Date time edit"
msgstr "日期时间编辑框"

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:25:<autosummary>:1
#: ********************************
msgid ":py:obj:`DateEdit <qfluentwidgets.components.widgets.spin_box.DateEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:119
#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:25:<autosummary>:1
#: ******************************** 4d02ca75c0874198bf2d0e3cfec34c51
msgid "Date edit"
msgstr "日期编辑框"

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:28
#: ********************************
msgid ""
"Bases: :py:obj:`qfluentwidgets.common.icon.FluentIconBase`, "
":py:obj:`enum.Enum`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:44
#: ********************************
msgid "get the path of icon"
msgstr "返回图标路径"

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:47
#: ********************************
msgid "Parameters"
msgstr "参数"

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:51
#: ********************************
msgid "theme: Theme"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:49
#: ********************************
msgid ""
"the theme of icon * `Theme.Light`: black icon * `Theme.DARK`: white icon "
"* `Theme.AUTO`: icon color depends on `config.theme`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:58
#: ********************************
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QToolButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:77
#: ********************************
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QSpinBox`, :py:obj:`Ui_SpinBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:87
#: ********************************
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QDoubleSpinBox`, :py:obj:`Ui_SpinBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:97
#: ********************************
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTimeEdit`, :py:obj:`Ui_SpinBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:107
#: ********************************
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QDateTimeEdit`, :py:obj:`Ui_SpinBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/spin_box/index.rst:117
#: ********************************
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QDateEdit`, :py:obj:`Ui_SpinBox`"
msgstr ""

