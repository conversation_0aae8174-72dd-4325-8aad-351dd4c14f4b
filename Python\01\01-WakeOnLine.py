import customtkinter as ctk
import paramiko
from threading import Thread
import queue
import time

class WakeOnLanGUI:
    def __init__(self):
        # 设置主题
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # 创建主窗口
        self.window = ctk.CTk()
        self.window.title("远程开机工具")
        self.window.geometry("400x500")
        
        # 创建消息队列用于线程通信
        self.message_queue = queue.Queue()
        
        self._create_widgets()
        self._create_layout()
        
        # 开始定期检查消息队列
        self.window.after(100, self._check_queue)
    
    def _create_widgets(self):
        # 创建输入框和标签
        self.server_frame = ctk.CTkFrame(self.window)
        self.server_label = ctk.CTkLabel(self.server_frame, text="Server:")
        self.server_entry = ctk.CTkEntry(self.server_frame)
        self.server_entry.insert(0, "t.fn")
        
        self.username_frame = ctk.CTkFrame(self.window)
        self.username_label = ctk.CTkLabel(self.username_frame, text="Username:")
        self.username_entry = ctk.CTkEntry(self.username_frame)
        self.username_entry.insert(0, "j1900")
        
        self.password_frame = ctk.CTkFrame(self.window)
        self.password_label = ctk.CTkLabel(self.password_frame, text="Password:")
        self.password_entry = ctk.CTkEntry(self.password_frame, show="*")
        
        self.mac_frame = ctk.CTkFrame(self.window)
        self.mac_label = ctk.CTkLabel(self.mac_frame, text="MAC Address:")
        self.mac_entry = ctk.CTkEntry(self.mac_frame)
        self.mac_entry.insert(0, "d8:bb:c1:63:43:dd")
        
        # 创建按钮
        self.wake_button = ctk.CTkButton(
            self.window, 
            text="远程开机", 
            command=self._start_wake_thread
        )
        
        # 创建日志文本框
        self.log_frame = ctk.CTkFrame(self.window)
        self.log_text = ctk.CTkTextbox(
            self.log_frame, 
            height=200,
            font=("Consolas", 12)  # 使用等宽字体
        )
    
    def _create_layout(self):
        # 设置布局
        padding = 10
        
        self.server_frame.pack(fill="x", padx=padding, pady=padding)
        self.server_label.pack(side="left", padx=padding)
        self.server_entry.pack(side="left", fill="x", expand=True, padx=padding)
        
        self.username_frame.pack(fill="x", padx=padding, pady=padding)
        self.username_label.pack(side="left", padx=padding)
        self.username_entry.pack(side="left", fill="x", expand=True, padx=padding)
        
        self.password_frame.pack(fill="x", padx=padding, pady=padding)
        self.password_label.pack(side="left", padx=padding)
        self.password_entry.pack(side="left", fill="x", expand=True, padx=padding)
        
        self.mac_frame.pack(fill="x", padx=padding, pady=padding)
        self.mac_label.pack(side="left", padx=padding)
        self.mac_entry.pack(side="left", fill="x", expand=True, padx=padding)
        
        self.wake_button.pack(pady=padding)
        
        self.log_frame.pack(fill="both", expand=True, padx=padding, pady=padding)
        self.log_text.pack(fill="both", expand=True, padx=padding, pady=padding)
    
    def _log_message(self, message):
        self.message_queue.put(message)
    
    def _check_queue(self):
        # 检查消息队列并更新日志
        while not self.message_queue.empty():
            message = self.message_queue.get()
            self.log_text.insert("end", message + "\n")
            self.log_text.see("end")
        self.window.after(100, self._check_queue)
    
    def _wake_pc(self):
        try:
            # 创建SSH客户端
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 连接服务器
            self._log_message("🔌 正在连接服务器...")
            ssh.connect(hostname=self.server_entry.get(),
                       username=self.username_entry.get(),
                       password=self.password_entry.get())
            
            # 执行wakeonlan命令
            command = f'/usr/sbin/wakeonlan {self.mac_entry.get()}'
            self._log_message("\n📝 执行命令:")
            self._log_message(f"   > {command}")
            
            stdin, stdout, stderr = ssh.exec_command(command)
            
            # 等待命令完成
            exit_status = stdout.channel.recv_exit_status()
            
            if exit_status == 0:
                self._log_message("\n✅ 执行结果:")
                self._log_message("   魔法包发送成功")
            else:
                error = stderr.read().decode('utf-8').strip()
                self._log_message("\n❌ 执行失败:")
                self._log_message(f"   {error}")
                
        except Exception as e:
            self._log_message("\n❌ 错误信息:")
            self._log_message(f"   {str(e)}")
            
        finally:
            ssh.close()
            self._log_message("\n🔌 连接已关闭")
            self._log_message("-------------------")
    
    def _start_wake_thread(self):
        # 在新线程中执行wake_pc，避免界面卡顿
        Thread(target=self._wake_pc, daemon=True).start()
    
    def run(self):
        self.window.mainloop()

if __name__ == "__main__":
    app = WakeOnLanGUI()
    app.run()