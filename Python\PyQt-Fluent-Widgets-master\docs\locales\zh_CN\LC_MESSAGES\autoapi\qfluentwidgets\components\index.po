# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON>o
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-06-01 17:56+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2
#: 70ec5712cbb84a43be506f9b30656b65
msgid "components"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:22
#: fd1447265cfa43c1b8bc96cf9d8b2518
msgid "Package Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: c525a01f2fa54a6a8f8336e081e7dd4c
msgid ":py:obj:`ColorDialog <qfluentwidgets.components.ColorDialog>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:150
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 77a95669993a4f56b34d7c1bb712f901 f8546e49e6f14325a6f3a16f3ceb18be
msgid "Color dialog"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 216eadb56f4b407d9a3e7298ab00e04b
msgid ":py:obj:`Dialog <qfluentwidgets.components.Dialog>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:176
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 40cb19ce2d7e423f9a4d57ee09a3bf5a 967b6fa838fe489aa72f8d6a362e8755
msgid "Dialog box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: cff70b38740642d1a11d36967f52c941
msgid ":py:obj:`MessageBox <qfluentwidgets.components.MessageBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:194
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 5ca49f0573ab4027b9f100c3a0bb43fe 9041407e7ac74ea49e201088cd41b039
msgid "Message box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 0c823f2d17ba434ca896427a853eb90e
msgid ":py:obj:`FolderListDialog <qfluentwidgets.components.FolderListDialog>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:212
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 600097098042443c9c6f60c23391278e d27db4d3cb0249d1a07450c183e3519b
msgid "Folder list dialog box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: d52148363d5043f8a73da2534e080762
msgid ":py:obj:`MessageDialog <qfluentwidgets.components.MessageDialog>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:223
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 835b073e2bd84a88916ce8346739e957 a0d5f2d77551492cb855c7bdedc8fef7
msgid "Win10 style message dialog box with a mask"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: a5321315501f4c9fb76a565bd9ee681c
msgid ":py:obj:`ExpandLayout <qfluentwidgets.components.ExpandLayout>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:238
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 5407795e0edf475792e60ff7f08d0d8c edf08ad89b064156aac53940e3b6a03c
msgid "Expand layout"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 025f320fc8e84c5888ae7d0da8dead61
msgid ":py:obj:`FlowLayout <qfluentwidgets.components.FlowLayout>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:283
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 328f94f025e444dbb6ad3543288c650d c0d51a8e8bdc4d48b8bb3e144bde62ee
msgid "Flow layout"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: d82adf9130484fc9a5750d768c39c93a
msgid ":py:obj:`VBoxLayout <qfluentwidgets.components.VBoxLayout>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:368
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 2bea76c68be741ca9dffa6766a78f7c2 e4cc3bff734047cb971905b710dfcad2
msgid "Vertical box layout"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: a72037b8c3d3431191d4ec7f354cfa2f
msgid ":py:obj:`SettingCard <qfluentwidgets.components.SettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:400
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 0561c96a65204064baaa8e87d652bef7 27138fa7b1e642dfb95d47ffcbd55dfb
msgid "Setting card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 5fadc3d7374c40fd85c99b96b7d8575f
msgid ""
":py:obj:`SwitchSettingCard "
"<qfluentwidgets.components.SwitchSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:422
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: eb5a817b4ba84d749c6d95f012db937d f94ccf82f2c5453db9a333d97e694691
msgid "Setting card with switch button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 5eb7636a252d453fb67fb0351d50e941
msgid ":py:obj:`RangeSettingCard <qfluentwidgets.components.RangeSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:441
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 48b8ddb11f9d46f59885650c76054cc3 f6f809aeb4af4d80992d5974802a44a5
msgid "Setting card with a slider"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: e61fe3b0f82e4f6583d865751ffd671a
msgid ":py:obj:`PushSettingCard <qfluentwidgets.components.PushSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:457
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: e36cc464248f401b975d086d863ad83e fe30d073e02f44168c09778d457028a0
msgid "Setting card with a push button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 168b96670ee34421a644e5e107d22b23
msgid ":py:obj:`ColorSettingCard <qfluentwidgets.components.ColorSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:468
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: b8ef7febfd4d4b1eb133e5fb0a049410 e079ead673024689b40bba6c42b14c64
msgid "Setting card with color picker"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 6e49bef7c8184360abf0b50bb00cbbac
msgid ":py:obj:`HyperlinkCard <qfluentwidgets.components.HyperlinkCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:484
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: a0a04ff6b8234eae8192abeed56e7e68 aab4ed261d454538917b9668031e3ba4
msgid "Hyperlink card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 2af96ab186da45e6a4539eeca8fd14ff
msgid ""
":py:obj:`PrimaryPushSettingCard "
"<qfluentwidgets.components.PrimaryPushSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:491
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 55cd25fc535a4867925a4d9e1d635cdb ff4f58ac96bc4ad5a1c3cd0bba8a9efd
msgid "Push setting card with primary color"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 3e46b32fa08d4ff5a230f678c993870f
msgid ""
":py:obj:`ColorPickerButton "
"<qfluentwidgets.components.ColorPickerButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:498
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 49d147c46649480c93c83289e0570069 5f448f2e85f741fca003902c5198dbec
msgid "Color picker button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 6a9a5b3063614f46809339a02f200fa6
msgid ""
":py:obj:`ComboBoxSettingCard "
"<qfluentwidgets.components.ComboBoxSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:517
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 47d5233901e84cdaa45828c261535448 b617a4e85fb0451c896003b35cc74438
msgid "Setting card with a combo box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: fc02b863dbcb421faece373259416c46
msgid ""
":py:obj:`ExpandSettingCard "
"<qfluentwidgets.components.ExpandSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:529
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 493818b891e042ecbc2fecdda5f814d8 ce2e8d012a6449d690c3a9e0fcf0d8c4
msgid "Expandable setting card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 5cc6fb79b9174eb3947b4aa74bf5a999
msgid ""
":py:obj:`ExpandGroupSettingCard "
"<qfluentwidgets.components.ExpandGroupSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:562
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 09101515f36b4f1d885767d3f50ce0f7 bdf9eb3e02a749beb85a92ec9589ad87
msgid "Expand group setting card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 2a17018a0e8d455d9ffd4cd8456640cd
msgid ""
":py:obj:`FolderListSettingCard "
"<qfluentwidgets.components.FolderListSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:574
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 3d192da137ef49e7aef787c633f83de0 a753e12d9e4444a68c02162164250c58
msgid "Folder list setting card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 0ae6c4093eab436a9498190525906faf
msgid ""
":py:obj:`OptionsSettingCard "
"<qfluentwidgets.components.OptionsSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:585
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: a9bf227c851b49d6b0d7dc18d1fb371e f3a1f1acc9254349aa21d82b6b45ef59
msgid "setting card with a group of options"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: d7d726e81a1a4d3b8563fdc69ceb625d
msgid ""
":py:obj:`CustomColorSettingCard "
"<qfluentwidgets.components.CustomColorSettingCard>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:601
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 58d32ea3fa5a4ea3828d28097e146831 946acaae65584932a252b975a4eb9658
msgid "Custom color setting card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 4f314507cf8248ce9fe5bbc65b9ea023
msgid ":py:obj:`SettingCardGroup <qfluentwidgets.components.SettingCardGroup>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:612
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: d78a270ea4a94428936eec2b16923dc3 daa3b7dcc1204c66993736a206755e38
msgid "Setting card group"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 3713286d4fcc45fdbb3151c8fc9c1864
msgid ""
":py:obj:`DropDownPushButton "
"<qfluentwidgets.components.DropDownPushButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:632
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 0e1a09a9922d4126a1824740e2ccf597 5a3e902419dd470fb0838c0d8e1975d5
msgid "Drop down push button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 4403753b32104dc1b6123e82a7464168
msgid ""
":py:obj:`DropDownToolButton "
"<qfluentwidgets.components.DropDownToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:645
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 09c5c4152363430f8280a69aef19459e 85f1c44cdaef4f6bb350a22a32493dec
msgid "Drop down tool button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: d8886d7d52ac4dc097889b03721d4bd4
msgid ""
":py:obj:`PrimaryPushButton "
"<qfluentwidgets.components.PrimaryPushButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:658
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 67c68348f26d49e18719ecc37da27a9f c0c1066e3d8d4844a640362cc4724c31
msgid "Primary color push button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 5ceb776fad3d4f109ea220da5498c093
msgid ":py:obj:`PushButton <qfluentwidgets.components.PushButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:665
#: ../../source/autoapi/qfluentwidgets/components/index.rst:758
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 29b59948ef3845ec825b4311a8e6042d 37ce9935d7e2494587aa8314774b7353
#: 9b8371a805964f84acd4a779a1fd3c20
msgid "push button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 5afd535f2c124229aea4da4a3f611f7d
msgid ":py:obj:`RadioButton <qfluentwidgets.components.RadioButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:696
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 743829e621b84b4c97dc7774c0421f28 da42116d681647efb683d03c40bca972
msgid "Radio button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: c465fc49d7094044a152718aecd2433c
msgid ":py:obj:`HyperlinkButton <qfluentwidgets.components.HyperlinkButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:703
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: cf27e344bda644b4aae87624fa1310a9 f92bc710df404ba4b508cc5a4657abb3
msgid "Hyperlink button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: d5c45537dc38479f84cb99c8ec55f5c5
msgid ":py:obj:`ToolButton <qfluentwidgets.components.ToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:720
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 14fc9fb3d0d3456c87fb7b68d5f876f5 fd2e0c304ee541f982a091893dc2e2f0
msgid "Tool button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 97b2715cb86c4464a2ff567e529a4c6f
msgid ""
":py:obj:`TransparentToolButton "
"<qfluentwidgets.components.TransparentToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:751
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: aeb5e780d7d9457db7cab2179ca4f91b cde591b15a5c4afb8613521987f94ca7
msgid "Transparent background tool button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 6789c87d1d7049d6bb351fe363349e75
msgid ":py:obj:`ToggleButton <qfluentwidgets.components.ToggleButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 9b5d847020fd405b84036b9a10fc145e
msgid ":py:obj:`SplitWidgetBase <qfluentwidgets.components.SplitWidgetBase>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:765
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 8808bf9864554ae89abbe98fc02d70ad bfddaea516d7474a9228048652bfba43
msgid "Split widget base class"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 2eba0326e4d44a8e8c52d7a8d6431cb7
msgid ":py:obj:`SplitPushButton <qfluentwidgets.components.SplitPushButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:802
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 3d91b2b8be9e49ed85cb1bd01768d528 d848ff16593241eeb376b0d821550b34
msgid "Split push button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: a7681035e0b748758b2a49ef051d3237
msgid ":py:obj:`SplitToolButton <qfluentwidgets.components.SplitToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:836
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 873fddf005684ac28ab9bea786d74317 b893485e4d074e3588eada5c583f7f59
msgid "Split tool button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 17abe1e9f1c14d8eab154c17638a88d8
msgid ""
":py:obj:`PrimaryToolButton "
"<qfluentwidgets.components.PrimaryToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:860
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 1144eceac59f4da1834dfee5b20a6d75 9c9250dfdc3a418abd5cc25662920e53
msgid "Primary color tool button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 17a4765e623848f495fe4516ea7934ee
msgid ""
":py:obj:`PrimarySplitPushButton "
"<qfluentwidgets.components.PrimarySplitPushButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:867
#: ../../source/autoapi/qfluentwidgets/components/index.rst:874
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 462098c60e304863a32aae8187df7c95 5182d3ac064341d48c8366fac4f15e2e
#: 8dc043f529814a5e99a9e2bb97be84d5 d2421c8395a842c58fe4fb02f1a4e6ed
msgid "Primary split push button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 631f91779f8e4252a96c85522da2f397
msgid ""
":py:obj:`PrimarySplitToolButton "
"<qfluentwidgets.components.PrimarySplitToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 250e7724e5894f0cb08372e7b3bb3aae
msgid ""
":py:obj:`PrimaryDropDownPushButton "
"<qfluentwidgets.components.PrimaryDropDownPushButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:881
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 766669ad70154acb99db4361f6ade75c f61b51bca7864d32bd31797f85dbbc29
msgid "Primary color drop down push button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: c9f4582b690446629863442b51ecd013
msgid ""
":py:obj:`PrimaryDropDownToolButton "
"<qfluentwidgets.components.PrimaryDropDownToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:894
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 8a8b847b727a46acb532813a248c7530 ef247fd5893448daafdcf2c54b21f728
msgid "Primary drop down tool button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 9dd989451499498d99008a2abffe9102
msgid ":py:obj:`CheckBox <qfluentwidgets.components.CheckBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:907
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 121d7d80862946f98a0647d06df0ff79 753bde70e83146fdb9d809d7fce16adc
msgid "Check box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 54275c7d290f4c1e8a19f510ac8bbd58
msgid ":py:obj:`ComboBox <qfluentwidgets.components.ComboBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:917
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 654311f36b5f4eac85c43f0c1a80ea5a e6403e9e729e4f69bd34cbe80a73e891
msgid "Combo box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 575b3a313675402795b5b7605f688414
msgid ":py:obj:`EditableComboBox <qfluentwidgets.components.EditableComboBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:941
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 1d58b999e24a457e98ffd6f2b598aecd bf806b64f0ee4e06b5e256dbc3645689
msgid "Editable combo box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 82b2b20dd2864c0287ccce42d9bbcea5
msgid ":py:obj:`LineEdit <qfluentwidgets.components.LineEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:964
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 6016604f263c41f9a978b37448906dd2 961f9296be034d6da5a56e4667dc808f
msgid "Line edit"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: f1f1a8bdf83544ee80d3e0875adb66c7
msgid ":py:obj:`TextEdit <qfluentwidgets.components.TextEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:989
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 723f39f1b1c5420693893d06f3501bed c8103e6768f44fe4b81bd2e024bad1e9
msgid "Text edit"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: c43fb8978fa2436a8d17bba8d0c4d44b
msgid ":py:obj:`PlainTextEdit <qfluentwidgets.components.PlainTextEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:999
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 06551ed3c12b48d681bebc3020106455 ed917617269846799c0b6591d587b94d
msgid "Plain text edit"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 5b707b87ce5d4d6091da1e218fab561f
msgid ":py:obj:`LineEditButton <qfluentwidgets.components.LineEditButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1009
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 1281ac139c1f49769c784b3f97a9762b 4d6308e46f7346fba11bfe2cb4663ee9
msgid "Line edit button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 3ddada190b8645f8a896bc4fb237223c
msgid ":py:obj:`SearchLineEdit <qfluentwidgets.components.SearchLineEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1025
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: cd0d82de01b944ad87445710980d1cbc f26eedab666b47c69b7dff92aa9cfa3e
msgid "Search line edit"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 361a695e89754894a4444c808134de5f
msgid ":py:obj:`IconWidget <qfluentwidgets.components.IconWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1048
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 4b13787aa8324f58bf78a7c68fd9dca1 5e32199b4e9c48d58b8906c6dfa3ea61
msgid "Icon widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: dff87d6c6c5a426f8640ab49cbc5296d
msgid ":py:obj:`PixmapLabel <qfluentwidgets.components.PixmapLabel>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1068
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 364853f0dc384be2821a0bfafb958b05 75a4548ca43b49afa73113f6804c0a0a
msgid "Label for high dpi pixmap"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: c9976f4d1a13492c8eaefc859de36381
msgid ":py:obj:`ListWidget <qfluentwidgets.components.ListWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1084
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 3b9954a262b44996944764ad526b5502 a1ca65622cc64be8ab0c2a72680404e4
msgid "List widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 8ed051942c69444f8c75e0ccf3a7359b
msgid ":py:obj:`ListView <qfluentwidgets.components.ListView>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1097
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 056a27dca2914c3c951b22a6d5e151a0 fadf8afc64db408fb87a6d2a024113cb
msgid "List view"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: f79f4eb98dfc4ab9a336e588be8e6dd3
msgid ":py:obj:`ListItemDelegate <qfluentwidgets.components.ListItemDelegate>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1104
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 1fda17df52fb40ad9eddb46fbbf17b3b 2e6224250d11475cb132e8b8f66d858d
msgid "List item delegate"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: fad36df47f3c4529b464182b1cf64b41
msgid ":py:obj:`DWMMenu <qfluentwidgets.components.DWMMenu>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1111
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 1648b37fa0364e7d94f02e5a901a1762 55670fdb08564ba0a2edee3b0d8b467b
msgid "A menu with DWM shadow"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 603596e352784e9a9b67f82849afd88c
msgid ":py:obj:`LineEditMenu <qfluentwidgets.components.LineEditMenu>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1121
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 21b6d33b5e4a48868ba4b8318d146758 fd7ea6f3a2494e9eaff39f9ff00c4da0
msgid "Line edit menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 9f1c52e20a1846e5a9302fede9c92747
msgid ":py:obj:`RoundMenu <qfluentwidgets.components.RoundMenu>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1128
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 26855937be814cd688705aa27ddec422 d7dc04d49367468299515693862ddb00
msgid "Round corner menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 81fe7859310145b9b5ac3632387bdb8f
msgid ""
":py:obj:`MenuAnimationManager "
"<qfluentwidgets.components.MenuAnimationManager>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1275
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 2ec7e910ba1f4b9aa0f77eb277590ba8 ec415f4513bb49c4826de38f8f2e0315
msgid "Menu animation manager"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 6998acf864944170a441f13fe9e32d59
msgid ""
":py:obj:`MenuAnimationType "
"<qfluentwidgets.components.MenuAnimationType>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1304
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 16d1024e182542249be483cac06443ff 8f32ec1c92234b5c95ac29bb0f984131
msgid "Menu animation type"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: fe31a635bd7740cea9792620b8d0301a
msgid ":py:obj:`InfoBar <qfluentwidgets.components.InfoBar>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1326
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 37defed59cf8493b9004da019bae8738 76b68413b2d4428f9f991019d42f1604
msgid "Information bar"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 82ce69c03c9e441daacba852665a788e
msgid ":py:obj:`InfoBarIcon <qfluentwidgets.components.InfoBarIcon>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1384
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 126a44e5435348b289a7f1e4716cd800 641c672de55e4f018046dac49ec89d37
msgid "Info bar icon"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 4a2a8b9eb8b142678b3d80c46f0addfd
msgid ":py:obj:`InfoBarPosition <qfluentwidgets.components.InfoBarPosition>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1424
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1963
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 82a85d8cde6b437d96d2fa234d11f139 a3082c72715e4ddf9a850a4840f2a98e
#: f2b2aac3d53b42ec9709a936aa5eb8a3
msgid "Info bar position"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 3712e6231f9242838cda492d5c187d42
msgid ""
":py:obj:`SingleDirectionScrollArea "
"<qfluentwidgets.components.SingleDirectionScrollArea>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1466
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 306254806599470093cd3a900db50c0f a32c490fe58b4927ab69fd3c45525fc0
msgid "Single direction scroll area"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 5fbe8c21d386403d83f538412b9c742b
msgid ":py:obj:`SmoothMode <qfluentwidgets.components.SmoothMode>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1492
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: c5c02f782b4345b0b3758ac3193e9572 f1a8b0de21bf485ca8a7bd6eac51a590
msgid "Smooth mode"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 8ee5faee043f4b13bf3c2cfe0feef41b
msgid ":py:obj:`SmoothScrollArea <qfluentwidgets.components.SmoothScrollArea>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1524
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1547
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 42516141f6d04b1392c7b36140d85530 5f33d0281c1b412b8daf6d5e02bd23ff
#: 9e39bc341fb34293929e9fc371c064ca
msgid "Smooth scroll area"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 59b705b68d49444ab5649f606aacc7fd
msgid ":py:obj:`ScrollArea <qfluentwidgets.components.ScrollArea>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 50d6d64e76ad4c0789bbaecb562e89ee
msgid ":py:obj:`Slider <qfluentwidgets.components.Slider>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1554
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 0b440aa4a91744a9a3d13565cf5e59b5 669abf60862b45bd95cf45b2dd2954dd
msgid "A slider can be clicked"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: ff42df9f436e4ac59674e3ef1f0fa33c
msgid ""
":py:obj:`HollowHandleStyle "
"<qfluentwidgets.components.HollowHandleStyle>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1568
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 48d058fa47364ffa825aa8102fc62bc0 f9c82e7c7641458db689415c1e40a949
msgid "Hollow handle style"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 0190cff6da2a42358c6efc9af28ca264
msgid ":py:obj:`SpinBox <qfluentwidgets.components.SpinBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1585
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 51372a08ab6745ccbf51f2d03b021586 cbe10f8b5a4f427a95f70786649f669b
msgid "Spin box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: ea02cff4fad447069b235c8b4fead7ac
msgid ":py:obj:`DoubleSpinBox <qfluentwidgets.components.DoubleSpinBox>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1595
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 23df5336b78e463d836e4687ad15bb2e e6d2e93b442f479aa7bbc85fd1fd6bb0
msgid "Double spin box"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 46fbbead187e44b8b69c18a80a408a97
msgid ":py:obj:`DateEdit <qfluentwidgets.components.DateEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1605
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 9e66aa5b99dc400ab1296f15dfab66ac d69305bb940c467288dea225e3ee4c4d
msgid "Date edit"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 772858962ca6431fa0935412ea5d505e
msgid ":py:obj:`DateTimeEdit <qfluentwidgets.components.DateTimeEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1615
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 392e1bcdda4b47c59696e1784438027e 695befba7de04f1eb9293ca0e0b01bfe
msgid "Date time edit"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 55ea3be2da0b4a74bf53444cbc11c6f9
msgid ":py:obj:`TimeEdit <qfluentwidgets.components.TimeEdit>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1625
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 4ff393a08f6f4753ad7a2cfdd676f69a 860c1b140ce344ac964a5a7bf10280f9
msgid "Time edit"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 7f6701fe8c0f4ea9a6d712515cbb44c9
msgid ""
":py:obj:`PopUpAniStackedWidget "
"<qfluentwidgets.components.PopUpAniStackedWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1635
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 70b6f32331cb4a309c9f611f3599d9e9 bb5bbd8c808c45e1b6ae8919fc316e1a
msgid "Stacked widget with pop up animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: f730bf63b2d9449fad3d4ddc5f41f4f3
msgid ""
":py:obj:`OpacityAniStackedWidget "
"<qfluentwidgets.components.OpacityAniStackedWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1710
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: a60a80aa5c784dcf89f0e6ecaabcf198 d4a397dbe1494e6e80440e007dc843b5
msgid "Stacked widget with fade in and fade out animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 08954abef72b41c68e27842d56a38dad
msgid ":py:obj:`StateToolTip <qfluentwidgets.components.StateToolTip>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1726
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 70248a33a4e94100b4c3820aea38c3f9 fc0a2fe9e65949a1a03db242a6cf4d25
msgid "State tooltip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 0d76168a330e4112992358e0823f89ad
msgid ":py:obj:`SwitchButton <qfluentwidgets.components.SwitchButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1762
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 1523086eec2f44168480f176074abba7 44ddfe7f499c43799765a835080a25a0
msgid "Switch button class"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 1b9c51e8cff64a7682ab8d98f342a5df
msgid ""
":py:obj:`IndicatorPosition "
"<qfluentwidgets.components.IndicatorPosition>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1833
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 530182da8a184914b2a6143b0a53d869 ced17735380d4256beff79462211ef24
msgid "Indicator position"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: e7b04031d0054c398142cc2796325b31
msgid ":py:obj:`TableView <qfluentwidgets.components.TableView>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1850
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 50140bcc1a4d49db80550761533205ba f1cdcf3894a94f42a63fe79dfc74b020
msgid "Table view"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 1407462712dd4955a366cb254cd77c9d
msgid ":py:obj:`TableWidget <qfluentwidgets.components.TableWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1857
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 7c58b55e65ad42d1862724d8fadf7cce ddc3146ee51241bcba77bed72d0ece1f
msgid "Table widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: cc2a989f41594e15bd89e6b18131a489
msgid ""
":py:obj:`TableItemDelegate "
"<qfluentwidgets.components.TableItemDelegate>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 60fa1211300a44ba85aae77de5f69261
msgid ":py:obj:`ToolTip <qfluentwidgets.components.ToolTip>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1899
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 65ee7a0f7a014d9ebbf10ae3b00754e0 941caa2c7cee4c4b9d0177dabb763e1e
msgid "Tool tip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 9397603e87984acfa7a7e851d663d64e
msgid ":py:obj:`ToolTipFilter <qfluentwidgets.components.ToolTipFilter>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1938
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 5c00f53e3e56472b94fcc40232f8e264 dfeee339be374d628b15972bcb07c15d
msgid "Tool tip filter"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 291bfb25b1cb4f9cbac5e427ad057731
msgid ":py:obj:`ToolTipPosition <qfluentwidgets.components.ToolTipPosition>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: ecd0eb22b33b4a7a881bd86c7b25a39a
msgid ":py:obj:`TreeWidget <qfluentwidgets.components.TreeWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2010
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 280532ddd88949098c21fb16b8c5e1b8 a6496f567b914d30a699922bd557f758
msgid "Tree widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: bd6aad7fcb5c4c24af3d9023b8ace8d6
msgid ":py:obj:`TreeView <qfluentwidgets.components.TreeView>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2017
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: af4e0da7acd44dfcb950de4bfe6eaf9a fb2981d5fec34561849795f06e4ba5cf
msgid "Tree view"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: de5b2b55a1e344b0aa327f4f96ec3c29
msgid ":py:obj:`TreeItemDelegate <qfluentwidgets.components.TreeItemDelegate>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2024
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 49253e7651dc4ea3af9662ef5fd4ea8a a6390239a63c4b23a6493442c784d0d6
msgid "Tree item delegate"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: ba6ba822c5074b1a9e4f5aac6f11cd7e
msgid ":py:obj:`CycleListWidget <qfluentwidgets.components.CycleListWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2037
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 8ae73ce7f8f84a2198e4d315f16c42ef 96d8c77a715443fd967d9213df36128c
msgid "Cycle list widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: d2ba8b8215ed40c48ce4aa61084e5dad
msgid ""
":py:obj:`IndeterminateProgressBar "
"<qfluentwidgets.components.IndeterminateProgressBar>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2108
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 53d0529a6ced44339ccaf09bf9ef8543 9b18e5f2226249ae86e3ebc4cc205ec1
msgid "Indeterminate progress bar"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 69f0a1b06c6a460696a09b509e6a3373
msgid ":py:obj:`ProgressBar <qfluentwidgets.components.ProgressBar>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 2c7fe2192c0649f2a4548e1cd8f4099f
msgid ":py:obj:`ProgressRing <qfluentwidgets.components.ProgressRing>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2216
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: ae927dac35054abe9d34c7993a6da3dc df94ec76375242d9993d2f69916e5a71
msgid "Progress ring"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 27ffa2b148644c7180ae3d2e3c43ed1c
msgid ":py:obj:`ScrollBar <qfluentwidgets.components.ScrollBar>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2226
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: dd8a2ffb901643d1b3ba95f162197357 f1e0f466bc424f808b1ca4598293f556
msgid "Fluent scroll bar"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 39943a2eae2049bf865c8ef08ae39d3a
msgid ":py:obj:`SmoothScrollBar <qfluentwidgets.components.SmoothScrollBar>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2337
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 1aabbc76cd924e08a8302294aee764e0 53346996f36547f79862ef3b03a61e3b
msgid "Smooth scroll bar"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 969382703e11423690d5b35f08acf39b
msgid ""
":py:obj:`SmoothScrollDelegate "
"<qfluentwidgets.components.SmoothScrollDelegate>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2379
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 32e5d0acbf9b4707b66bfb7d467aa7a4 91adb27e80d544daacb3417d1ad3d849
msgid "Smooth scroll delegate"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 862a2485970645f8b351f9148aeefe03
msgid ":py:obj:`NavigationWidget <qfluentwidgets.components.NavigationWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2395
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 8ca77d3b0e28471493d402db37a3d607 b79752ce1f0c4ad9aa84f316ea1598c9
msgid "Navigation widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: bb6284e2388441289708d592b1d9612f
msgid ""
":py:obj:`NavigationPushButton "
"<qfluentwidgets.components.NavigationPushButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2438
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 2756a16ac2bb416a9cef9d9a43657547 82c26f3b339b47c1aa9cf4e870972c7e
msgid "Navigation push button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: fe05378ec3e4478e9bf1ec06b2ef6796
msgid ""
":py:obj:`NavigationSeparator "
"<qfluentwidgets.components.NavigationSeparator>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2460
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 8e21bb2663dd45fcb9c58caadbb29b60 91ebe492aab54e0ab440c64cd281798e
msgid "Navigation Separator"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 7a751861c94442b2a104a77ce3f73762
msgid ""
":py:obj:`NavigationToolButton "
"<qfluentwidgets.components.NavigationToolButton>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2475
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 7f8e03e0a0414cf4b2b1c59e7e912b18 87aaee98528749a08aa648d0af42a06c
msgid "Navigation tool button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 70eeb83de08641cdb423a7fcf79327c3
msgid ""
":py:obj:`NavigationTreeWidget "
"<qfluentwidgets.components.NavigationTreeWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2487
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 4b0c8f29c94d4376a219e3857c0c76e7 cce9add167ac45fb8d565abab3bddcd0
msgid "Navigation tree widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 4e10eb4254674bc2afac25cda64408d5
msgid ""
":py:obj:`NavigationTreeWidgetBase "
"<qfluentwidgets.components.NavigationTreeWidgetBase>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2577
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 6eb646dcb2f74978bf12440f9eeac84c c5039b69e2084541b38ef7eaceb36802
msgid "Navigation tree widget base class"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 3fc0af2361124d5d9dd65789b91b6f94
msgid ":py:obj:`NavigationPanel <qfluentwidgets.components.NavigationPanel>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2644
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 3e2513ac52fe4afebb60226950e231d4 e5468d7bf8d248c2ba6735139b56114c
msgid "Navigation panel"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 58ad518b8a5d42648cbf18e8edae1505
msgid ""
":py:obj:`NavigationItemPosition "
"<qfluentwidgets.components.NavigationItemPosition>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2863
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 2de1c97af1eb48bbab3d0a894f5b19db 59a147ec091d47d5a0aabe1d3c2c186e
msgid "Navigation item position"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 86add22d684b45918b58231437502778
msgid ""
":py:obj:`NavigationDisplayMode "
"<qfluentwidgets.components.NavigationDisplayMode>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2885
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 0175eeb6dc924e3da7f7239783c0546b 1710fb2c397840e89ab8fb1185bc5110
msgid "Navigation display mode"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 9a274a15e4e24180a53a8b62303ea790
msgid ""
":py:obj:`NavigationInterface "
"<qfluentwidgets.components.NavigationInterface>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2912
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 2841c296474a437e81c616d13e61659f 5746368d44534ba3879610bbc035333b
msgid "Navigation interface"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 1843b34add3e43efb2eb4fff78fa4e6a
msgid ":py:obj:`Pivot <qfluentwidgets.components.Pivot>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3103
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 2de847ae886b49b98d31b1874201cd0a d80f0de13b824381a42a2d41b31e1193
msgid "Pivot"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: c6fa91a7d1c142aa8b424b3e86802479
msgid ":py:obj:`PivotItem <qfluentwidgets.components.PivotItem>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3160
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: a241b30ffc074f8eb33a75471a4af331 c4d1bf1a50e0443997897ac48c37d2f6
msgid "Pivot item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: f5c7b45f8dca42d69ea35fc60bbace48
msgid ":py:obj:`CalendarPicker <qfluentwidgets.components.CalendarPicker>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3177
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 4e796373d67a48eabe6783f9fb1f49ca
msgid "Calendar picker"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 25e89fb6a0d341cf8d9aa4fa550ff753
msgid ":py:obj:`DatePickerBase <qfluentwidgets.components.DatePickerBase>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3199
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: b7784fa9ecb84da1b461c5f276c42de5
msgid "Date picker base class"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: f5c7b45f8dca42d69ea35fc60bbace48
msgid ":py:obj:`DatePicker <qfluentwidgets.components.DatePicker>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3234
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: e3bc10067ed04c2aa3487f51fed324e2
msgid "Date picker"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 68e69d50f88a4f1c90bfef9984e05614
msgid ":py:obj:`ZhDatePicker <qfluentwidgets.components.ZhDatePicker>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3276
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 4e796373d67a48eabe6783f9fb1f49ca
msgid "Chinese date picker"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 91a51de16b7e4f039607a0ec723828b9
msgid ":py:obj:`PickerBase <qfluentwidgets.components.PickerBase>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3283
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 4a0ac8c3c6dc475bbb99c1084a4f4af0
msgid "Picker base class"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 4110738010b9464b8cd9a8fd310f2f0d
msgid ":py:obj:`PickerPanel <qfluentwidgets.components.PickerPanel>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3404
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 16515baa246d4c42bed1565498edd18c
msgid "picker panel"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 911b6305cf7148c4b6c45dc932cbc30f
msgid ""
":py:obj:`PickerColumnFormatter "
"<qfluentwidgets.components.PickerColumnFormatter>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3481
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 808c9c8bfbb84202873f5507678239a3
msgid "Picker column formatter"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 74bb11a3b5dd41b992709e2abad2a426
msgid ":py:obj:`TimePicker <qfluentwidgets.components.TimePicker>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3498
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 7b89663fde064b5ab8c0c18ad1d953f7
msgid "24 hours time picker"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: ac90291f33f145a5b587b05041c77ae6
msgid ":py:obj:`AMTimePicker <qfluentwidgets.components.AMTimePicker>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3525
#: ../../source/autoapi/qfluentwidgets/components/index.rst:145:<autosummary>:1
#: 019643b62a264da5978f35c4b5e6a1d3
msgid "AM/PM time picker"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:148
#: ../../source/autoapi/qfluentwidgets/components/index.rst:210
#: ../../source/autoapi/qfluentwidgets/components/index.rst:221
#: 3b9e5a77da30488db06a4909da255128 78c8477e91c841a7b491dd970a628992
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.dialog_box.mask_dialog_base.MaskDialogBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:158
#: ../../source/autoapi/qfluentwidgets/components/index.rst:506
#: d4fee7e053644565acf268dd40121b12
msgid "set color"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:163
#: b41a7e2559ea4055a488c1f98c5ceee7
msgid "update style sheet"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:168
#: 072dc28f3b494c5e92862a479483a531
msgid "fade in"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:174
#: a044b08eeab4486eb2ce6cc8535b724a
msgid "Bases: :py:obj:`qframelesswindow.FramelessDialog`, :py:obj:`Ui_MessageBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:192
#: 83aec2a826a3405bbb3990d9c3467ec4
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.dialog_box.mask_dialog_base.MaskDialogBase`,"
" :py:obj:`Ui_MessageBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:236
#: ../../source/autoapi/qfluentwidgets/components/index.rst:281
#: 306378560cf049a9bb09652a50daac6a
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QLayout`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:263
#: ../../source/autoapi/qfluentwidgets/components/index.rst:331
#: c335387eae8d406dac5b6e6d8c64770e
msgid "get the minimal height according to width"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:293
#: 02ac4df66f94496bbd162726c241cea7
msgid "set the moving animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:296
#: ../../source/autoapi/qfluentwidgets/components/index.rst:786
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1168
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1183
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1208
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1243
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1259
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1290
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1342
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1411
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1479
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1531
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1650
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1666
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1688
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1917
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2048
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2179
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2366
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2428
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2494
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2519
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2529
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2559
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2585
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2596
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2607
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2628
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2658
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2689
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2714
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2748
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2776
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2786
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2799
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2839
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2923
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2954
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2979
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3013
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3041
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3051
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3064
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3074
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3110
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3126
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3145
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3251
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3290
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3360
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3424
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3468
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3505
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3537
#: 366f83e69e3149d381bad25f2bf1959d
msgid "Parameters"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:298
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1536
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1677
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1699
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1918
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2368
#: a26a117c237c4b0cb0fdaa7ea69f4b2b
msgid "duration: int"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:298
#: 3b84f96263274bcfb4b661eb24180766
msgid "the duration of animation in milliseconds"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:300
#: da84540579cc4044b0fcf00555841019
msgid "ease: QEasingCurve"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:301
#: 593d1e204d044eb9b6bd78629c90aa88
msgid "the easing curve of animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:315
#: ../../source/autoapi/qfluentwidgets/components/index.rst:392
#: 62ef4bc19d4446108fca8d4e82b75297
msgid "remove all widgets from layout"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:320
#: e843faafa8e14fb38f7d9d6816469202
msgid "remove all widgets from layout and delete them"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:345
#: 5360addf931a4c53ab1bf01a435e3484
msgid "set vertical spacing between widgets"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:350
#: 104498dae92c441abf6f84c0ed1ac9c5
msgid "get vertical spacing between widgets"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:355
#: 46dd499c774047629178097dac10b68d
msgid "set horizontal spacing between widgets"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:360
#: 30e47d5feefa4a29ae0cc8426347157b
msgid "get horizontal spacing between widgets"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:366
#: 61a1f3f016c24447bf18b4839a226693
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QVBoxLayout`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:372
#: 7f52220a202e4887b023ecc65e6e131d
msgid "add widgets to layout"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:377
#: cf04e4c94d174611bf94d491dd60e824
msgid "add widget to layout"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:382
#: 10aee4bbe0b74a558153f10e5a39e5fc
msgid "remove widget from layout but not delete it"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:387
#: 1ed84f5eae194382b3248cc28d5c3526
msgid "remove widget from layout and delete it"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:398
#: ../../source/autoapi/qfluentwidgets/components/index.rst:527
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1324
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1897
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2642
#: a426f52314624550b564f83616d62c77
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QFrame`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:404
#: ed7cc5a24388455fb60568afe0c0ec19
msgid "set the title of card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:409
#: d251f72060454ab8a0fc1b84c2675234
msgid "set the content of card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:414
#: ../../source/autoapi/qfluentwidgets/components/index.rst:430
#: ../../source/autoapi/qfluentwidgets/components/index.rst:449
#: ../../source/autoapi/qfluentwidgets/components/index.rst:476
#: ../../source/autoapi/qfluentwidgets/components/index.rst:521
#: ../../source/autoapi/qfluentwidgets/components/index.rst:554
#: b12a1c4ac32e4a19a8651f98f4f25d76
msgid "set the value of config item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:420
#: ../../source/autoapi/qfluentwidgets/components/index.rst:439
#: ../../source/autoapi/qfluentwidgets/components/index.rst:455
#: ../../source/autoapi/qfluentwidgets/components/index.rst:466
#: ../../source/autoapi/qfluentwidgets/components/index.rst:482
#: ../../source/autoapi/qfluentwidgets/components/index.rst:515
#: 212ee28569c64ba4a015c83a7473c845 a035eca3471e4291bde11b2be34a34e8
msgid "Bases: :py:obj:`SettingCard`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:489
#: fc555b3d92454abcaa4ef71768c155ac
msgid "Bases: :py:obj:`PushSettingCard`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:496
#: ../../source/autoapi/qfluentwidgets/components/index.rst:718
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1007
#: d47e75ca09e847478aedb50d8bd85678
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QToolButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:533
#: 608e06daa0f349c1a96ade3a9425c277
msgid "add widget to tail"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:538
#: f93422b092e64ad3aa6127b5a09b31ef
msgid "set the expand status of card"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:543
#: bbe7f66d71f44e1cb3b87fd3cef1347c
msgid "toggle expand status"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:560
#: b096f42129bc49c38dc0c20d6a4f7f80
msgid "Bases: :py:obj:`ExpandSettingCard`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:566
#: e400cdd7616c47c5a3ba2308a4847458
msgid "add widget to group"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:572
#: ../../source/autoapi/qfluentwidgets/components/index.rst:583
#: a644376f93274d3cbd8c88a7ff16fa36 c3c2585ca7814608a254dc2e48865961
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.settings.expand_setting_card.ExpandSettingCard`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:593
#: 30ef678f54514fac9319ee027abb8a0d
msgid "select button according to the value"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:599
#: 6c92dfd725394db9b03ed4cb37fa56c9
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.settings.expand_setting_card.ExpandGroupSettingCard`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:610
#: ../../source/autoapi/qfluentwidgets/components/index.rst:763
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1046
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1126
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1724
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1760
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2224
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2393
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2910
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3101
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3402
#: 70e9f5d3e0b8418c8a8644d69ba8f88c
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:616
#: c7ba5c5e1e2c41f8a9b200976506e57d
msgid "add setting card to group"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:621
#: e6de8b1d6a234894858d0368523de4f8
msgid "add setting cards to group"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:630
#: 6902c8a5930a4c358c2fd59f364a253d
msgid "Bases: :py:obj:`DropDownButtonBase`, :py:obj:`PushButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:643
#: 79992f04f2414362b2add37a0bc2abe8
msgid "Bases: :py:obj:`DropDownButtonBase`, :py:obj:`ToolButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:656
#: ../../source/autoapi/qfluentwidgets/components/index.rst:756
#: 48b3e7b72f22445bb999f3dfa6ae0dbd
msgid "Bases: :py:obj:`PushButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:663
#: ../../source/autoapi/qfluentwidgets/components/index.rst:701
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3158
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3175
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3281
#: b6d3c27fe3614826ba0e185a01f95dcb
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QPushButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:694
#: 2aaac90227b144eca48eca6c82ebfa8e
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QRadioButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:749
#: ../../source/autoapi/qfluentwidgets/components/index.rst:858
#: 6f4de1cb318e4ec2ba6b9bd196bdcc13
msgid "Bases: :py:obj:`ToolButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:773
#: 3f0d92b3e9a4419c95efa2271351f1fd
msgid "set the widget on left side"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:778
#: 5b49a9346798459282cf4c419c1464c6
msgid "set drop dow button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:783
#: f7a8e0ff59d64a3e8e9de4a3379ddbbe
msgid "set the widget pops up when drop down button is clicked"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:788
#: dec96729d87849559bce3c06d612314c
msgid "flyout: QWidget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:788
#: 2f84b514b83144e1a836df24a43baa2b
msgid ""
"the widget pops up when drop down button is clicked. It should contain "
"the `exec` method, whose first parameter type is `QPoint`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:794
#: d0b0db9b4ca24db08eb94addbbd670c1
msgid "show flyout"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:800
#: ../../source/autoapi/qfluentwidgets/components/index.rst:834
#: 3616fdd350074da4b8519c03ebd8e258 43789f087fe848b9b881b2e1b35c93b1
msgid "Bases: :py:obj:`SplitWidgetBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:865
#: 5e2f10f242cf47569c8493a053bb08cd
msgid "Bases: :py:obj:`SplitPushButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:872
#: c4046ff8a51f4fb6808c9ded10c970b2
msgid "Bases: :py:obj:`SplitToolButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:879
#: f203e5c5c3ef4a35904eb108dda29b5f
msgid "Bases: :py:obj:`PrimaryDropDownButtonBase`, :py:obj:`PrimaryPushButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:892
#: e11706b55a3c41e8848830fbd830cad3
msgid "Bases: :py:obj:`PrimaryDropDownButtonBase`, :py:obj:`PrimaryToolButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:905
#: 5351dd6a77fc45e8ba85094c26ea3e20
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QCheckBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:915
#: 9be7bc2a551a403f9132ba31e8a9147a
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QPushButton`, :py:obj:`ComboBoxBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:939
#: 983fa1880e02466daf0c2acb4362cfcf
msgid ""
"Bases: :py:obj:`qfluentwidgets.components.widgets.line_edit.LineEdit`, "
":py:obj:`ComboBoxBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:956
#: 258d9a5a366c4c959398dc5426610941
msgid "Clears the combobox, removing all items."
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:962
#: 41813cfabe144d9e861eee0a5c1995e2
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QLineEdit`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:987
#: 96879ab681cf40c784e9f22108b0dd77
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTextEdit`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:997
#: 807b783d3898422e891a33572278b1a1
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QPlainTextEdit`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1023
#: b6482d374bea4b59aeaef92568f17871
msgid "Bases: :py:obj:`LineEdit`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1037
#: 04b7a45804794e61a3505417cd994968
msgid "emit search signal"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1066
#: 7702a11669c74d0c8f75fbded92492d6
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QLabel`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1082
#: e100d5693f5a458bb4ac3dfbeeec4613
msgid "Bases: :py:obj:`ListBase`, :py:obj:`PyQt5.QtWidgets.QListWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1095
#: df465f5460154c3da3f9ea5188354985
msgid "Bases: :py:obj:`ListBase`, :py:obj:`PyQt5.QtWidgets.QListView`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1102
#: 4d61473150ac47f8a117ff7b4b265802
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.widgets.table_view.TableItemDelegate`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1109
#: f8a6e00fc9c5438c94fda9139ea8f7cf
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QMenu`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1119
#: 1a761f343693424d817ce127076660a4
msgid "Bases: :py:obj:`EditMenu`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1136
#: 3458e581a6e54420a53e3b9133240746
msgid "set the height of menu item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1141
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3416
#: d421f6702e494316ad11a5354c3ad435
msgid "add shadow to dialog"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1155
#: 74492e7c80574f5fb9a8c92d2f751c72
msgid "clear all actions"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1160
#: b62dd264d79c401a90c8df31c99237f9
msgid "set the icon of menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1165
#: d884facb51374f13b3b7148e99b58435
msgid "add action to menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1169
#: 9279144e39d14e0fb6bf806e51e5c0eb
msgid "action: QAction"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1170
#: 3087f7d85ec1416dbb13890035b69a95
msgid "menu action"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1175
#: d1010bd7ae6840a385c397a9afc47438
msgid "inserts action to menu, before the action before"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1180
#: 110d6ba945fb47adac294cc700a421b0
msgid "add actions to menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1184
#: 4ca800028e2a4231bf405459225992e2
msgid "actions: Iterable[QAction]"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1185
#: 678581e2441c4d23851194c6030890e8
msgid "menu actions"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1190
#: 10f9ecf005c24496bbd8b320a11ce8d9
msgid "inserts the actions actions to menu, before the action before"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1195
#: 0d7bdd4283c84ef4b2c858deb0d3600d
msgid "remove action from menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1200
#: 28959f82de664d99a806927f0c5d0af8
msgid "set the default action"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1205
#: 6e23d1596a5748c09992c81f5cf73f44
msgid "add sub menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1209
#: c4b739adf884481db4e5bc2df4cd4ccd
msgid "menu: RoundMenu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1210
#: ad7151004d8e4f66b1bd67de57347853
msgid "sub round menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1215
#: c1d156b949304e27830629cb78e52268
msgid "insert menu before action `before`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1220
#: 75b5ff856215494d9a510959fb00e706
msgid "add seperator to menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1240
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1256
#: 703738d5fe884877958546c5dbc89197
msgid "show menu"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1245
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1261
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3470
#: 36a0657a97b04693a0b379995e0cf6c7
msgid "pos: QPoint"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1245
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1261
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3470
#: ab9d41eda62c4a8f91c9f51cd5743a0e
msgid "pop-up position"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1248
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1264
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3472
#: 6b2b4a98bfac4d2989baec83071034a1
msgid "ani: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1248
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1264
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3473
#: 738a56af444d44848a64a1786d5e3d2f
msgid "Whether to show pop-up animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1250
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1266
#: 7384364cc7d14f4c8e7c90ec605a64d5
msgid "aniType: MenuAnimationType"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1251
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1267
#: 734eb607fd684b0d984e34a5c110d9aa
msgid "menu animation type"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1273
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1936
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2377
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3479
#: 7b54671c376c436cb90b1bb39258c15d
msgid "Bases: :py:obj:`PyQt5.QtCore.QObject`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1287
#: a25962d437794c01a8d36666c216bc48
msgid "register menu animation manager"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1291
#: 6154b869456b4a73bc79ff10c5dadec6
msgid "name: Any"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1292
#: db7173e056f3463dadf9d9e0d0a3dbaf
msgid "the name of manager, it should be unique"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1302
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1422
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1490
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1831
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1961
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2861
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2883
#: 3bcb7895c5e94f689a677985add3d2a5 85ed0e775bb24c22a562dc27017fd29f
msgid "Bases: :py:obj:`enum.Enum`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1334
#: 12ad8044e44d4b4a89df10cd5a69814d
msgid "add widget to info bar"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1339
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2176
#: c24ba72ce1894571bfa823c878e718e3
msgid "set the custom background color"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1343
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2180
#: baf8fba8ea7e45edaaf23aa79e604510
msgid "light, dark: str | Qt.GlobalColor | QColor"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1344
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2181
#: c03ad9d303324e24b2d760da146bf711
msgid "background color in light/dark theme mode"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1382
#: a3b741dfe16d42b7a4a1370970c1c1fd
msgid ""
"Bases: :py:obj:`qfluentwidgets.common.icon.FluentIconBase`, "
":py:obj:`enum.Enum`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1408
#: bd418469120b4b09be53085828c6006d
msgid "get the path of icon"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1415
#: 143f84e3c77548e4a30e65a2e88938f8
msgid "theme: Theme"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1413
#: 0f48ff9b47aa4716821c45263c4c1598
msgid ""
"the theme of icon * `Theme.Light`: black icon * `Theme.DARK`: white icon "
"* `Theme.AUTO`: icon color depends on `config.theme`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1464
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1522
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1545
#: 97a3ab9ebe5a443787ad8125039dab9c
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QScrollArea`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1476
#: 46b6f53cfd6143899d30c5df6b72bf46
msgid "set smooth mode"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1480
#: 12afade90df24549b8cb30d0d256493f
msgid "mode: SmoothMode"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1481
#: 44a2b2e7de27454d84b7ece38dd5bc9a
msgid "smooth scroll mode"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1528
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2363
#: 0b3bd03dabd841b2bf02ae3ea4a3d264
msgid "set scroll animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1533
#: 90d76fbb67284b15bd97485ca14586c4
msgid "orient: Orient"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1533
#: 0a311cc1ae2f4229bc86771447085484
msgid "scroll orientation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1536
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2368
#: 6ef885b815554ba4ac1df97cb8130f1e
msgid "scroll duration"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1538
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2370
#: a460147e86754333b1730cb07f38458a
msgid "easing: QEasingCurve"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1539
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2371
#: 775712e656004a6ebf2b851d6f7ac2ff
msgid "animation type"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1552
#: 7b801fa8a3a340a68345ee07a6cecdec
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QSlider`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1566
#: 78ad2b0a3e994264ab33635ec9a2737e
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QProxyStyle`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1572
#: f3408d29a02740e09cc4874138fb1b6b
msgid "get the rectangular area occupied by the sub control"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1577
#: 2ec14c57a05d48118fb053b45783c698
msgid "draw sub control"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1583
#: 40129fc2cb1644d296f9d718fac97c8e
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QSpinBox`, :py:obj:`Ui_SpinBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1593
#: c5fcd204f5ab48f4a3b35f33c079294b
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QDoubleSpinBox`, :py:obj:`Ui_SpinBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1603
#: cb5f911e8ab546899a2c83beafc6b24d
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QDateEdit`, :py:obj:`Ui_SpinBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1613
#: 83bdd3bf51964dc3824779836b6d3315
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QDateTimeEdit`, :py:obj:`Ui_SpinBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1623
#: b7585b076ebc447491b27feec65c9853
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTimeEdit`, :py:obj:`Ui_SpinBox`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1633
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1708
#: 752355fc1d0247c6a3030fb8ca810c1b
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QStackedWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1647
#: de6d2a4b7c7f4660a183bde13eacda91
msgid "add widget to window"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1652
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1690
#: da6561f00f8643ffaa7e0da246c92280
msgid "widget:"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1652
#: 503b039244ee409fb17f95030a7ceac1
msgid "widget to be added"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1655
#: 09cd20656f374089ab1fd57b6b42b19c
msgid "deltaX: int"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1655
#: 8e9410b16892470a8fbaf78bde5bf7da
msgid "the x-axis offset from the beginning to the end of animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1657
#: f6b003186c5e45538c7d5863cef0a262
msgid "deltaY: int"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1658
#: e085813fbb214e009400c702b6925a58
msgid "the y-axis offset from the beginning to the end of animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1663
#: d983f3006a0b4b4985cc7034728495ac
msgid "set current window to display"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1668
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2716
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2750
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2788
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2981
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3015
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3053
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3128
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3362
#: 4c17b7a38a6045988d5d56a1cabc12a1
msgid "index: int"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1668
#: a08ccb2029d0409aaad6f18d2973df4d
msgid "the index of widget to display"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1671
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1693
#: fc98e79a64214e409549d0709107a209
msgid "isNeedPopOut: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1671
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1693
#: 6e359be96c59456cae2c6a97f3551270
msgid "need pop up animation or not"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1674
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1696
#: 74bb7bb4d8634e36a5d8b16aaa1d0dcc
msgid "showNextWidgetDirectly: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1674
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1696
#: 716705e7fd8f4430af17339d2f6e3315
msgid "whether to show next widget directly when animation started"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1677
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1699
#: 8614e85530af4c59b7ef221327cf0df2
msgid "animation duration"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1679
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1701
#: b7d3da2cb43148e1b8e50c613aa9a018
msgid "easingCurve: QEasingCurve"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1680
#: ../../source/autoapi/qfluentwidgets/components/index.rst:1702
#: ********************************
msgid "the interpolation mode of animation"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1685
#: 4416b2bb45da481fab09ce391b1ba7bd
msgid "set currect widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1690
#: 9fda75cbbfb6421ba408cc775d4546be
msgid "the widget to be displayed"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1734
#: 6d8e076e4db64e00bd82859f72878690
msgid "set the title of tooltip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1739
#: 6b07e46da9454e5798aae928df2ce23b
msgid "set the content of tooltip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1744
#: c26d6553790845f9a6f35e2ff2457ad1
msgid "set the state of tooltip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1749
#: cc7c2de8d7084f1aa7d28b1096775831
msgid "get suitable position in main window"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1754
#: e694bdea2eda4b72b6f9c45791be5982
msgid "paint state tooltip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1796
#: d4295ede9ce344c3a091e72aa3028cc0
msgid "set checked state"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1801
#: ad768d15b3264505ab20e3a6296d2397
msgid "toggle checked state"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1848
#: fb2e66a240164a4b9707500489aa05e2
msgid "Bases: :py:obj:`TableBase`, :py:obj:`PyQt5.QtWidgets.QTableView`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1855
#: adab623c30604c9d99cd3cb80e24a23f
msgid "Bases: :py:obj:`TableBase`, :py:obj:`PyQt5.QtWidgets.QTableWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1868
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2022
#: 2264480c241246bca2741cc2f19b5018
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QStyledItemDelegate`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1906
#: 92301313a6ef4020adc1fd22c70c44d5
msgid "set text on tooltip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1914
#: 82babd2acd56467085e8638e3097649e
msgid "set tooltip duration in milliseconds"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1919
#: d1d810ae53354bba8f11eee8c2c62db6
msgid ""
"display duration in milliseconds, if `duration <= 0`, tooltip won't "
"disappear automatically"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1930
#: 66d258759a854bf7b103b7788087a1cd
msgid "adjust the position of tooltip relative to widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1945
#: 4a00595e7abb44aeb9d224dd720dd3da
msgid "hide tool tip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1950
#: c0dea8993d2c4b88baca3347d77109a6
msgid "show tool tip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:1955
#: d2d4984331b74eb4a661ae517ba8cb01
msgid "set the delay of tool tip"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2008
#: fa68d32903c64ed7ae1a4320ea0df1d1
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTreeWidget`, :py:obj:`TreeViewBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2015
#: 4e559bdfc0cd45dfa7c3f551e5ba9b54
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTreeView`, :py:obj:`TreeViewBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2035
#: 589989eec90d41048362bdf9f1726e9c
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QListWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2045
#: a77dfcf7703c45d082c96cf1bcda8cea
msgid "set items in the list"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2050
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3426
#: 587a14b5f8814b9f9cd3180d2fa3a6ac
msgid "items: Iterable[Any]"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2050
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3426
#: 803b36d4af7d4874a05ab9164862fe75
msgid "the items to be added"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2053
#: c577870c6a1c4953862365f939f65ce9
msgid "itemSize: QSize"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2053
#: 70910f78e66348528dbcb110c3c832e5
msgid "the size of item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2055
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3301
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3373
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3431
#: 07cc594bc2cc4bcfa4081c87937a959d
msgid "align: Qt.AlignmentFlag"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2056
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3432
#: 0aa11032f432493dbb153836a9ecb953
msgid "the text alignment of item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2061
#: b771c8ef6b9a47258008b8b556b12f6d
msgid "set the selected item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2066
#: c967c77e6c6743bca0dfa494b195ad92
msgid "scroll to item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2074
#: b7278836568d4aa9ac48af5d75c4acfc
msgid "scroll down an item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2079
#: 7bab9ea8f4cf42939fb225f4fa45b11f
msgid "scroll up an item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2106
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2152
#: 6e5090d04449490aab19fcedd3ddbc45 a4dc9b5dd6ec43df9ac1585f3014099c
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QProgressBar`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2214
#: 12de74c031e54924a2cef3fd006f6602
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.widgets.progress_bar.ProgressBar`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2295
#: 9f2d4cc2f1894d0bad1efc966f378c27
msgid "expand scroll bar"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2300
#: 42aa1c8c19e74c04b34f37515956ea91
msgid "collapse scroll bar"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2326
#: 2e44cb9810814e4783440ac0adee7b2e
msgid "whether to force the scrollbar to be hidden"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2335
#: b1ca1b25493b47f1a579d2b73fe4e352
msgid "Bases: :py:obj:`ScrollBar`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2344
#: c1bae3adff004a9f8ab8e28d5932dd94
msgid "scroll the specified distance"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2349
#: 5efe23d029d84361bcf8110b29a5acb4
msgid "scroll to the specified position"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2420
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2464
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2479
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2569
#: ec1d73fb27ed496991b575682f1bcd65
msgid "set whether the widget is compacted"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2425
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2556
#: fce2a48378664ac8892791718a30e56b
msgid "set whether the button is selected"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2429
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2560
#: 0160524b995c4b7da51606d74d5c0bed
msgid "isSelected: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2430
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2561
#: 678cfc0ecbd44cff9c9a53363bd178b0
msgid "whether the button is selected"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2436
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2458
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2575
#: 4ed3cf720bcd457d8c83dc35ba864b2f b580f60fcdb94f52abca70e694a6f19e
msgid "Bases: :py:obj:`NavigationWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2473
#: edcda691ea674933926a1be7321ddebe
msgid "Bases: :py:obj:`NavigationPushButton`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2485
#: b793be8cb14d454098a599d5addc37c7
msgid "Bases: :py:obj:`NavigationTreeWidgetBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2491
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2582
#: d6666bcad064489e89c1c160a4802a56
msgid "add child"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2495
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2520
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2530
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2586
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2597
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2608
#: 6265d15807a646c39f39bb6166470df0
msgid "child: NavigationTreeWidgetBase"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2496
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2521
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2531
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2587
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2598
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2609
#: 85b31ef07f92450f868b36c461cd57c9
msgid "child item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2516
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2593
#: 34c49e3d717149368200602368ca3e09
msgid "insert child"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2526
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2604
#: 1c6393b3fccd46a5ac14be4f8a6483c1
msgid "remove child"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2536
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2636
#: bb0e4f33c5ac48b3a9d391ee779ca516
msgid "return child items"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2541
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2625
#: 1dedba58292742b1a41518b79bd70b61
msgid "set the expanded status"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2546
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2614
#: 74ff375d25aa483faa1fa0e3018f79c8
msgid "is root node"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2551
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2619
#: 7a5df48850364e8f89c48896b318b4f0
msgid "is leaf node"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2629
#: ********************************
msgid "isExpanded: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2630
#: 0bc3f3e0ab71460e9cfa6e6b8dd17ae0
msgid "whether to expand node"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2655
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2920
#: c22eb82a076149bcab67f5820effaf5e
msgid "add navigation item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2660
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2691
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2719
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2753
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2800
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2840
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3112
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3131
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3146
#: d2cf128557c5426d9e716410b62e0e70
msgid "routeKey: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2660
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2691
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2719
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2753
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2801
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2841
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2925
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2956
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2984
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3018
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3066
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3076
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3112
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3131
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3147
#: 2b0fbe3526b64b17b5a5bb013eafef42
msgid "the unique name of item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2663
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2722
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2928
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2987
#: dd77150ccd4a4afe8da5dffe2076579d
msgid "icon: str | QIcon | FluentIconBase"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2663
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2722
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2928
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2987
#: 7b1da7ae75ce4c199b9bfecf847b18ae
msgid "the icon of navigation item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2666
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2725
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2931
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2990
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3115
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3134
#: 059b75bf43734ae1b5c1608bce33030f
msgid "text: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2666
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2725
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2931
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2990
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3115
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3134
#: a6f87a3d59b24f8e9bf02647aec1dec6
msgid "the text of navigation item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2669
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2697
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2728
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2759
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2934
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2962
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2993
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3024
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3117
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3136
#: 0be44c93a2ad41b6a9fe206494c1a06d
msgid "onClick: callable"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2669
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2697
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2728
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2759
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2934
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2962
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2993
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3024
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3118
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3137
#: af37de81e180489b8b269d56be2f7d60
msgid "the slot connected to item clicked signal"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2672
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2700
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2731
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2762
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2940
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2965
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2999
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3027
#: f7dfa563830b4d45bdebf11d7c2fe68b
msgid "position: NavigationItemPosition"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2672
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2700
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2731
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2762
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2940
#: 8dc8a43b6f79401b9f90b45649033d00
msgid "where the button is added"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2675
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2734
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2937
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2996
#: 87867e521c80493bb31d842b61ca0275
msgid "selectable: bool"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2675
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2734
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2937
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2996
#: 836ae6c6b6fb4a588ef486cfc4d09332
msgid "whether the item is selectable"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2678
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2703
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2737
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2765
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2943
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2968
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3002
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3030
#: cb3c4b4c6c294be7b6ca73d99ab59f40
msgid "tooltip: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2678
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2737
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2943
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3002
#: 0ecf2f6d240c429485743340fd617056
msgid "the tooltip of item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2680
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2705
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2739
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2767
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2945
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2970
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3004
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3032
#: 54a5bed5bb3a4221830d5d5fa154aa5f
msgid "parentRouteKey: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2681
#: 7a24a5fee78a420db1adb9b1c022db46
msgid ""
"the route key of parent item, the parent widget should be "
"`NavigationTreeWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2686
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2951
#: 3746ad5e68cf4750be27117373d3b377
msgid "add custom widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2694
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2756
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2959
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3021
#: 1188dd4225754767a7b328d78c3030e7
msgid "widget: NavigationWidget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2694
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2756
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2959
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3021
#: aadbdddfc18e4ada818cbab30a9bec7a
msgid "the custom widget to be added"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2703
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2765
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2968
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3030
#: e02fd11b68e44b1dafbc4c59916c6fbf
msgid "the tooltip of widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2706
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2740
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2768
#: b606b9de3aa54e69babfbbc14c4feb16
msgid ""
"the route key of parent item, the parent item should be "
"`NavigationTreeWidget`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2711
#: 98e6f39767e04f91b91bf0421a91ddb6
msgid "insert navigation tree item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2716
#: b68eb6824e3f4937a7be5205071db077
msgid "the insert position of parent widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2745
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3010
#: b30874bfd68b4c74af0ed68ad3ed5927
msgid "insert custom widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2750
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2788
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2981
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3015
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3053
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3128
#: 7caa54d9a7f34903b3ccca83e237c041
msgid "insert position"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2773
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2783
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3038
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3048
#: aa3853515aec450e84d3d297bb7b60e4
msgid "add separator"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2777
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2790
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3042
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3055
#: 26931ad6d58a4adabcdce4012a9205fe
msgid "position: NavigationPostion"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2778
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2791
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3043
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3056
#: b9d53ca7104b4921a88657ba8c7d96b1
msgid "where to add the separator"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2796
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3061
#: 50c21d5556de42e4980e60c78216c223
msgid "remove widget"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2806
#: eb46d82d18b6473fa5e739f5e4da1a44
msgid "set whether the menu button is visible"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2811
#: 416e2e9239d846b68a7842307a87780d
msgid "set whether the return button is visible"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2816
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3086
#: 0de75933d19b4e42936bcf442a66d5ca
msgid "set the maximum width"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2821
#: 24232436f02b4018bc04db138ad8434c
msgid "expand navigation panel"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2826
#: cda8fb36eec24c53ac3b62c2bc6bbfdb
msgid "collapse navigation panel"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2831
#: 45fad0c29abf41428ab3381aa1073ded
msgid "toggle navigation panel"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2836
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3071
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3142
#: e568a84cfa7f434c9e3ad780b5f52c74
msgid "set current selected item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2855
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3081
#: 6c14304b0ff54556af13b6ecb77f120d
msgid "set the routing key to use when the navigation history is empty"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2925
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2956
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2984
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3018
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3065
#: 58dcc2ae1f174fd58eca02c22bf0e8f1
msgid "routKey: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2946
#: ../../source/autoapi/qfluentwidgets/components/index.rst:2971
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3005
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3033
#: dba40503e1ea422db058fc52d4516b94
msgid ""
"the route key of parent item, the parent item should be "
"`NavigationTreeWidgetBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2965
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3027
#: 6a571b3906904d91905e614ad1737adc
msgid "where the widget is added"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2976
#: 9f3d50ca0ff6479ba0bb931d87ebe54b
msgid "insert navigation item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:2999
#: 16b02ffb9fd84b83a31caab47a73bb6a
msgid "where the item is added"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3075
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3292
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3365
#: 716ad66d13914062b0a9196f57da7b88
msgid "name: str"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3107
#: 4b1702393e5d46b7aaf78956e0da74df
msgid "add item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3123
#: 8b3236f98e914812b4cdb28df47b1d13
msgid "insert item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3152
#: 0bd14aacf5914336b8d3f801a9cf73e4
msgid "set the pixel font size of items"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3185
#: b771c8ef6b9a47258008b8b556b12f6d
msgid "set the selected date"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3197
#: d3ca93db13364bf2a17edd793a9ff8bf
msgid ""
"Bases: "
":py:obj:`qfluentwidgets.components.date_time.picker_base.PickerBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3208
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3268
#: 7cb61e522750498a8315c3d545e95951
msgid "set current date"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3232
#: a6be5fceb32f42a18a48bf4bd34bea5e
msgid "Bases: :py:obj:`DatePickerBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3248
#: f398c70e624b4268a0bdf8b1f53b2dcb
msgid "set the format of date"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3252
#: 0563b6a04136466cbef5ee7e2125d135
msgid "format: int"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3253
#: 84dcf24485474ff59f3f20516d623914
msgid ""
"the format of date, could be `DatePicker.MM_DD_YYYY` or "
"`DatePicker.YYYY_MM_DD`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3258
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3396
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3517
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3544
#: e7afdade324b49179fa703f295e397b9
msgid "initial value of panel"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3263
#: c1deb81cb5ec43f1aee3ddcf8b721070
msgid "set whether the month column is tight"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3274
#: 3b556de4d91847a1bdd499c680095de2
msgid "Bases: :py:obj:`DatePicker`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3287
#: 6e64c6fea78a4c669c9aa8af9280590b
msgid "add column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3292
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3365
#: 669488273c2c4028bf8833630e5e08e9
msgid "the name of column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3295
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3368
#: aab1c23bf52548c896cb8657945c6345
msgid "items: Iterable"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3295
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3368
#: cdb05f87996c4ee88341de0eca177ff9
msgid "the items of column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3298
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3371
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3429
#: 430415b25b8746b58debdf3b717e877c
msgid "width: int"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3298
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3371
#: e6881d799ef044908cdc21b4786e406e
msgid "the width of column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3301
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3374
#: c67ded3be8ed4050a6da7f111a431e68
msgid "the text alignment of button"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3303
#: 8d804610ac3d4ecdbb71a3c4210fb914
msgid "formatter: PickerColumnFormatter"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3304
#: b2ae248161f64a1ebb03a013556836fc
msgid "the formatter of column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3309
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3324
#: 092a2b3e6ca54edab718b2c182b8ed07
msgid "set the text alignment of specified column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3314
#: 2afd4f1708074c009216288dd0e9d4b7
msgid "set the width of specified column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3319
#: 905b5df07af04d088dce85a5907b8573
msgid "make the specified column to be tight"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3347
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3485
#: c8ff6c13664c472a9fd1c7d8f52abc2c
msgid "convert original value to formatted value"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3352
#: 594775624d984055bc7d057a92919832
msgid "convert formatted value to origin value"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3357
#: a58d87ff5fa446f7a2e2e9de9fb31edc
msgid "set column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3362
#: 721c22d653d649c6b7e37f0baa5bfca6
msgid "the index of column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3379
#: 026b20e535b34536b6b4e2b0d97e8d63
msgid "clear columns"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3421
#: dd4f2b03af0347e4a76dd186e4d42a85
msgid "add one column to view"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3429
#: 82292425616a464b8d03bb15e0b3b1f5
msgid "the width of item"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3440
#: 728fa22deb2b4466918343f5be895b05
msgid "return the value of columns"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3445
#: b489d199f1ff4439934434a9454afe39
msgid "set the value of columns"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3450
#: 4af5c1a613cf454c86e82f533c0e4767
msgid "return the value of specified column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3455
#: 3b247007d91147da817895400f992396
msgid "set the value of specified column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3460
#: 08fdf8693e144437b921fcf2c1105f1a
msgid "return the list widget of specified column"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3465
#: 545e16c8435b46768bdad4a0fe11c5b2
msgid "show panel"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3490
#: f4cd30c5c60342af93b51094dc8d2171
msgid "convert formatted value to original value"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3496
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3523
#: d4f7f8ff9e8b41fd9218a0571c965ca3
msgid "Bases: :py:obj:`TimePickerBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3502
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3534
#: ef47c3b2d47d4d28bfe2023784ef9469
msgid "set current time"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3506
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3538
#: 8fc774db34bc4da885923e12151d3f83
msgid "time: QTime"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3507
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3539
#: 61b9ea626f4644b79e32c5f1b8a9dc2a
msgid "current time"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/index.rst:3512
#: ../../source/autoapi/qfluentwidgets/components/index.rst:3529
#: 5da01799b4dc477e9becdc5673d5e29b
msgid "set the visibility of seconds column"
msgstr ""

#~ msgid "A scroll area which can scroll smoothly"
#~ msgstr ""

#~ msgid "Bases: :py:obj:`PyQt5.QtWidgets.QScrollBar`"
#~ msgstr ""

#~ msgid "global position of widget"
#~ msgstr ""

#~ msgid "size: QSize"
#~ msgstr ""

#~ msgid "size of widget"
#~ msgstr ""

#~ msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTreeWidget`"
#~ msgstr ""

#~ msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTreeView`"
#~ msgstr ""

#~ msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTableView`, :py:obj:`TableBase`"
#~ msgstr ""

#~ msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTableWidget`, :py:obj:`TableBase`"
#~ msgstr ""

