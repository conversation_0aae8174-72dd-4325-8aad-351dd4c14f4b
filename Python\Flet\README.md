# PDF文本替换编辑工具

基于flet框架开发的GUI应用程序，支持PDF文件的文本查找和替换功能。

## 功能特性

- 📁 **文件导入**: 支持选择和导入PDF文件
- 👀 **内容预览**: 实时显示PDF文本内容
- 🔄 **文本替换**: 支持查找和替换指定文本
- 💾 **导出功能**: 将修改后的内容保存为新的PDF文件
- 🎨 **友好界面**: 清晰的GUI布局，操作简单直观
- ⚠️ **错误处理**: 完善的异常处理和状态提示

## 系统要求

- Python 3.7+
- Windows/macOS/Linux

## 安装和运行

### 方法一：自动安装（推荐）

1. 运行安装脚本：
```bash
python install_and_run.py
```

脚本会自动检查并安装所需依赖，然后启动应用程序。

### 方法二：手动安装

1. 安装依赖库：
```bash
pip install -r requirements.txt
```

或者单独安装：
```bash
pip install flet PyPDF2 pdfplumber reportlab
```

2. 运行应用程序：
```bash
python pdf_text_editor.py
```

## 使用说明

### 1. 导入PDF文件
- 点击"选择PDF文件"按钮
- 在文件对话框中选择要编辑的PDF文件
- 文件路径将显示在界面上，PDF内容会自动提取并显示在预览区域

### 2. 文本替换
- 在"原文"输入框中输入需要替换的文字
- 在"变更为"输入框中输入替换后的新文字
- 点击"执行替换"按钮进行替换
- 预览区域会实时更新显示修改后的内容

### 3. 保存PDF
- 完成文本替换后，点击"保存PDF"按钮
- 选择保存位置和文件名
- 系统会生成包含修改内容的新PDF文件

## 界面布局

```
┌─────────────────────────────────────────────────────────┐
│                PDF文本替换编辑工具                        │
├─────────────────────────────────────────────────────────┤
│ 文件导入                                                │
│ [选择PDF文件] 已选择: document.pdf                       │
├─────────────────────────────────────────────────────────┤
│ 内容预览              │ 文本替换                         │
│ ┌─────────────────┐   │ ┌─────────────────────────────┐ │
│ │                 │   │ │ 原文:                       │ │
│ │   PDF文本内容   │   │ │ [输入要替换的文字]          │ │
│ │   预览区域      │   │ └─────────────────────────────┘ │
│ │                 │   │ ┌─────────────────────────────┐ │
│ │                 │   │ │ 变更为:                     │ │
│ │                 │   │ │ [输入新的文字]              │ │
│ └─────────────────┘   │ └─────────────────────────────┘ │
│                       │ [执行替换] [保存PDF]            │
├─────────────────────────────────────────────────────────┤
│ 状态: 就绪                                              │
└─────────────────────────────────────────────────────────┘
```

## 技术实现

### 核心库
- **flet**: GUI框架，提供现代化的用户界面
- **pdfplumber**: PDF文本提取，支持复杂布局的PDF文件
- **PyPDF2**: PDF文件处理的辅助库
- **reportlab**: PDF生成，支持中文字体

### 主要功能模块
- `PDFTextEditor`: 核心PDF处理类
- `extract_text_from_pdf()`: PDF文本提取
- `replace_text()`: 文本替换逻辑
- `create_pdf_from_text()`: PDF生成功能

## 注意事项

1. **字体支持**: 程序会自动检测系统中的中文字体，如果没有找到合适的中文字体，将使用默认字体
2. **PDF格式**: 支持大多数标准PDF格式，对于图片型PDF或复杂布局的PDF，文本提取效果可能有限
3. **文件大小**: 建议处理中小型PDF文件（<50MB），大文件可能需要较长处理时间
4. **备份建议**: 建议在编辑重要文档前先备份原文件

## 故障排除

### 常见问题

1. **依赖安装失败**
   - 确保Python版本>=3.7
   - 尝试升级pip: `python -m pip install --upgrade pip`
   - 使用国内镜像: `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/`

2. **PDF文本提取失败**
   - 检查PDF文件是否损坏
   - 确认PDF包含可提取的文本（非纯图片PDF）

3. **中文显示问题**
   - 确保系统安装了中文字体
   - Windows用户通常不会遇到此问题

4. **PDF生成失败**
   - 检查输出路径是否有写入权限
   - 确认磁盘空间充足

## 开发信息

- 开发语言: Python
- GUI框架: Flet
- 许可证: MIT
- 版本: 1.0.0

## 更新日志

### v1.0.0 (2025-06-25)
- 初始版本发布
- 支持PDF文本提取和替换
- 实现GUI界面
- 添加PDF导出功能
