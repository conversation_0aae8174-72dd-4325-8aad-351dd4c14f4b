// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const searchInput = document.getElementById('search-input');
    const searchButton = document.getElementById('search-button');
    const bookFilter = document.getElementById('book-filter');
    const colorFilter = document.getElementById('color-filter');
    const loadingElement = document.getElementById('loading');
    const errorMessageElement = document.getElementById('error-message');
    const notesListElement = document.getElementById('notes-list');

    // 存储所有笔记数据
    let allNotes = [];
    // 存储书籍信息
    let bookInfo = {};

    // 初始化加载数据
    fetchNotes();

    // 搜索按钮点击事件
    searchButton.addEventListener('click', function() {
        filterNotes();
    });

    // 搜索输入框回车事件
    searchInput.addEventListener('keyup', function(event) {
        if (event.key === 'Enter') {
            filterNotes();
        }
    });

    // 书籍筛选变化事件
    bookFilter.addEventListener('change', function() {
        filterNotes();
    });

    // 颜色筛选变化事件
    colorFilter.addEventListener('change', function() {
        filterNotes();
    });

    // 获取微信读书笔记数据
    function fetchNotes() {
        showLoading();

        // 从后台脚本获取笔记数据
        chrome.runtime.sendMessage({ action: "getBookmarks" }, function(response) {
            if (response.success) {
                processBookmarks(response.data);
            } else {
                showError(response.message || "获取笔记失败，请确保您已登录微信读书");
            }
        });
    }

    // 处理笔记数据
    function processBookmarks(data) {
        try {
            if (data && data.updated) {
                allNotes = data.updated || [];
                
                // 获取书籍信息
                chrome.runtime.sendMessage({ action: "getBookInfo", bookIds: [...new Set(allNotes.map(note => note.bookId))] }, function(response) {
                    if (response.success) {
                        bookInfo = response.data || {};
                        
                        // 处理笔记数据，添加书籍标题
                        allNotes.forEach(note => {
                            if (bookInfo[note.bookId]) {
                                note.bookTitle = bookInfo[note.bookId].title;
                                note.author = bookInfo[note.bookId].author;
                            } else {
                                note.bookTitle = "未知书籍";
                                note.author = "未知作者";
                            }
                            
                            // 章节信息
                            note.chapterTitle = bookInfo[note.bookId] && bookInfo[note.bookId].chapters && 
                                               bookInfo[note.bookId].chapters[note.chapterUid] ? 
                                               bookInfo[note.bookId].chapters[note.chapterUid] : 
                                               `第${note.chapterUid}章`;
                        });
                        
                        // 更新书籍筛选器
                        updateBookFilter();
                        
                        // 显示笔记
                        renderNotes(allNotes);
                        hideLoading();
                    } else {
                        showError("获取书籍信息失败: " + (response.message || "未知错误"));
                    }
                });
            } else {
                showError("未找到笔记数据，请确保您已登录微信读书");
            }
        } catch (error) {
            showError("处理笔记数据时出错: " + error.message);
        }
    }

    // 更新书籍筛选器
    function updateBookFilter() {
        // 清空现有选项（保留"所有书籍"选项）
        while (bookFilter.options.length > 1) {
            bookFilter.remove(1);
        }
        
        // 获取唯一的书籍ID列表
        const uniqueBooks = [...new Set(allNotes.map(note => note.bookId))];
        
        // 为每本书添加选项
        uniqueBooks.forEach(bookId => {
            const book = bookInfo[bookId] || { title: "未知书籍" };
            const option = document.createElement('option');
            option.value = bookId;
            option.textContent = book.title;
            bookFilter.appendChild(option);
        });
    }

    // 筛选笔记
    function filterNotes() {
        const searchText = searchInput.value.toLowerCase();
        const selectedBookId = bookFilter.value;
        const selectedColorStyle = colorFilter.value;
        
        let filteredNotes = allNotes;
        
        // 按搜索文本筛选
        if (searchText) {
            filteredNotes = filteredNotes.filter(note => 
                note.markText.toLowerCase().includes(searchText)
            );
        }
        
        // 按书籍筛选
        if (selectedBookId !== 'all') {
            filteredNotes = filteredNotes.filter(note => 
                note.bookId === selectedBookId
            );
        }
        
        // 按颜色筛选
        if (selectedColorStyle !== 'all') {
            filteredNotes = filteredNotes.filter(note => 
                note.colorStyle === parseInt(selectedColorStyle)
            );
        }
        
        // 渲染筛选后的笔记
        renderNotes(filteredNotes);
    }

    // 渲染笔记列表
    function renderNotes(notes) {
        // 清空笔记列表
        notesListElement.innerHTML = '';
        
        if (notes.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'empty-message';
            emptyMessage.textContent = '没有找到匹配的笔记';
            notesListElement.appendChild(emptyMessage);
        } else {
            // 按创建时间排序（最新的在前面）
            notes.sort((a, b) => b.createTime - a.createTime);
            
            // 创建笔记卡片
            notes.forEach(note => {
                const noteCard = createNoteCard(note);
                notesListElement.appendChild(noteCard);
            });
        }
        
        // 显示笔记列表
        notesListElement.classList.remove('hidden');
    }

    // 创建笔记卡片
    function createNoteCard(note) {
        const noteCard = document.createElement('div');
        noteCard.className = `note-card color-${note.colorStyle}`;
        
        const noteHeader = document.createElement('div');
        noteHeader.className = 'note-header';
        
        const bookTitle = document.createElement('div');
        bookTitle.className = 'note-book-title';
        bookTitle.textContent = note.bookTitle;
        
        const noteTime = document.createElement('div');
        noteTime.className = 'note-time';
        noteTime.textContent = formatDate(note.createTime);
        
        noteHeader.appendChild(bookTitle);
        noteHeader.appendChild(noteTime);
        
        const noteContent = document.createElement('div');
        noteContent.className = 'note-content';
        
        const noteText = document.createElement('div');
        noteText.className = 'note-text';
        noteText.textContent = note.markText;
        
        const noteChapter = document.createElement('div');
        noteChapter.className = 'note-chapter';
        noteChapter.textContent = note.chapterTitle;
        
        noteContent.appendChild(noteText);
        noteContent.appendChild(noteChapter);
        
        noteCard.appendChild(noteHeader);
        noteCard.appendChild(noteContent);
        
        // 添加点击事件，打开微信读书对应的页面
        noteCard.addEventListener('click', function() {
            if (note.bookId) {
                chrome.runtime.sendMessage({ 
                    action: "openBookPage", 
                    bookId: note.bookId,
                    chapterUid: note.chapterUid,
                    range: note.range
                });
            }
        });
        
        return noteCard;
    }

    // 格式化日期
    function formatDate(timestamp) {
        const date = new Date(timestamp * 1000);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 显示加载中
    function showLoading() {
        loadingElement.style.display = 'flex';
        errorMessageElement.classList.add('hidden');
        notesListElement.classList.add('hidden');
    }

    // 隐藏加载中
    function hideLoading() {
        loadingElement.style.display = 'none';
    }

    // 显示错误信息
    function showError(message) {
        loadingElement.style.display = 'none';
        errorMessageElement.innerHTML = message;
        errorMessageElement.classList.remove('hidden');
    }
});
