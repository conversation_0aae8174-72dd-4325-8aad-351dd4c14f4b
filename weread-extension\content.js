// 内容脚本，用于与微信读书网页交互
console.log("微信读书笔记助手已加载");

// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === "getLoginStatus") {
        // 检查是否已登录微信读书
        const isLoggedIn = checkLoginStatus();
        sendResponse({ isLoggedIn: isLoggedIn });
    }
});

// 检查登录状态
function checkLoginStatus() {
    // 检查是否存在用户信息元素
    const userElement = document.querySelector('.navBar_userInfo');
    return !!userElement;
}
