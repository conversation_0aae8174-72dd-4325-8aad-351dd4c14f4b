// Bitwarden 数据筛查工具 JavaScript
class BitwardenAnalyzer {
    constructor() {
        this.data = [];
        this.currentIndex = 0;
        this.isRunning = false;
        this.isPaused = false;
        this.results = [];
        this.duplicates = [];
        this.stats = {
            total: 0,
            tested: 0,
            success: 0,
            failed: 0,
            skipped: 0,
            invalid: 0,
            internal: 0,
            duplicates: 0
        };
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');

        // 文件上传事件
        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        fileInput.addEventListener('change', this.handleFileSelect.bind(this));
    }

    handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }

    handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            this.processFile(file);
        }
    }

    async processFile(file) {
        try {
            const text = await this.readFile(file);
            this.parseData(text, file.name);
            this.updateStats();
            this.enableStartButton();
            this.showMessage('文件上传成功！', 'success');
        } catch (error) {
            this.showMessage('文件读取失败: ' + error.message, 'error');
        }
    }

    readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = e => reject(new Error('文件读取失败'));
            reader.readAsText(file);
        });
    }

    parseData(text, filename) {
        this.data = [];
        
        try {
            if (filename.endsWith('.json')) {
                this.parseJsonData(text);
            } else if (filename.endsWith('.csv')) {
                this.parseCsvData(text);
            } else {
                this.parseTextData(text);
            }
        } catch (error) {
            throw new Error('数据解析失败: ' + error.message);
        }

        // 检测重复项
        this.detectDuplicates();
    }

    parseJsonData(text) {
        const jsonData = JSON.parse(text);

        // 尝试解析 Bitwarden 导出格式
        if (jsonData.items && Array.isArray(jsonData.items)) {
            jsonData.items.forEach(item => {
                if (item.login && item.login.uris && Array.isArray(item.login.uris)) {
                    item.login.uris.forEach(uri => {
                        const url = uri.uri || uri.url || '';
                        this.data.push({
                            name: item.name || '未命名',
                            url: url,
                            username: item.login.username || '',
                            password: item.login.password || '',
                            domain: this.extractDomain(url)
                        });
                    });
                } else if (item.login) {
                    // 如果没有uris数组，但有login信息，尝试其他字段
                    const url = item.login.url || item.login.uri || item.url || item.uri || '';
                    this.data.push({
                        name: item.name || '未命名',
                        url: url,
                        username: item.login.username || '',
                        password: item.login.password || '',
                        domain: this.extractDomain(url)
                    });
                } else {
                    // 没有login信息，直接从item获取
                    const url = item.url || item.uri || '';
                    this.data.push({
                        name: item.name || '未命名',
                        url: url,
                        username: item.username || '',
                        password: item.password || '',
                        domain: this.extractDomain(url)
                    });
                }
            });
        } else if (Array.isArray(jsonData)) {
            // 简单数组格式
            jsonData.forEach(item => {
                const url = item.url || item.uri || item.website || '';
                this.data.push({
                    name: item.name || item.title || '未命名',
                    url: url,
                    username: item.username || item.user || '',
                    password: item.password || '',
                    domain: this.extractDomain(url)
                });
            });
        }

        // 调试信息
        console.log('解析后的数据:', this.data);
        console.log('数据总数:', this.data.length);
    }

    parseCsvData(text) {
        const lines = text.split('\n').filter(line => line.trim());
        const headers = lines[0].split('\t').map(h => h.trim().toLowerCase()); // 支持制表符分隔

        // 如果不是制表符分隔，尝试逗号分隔
        if (headers.length === 1) {
            headers[0] = lines[0];
            const commaHeaders = lines[0].split(',').map(h => h.trim().toLowerCase());
            if (commaHeaders.length > 1) {
                headers.splice(0, 1, ...commaHeaders);
            }
        }

        console.log('CSV Headers:', headers);

        for (let i = 1; i < lines.length; i++) {
            let values;
            // 根据header的分隔符来分割数据
            if (headers.length > 1 && lines[0].includes('\t')) {
                values = lines[i].split('\t');
            } else {
                values = lines[i].split(',');
            }

            const item = {};

            headers.forEach((header, index) => {
                item[header] = values[index] ? values[index].trim() : '';
            });

            // 支持多种字段名格式
            const url = item.url || item.uri || item.website || item.login_uri || item['login uri'] || '';
            const name = item.name || item.title || item.login_name || '未命名';
            const username = item.username || item.user || item.login || item.login_username || item['login username'] || '';
            const password = item.password || item.login_password || item['login password'] || '';

            console.log('解析项目:', { name, url, username });

            this.data.push({
                name: name,
                url: url,
                username: username,
                password: password,
                domain: this.extractDomain(url)
            });
        }
    }

    parseTextData(text) {
        const lines = text.split('\n').filter(line => line.trim());

        lines.forEach(line => {
            line = line.trim();
            if (line) {
                // 先尝试匹配完整的URL
                const urlMatch = line.match(/https?:\/\/[^\s]+/);
                if (urlMatch) {
                    this.data.push({
                        name: '从文本提取',
                        url: urlMatch[0],
                        username: '',
                        password: '',
                        domain: this.extractDomain(urlMatch[0])
                    });
                } else {
                    // 如果不是完整URL，检查是否是域名或IP
                    const domainMatch = line.match(/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(:\d+)?$/) ||
                                      line.match(/^(\d+\.){3}\d+(:\d+)?$/) ||
                                      line.match(/^localhost(:\d+)?$/i);
                    if (domainMatch) {
                        this.data.push({
                            name: '从文本提取',
                            url: line,
                            username: '',
                            password: '',
                            domain: this.extractDomain(line)
                        });
                    }
                }
            }
        });
    }

    extractDomain(url) {
        try {
            if (!url || !url.includes('://')) {
                return url;
            }
            const urlObj = new URL(url);
            return urlObj.hostname;
        } catch {
            return url;
        }
    }

    detectDuplicates() {
        const domainMap = new Map();
        const passwordMap = new Map();
        
        this.data.forEach((item, index) => {
            // 检测重复域名
            if (item.domain) {
                if (!domainMap.has(item.domain)) {
                    domainMap.set(item.domain, []);
                }
                domainMap.get(item.domain).push({...item, index});
            }
            
            // 检测重复密码
            if (item.password) {
                if (!passwordMap.has(item.password)) {
                    passwordMap.set(item.password, []);
                }
                passwordMap.get(item.password).push({...item, index});
            }
        });
        
        this.duplicates = [];
        
        // 添加重复域名
        domainMap.forEach((items, domain) => {
            if (items.length > 1) {
                this.duplicates.push({
                    type: 'domain',
                    key: domain,
                    items: items
                });
            }
        });
        
        // 添加重复密码
        passwordMap.forEach((items, password) => {
            if (items.length > 1) {
                this.duplicates.push({
                    type: 'password',
                    key: password.substring(0, 10) + '...',
                    items: items
                });
            }
        });
        
        this.stats.duplicates = this.duplicates.length;
    }

    updateStats() {
        this.stats.total = this.data.length;
        document.getElementById('totalCount').textContent = this.stats.total;
        document.getElementById('testedCount').textContent = this.stats.tested;
        document.getElementById('successCount').textContent = this.stats.success;
        document.getElementById('duplicateCount').textContent = this.stats.duplicates;
    }

    enableStartButton() {
        document.getElementById('startBtn').disabled = false;
    }

    showMessage(message, type) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // 显示通知
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // 3秒后隐藏通知
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);

        console.log(`${type.toUpperCase()}: ${message}`);
    }

    updateCurrentTestDisplay(item, status, responseDetails = null) {
        const currentSiteName = document.getElementById('currentSiteName');
        const currentUrl = document.getElementById('currentUrl');
        const currentStatus = document.getElementById('currentStatus');
        const currentResponse = document.getElementById('currentResponse');
        const responseDetailsSpan = document.getElementById('responseDetails');

        // 调试信息
        console.log('更新显示:', {
            name: item.name,
            url: item.url,
            domain: item.domain,
            status: status,
            responseDetails: responseDetails
        });

        if (currentSiteName) currentSiteName.textContent = item.name || '未命名';
        if (currentUrl) currentUrl.textContent = item.url || item.domain || '无URL';
        if (currentStatus) currentStatus.textContent = status;

        if (responseDetails && currentResponse && responseDetailsSpan) {
            responseDetailsSpan.textContent = responseDetails;
            currentResponse.style.display = 'block';
        } else if (currentResponse) {
            currentResponse.style.display = 'none';
        }
    }

    async startAnalysis() {
        if (this.data.length === 0) {
            this.showMessage('请先上传数据文件', 'error');
            return;
        }

        this.isRunning = true;
        this.isPaused = false;

        // 只在首次开始时重置，继续时保持当前进度
        if (this.currentIndex === 0 || document.getElementById('startBtn').textContent === '🚀 开始分析') {
            this.currentIndex = 0;
            this.results = [];

            // 重置统计
            this.stats.tested = 0;
            this.stats.success = 0;
            this.stats.failed = 0;
            this.stats.skipped = 0;
            this.stats.invalid = 0;
            this.stats.internal = 0;

            // 清空结果区域
            document.getElementById('resultsSection').innerHTML = '';

            // 检查内网跳过设置并提示
            const skipInternal = document.getElementById('skipInternalUrls')?.checked || false;
            if (skipInternal) {
                const internalCount = this.data.filter(item => this.shouldSkipInternal(item)).length;
                if (internalCount > 0) {
                    this.showMessage(`将快速跳过 ${internalCount} 个内网地址`, 'info');
                }
            }
        }

        // 更新UI
        document.getElementById('startBtn').disabled = true;
        document.getElementById('startBtn').textContent = '🚀 开始分析';
        document.getElementById('pauseBtn').disabled = false;
        document.getElementById('progressSection').style.display = 'block';
        document.getElementById('currentTest').style.display = 'block';

        await this.runAnalysis();
    }

    async runAnalysis() {
        const skipInternal = document.getElementById('skipInternalUrls')?.checked || false;

        while (this.currentIndex < this.data.length && this.isRunning && !this.isPaused) {
            const item = this.data[this.currentIndex];

            // 更新进度条
            const progress = ((this.currentIndex + 1) / this.data.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';

            // 快速检查是否需要跳过内网地址
            if (skipInternal && this.shouldSkipInternal(item)) {
                // 批量处理内网跳过，减少UI更新频率
                this.processBatchSkip(item);
                this.currentIndex++;

                // 每处理5个跳过项目或到达末尾时更新一次UI
                if (this.currentIndex % 5 === 0 || this.currentIndex >= this.data.length) {
                    this.updateStats();
                    // 非常短的延迟，让UI有时间更新，但保持快速
                    await this.sleep(10);
                }
                continue;
            }

            // 更新当前测试显示（只对实际测试的项目）
            this.updateCurrentTestDisplay(item, '准备测试...');

            try {
                const result = await this.testUrl(item);
                this.addResult(result);
                this.stats.tested++;

                if (result.status === 'success') {
                    this.stats.success++;
                } else if (result.status === 'failed') {
                    this.stats.failed++;
                } else if (result.status === 'skipped') {
                    this.stats.skipped++;
                } else if (result.status === 'internal') {
                    this.stats.internal++;
                } else if (result.status === 'invalid') {
                    this.stats.invalid++;
                }

            } catch (error) {
                const result = {
                    ...item,
                    status: 'error',
                    message: error.message,
                    statusCode: null,
                    title: null,
                    timestamp: new Date().toLocaleTimeString()
                };
                this.addResult(result);
                this.stats.failed++;
                this.stats.tested++;
            }

            this.updateStats();
            this.currentIndex++;

            // 只有实际测试的URL才需要延迟
            await this.sleep(500);
        }

        if (this.currentIndex >= this.data.length) {
            this.completeAnalysis();
        }
    }

    processBatchSkip(item) {
        const result = {
            ...item,
            status: 'skipped',
            message: '跳过内网地址（用户设置）',
            statusCode: null,
            title: null,
            timestamp: new Date().toLocaleTimeString()
        };

        this.addResult(result);
        this.stats.tested++;
        this.stats.skipped++;

        // 更新当前测试显示，但不等待
        this.updateCurrentTestDisplay(item, '跳过内网', '已设置跳过内网地址');
    }

    async testUrl(item) {
        return new Promise(async (resolve) => {
            let url = item.url;

            // 更宽松的URL验证
            if (!url || url.trim() === '') {
                this.updateCurrentTestDisplay(item, '失败', '无URL地址');
                resolve({
                    ...item,
                    status: 'failed',
                    message: '无URL地址',
                    statusCode: null,
                    title: null,
                    timestamp: new Date().toLocaleTimeString()
                });
                return;
            }

            // 清理URL
            url = url.trim();

            // 如果URL不包含协议，尝试添加https://
            if (!url.includes('://')) {
                // 检查是否是IP地址或localhost
                if (url.match(/^(\d+\.){3}\d+/) || url.toLowerCase().includes('localhost')) {
                    url = 'http://' + url;
                } else {
                    url = 'https://' + url;
                }
            }

            this.updateCurrentTestDisplay(item, '正在测试...', `尝试连接: ${url}`);

            // 检查是否为内网地址（如果到了这里说明没有被跳过，所以标记为内网）
            if (this.isInternalUrl(url)) {
                this.updateCurrentTestDisplay(item, '内网地址', '检测到内网地址');
                resolve({
                    ...item,
                    url: url,
                    status: 'internal',
                    message: '检测到内网地址',
                    statusCode: null,
                    title: null,
                    timestamp: new Date().toLocaleTimeString()
                });
                return;
            }

            try {
                // 先尝试获取详细信息（包括状态码和标题）
                this.updateCurrentTestDisplay(item, '获取详细信息...', '正在获取状态码和标题');
                const result = await this.fetchWithDetails(url);
                this.updateCurrentTestDisplay(item, '成功', `状态码: ${result.statusCode}, 标题: ${result.title}`);
                resolve({
                    ...item,
                    url: url, // 使用处理后的URL
                    ...result,
                    timestamp: new Date().toLocaleTimeString()
                });
            } catch (error) {
                // 如果详细获取失败，尝试简单的连通性测试
                try {
                    this.updateCurrentTestDisplay(item, '尝试简单连接...', '详细获取失败，尝试简单连接');
                    const simpleResult = await this.simpleConnectivityTest(url);
                    this.updateCurrentTestDisplay(item, '连接成功', simpleResult.message);
                    resolve({
                        ...item,
                        url: url, // 使用处理后的URL
                        ...simpleResult,
                        timestamp: new Date().toLocaleTimeString()
                    });
                } catch (finalError) {
                    this.updateCurrentTestDisplay(item, '连接失败', finalError.message);
                    resolve({
                        ...item,
                        url: url, // 使用处理后的URL
                        status: 'failed',
                        message: finalError.message || '链接无法访问',
                        statusCode: null,
                        title: null,
                        timestamp: new Date().toLocaleTimeString()
                    });
                }
            }
        });
    }

    async fetchWithDetails(url) {
        return new Promise((resolve, reject) => {
            // 尝试使用fetch获取响应信息
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 8000);

            // 先尝试HEAD请求获取状态码
            fetch(url, {
                method: 'HEAD',
                signal: controller.signal,
            }).then(async (response) => {
                clearTimeout(timeoutId);

                // HEAD请求成功，再尝试GET获取标题
                try {
                    const getController = new AbortController();
                    const getTimeoutId = setTimeout(() => getController.abort(), 5000);

                    const getResponse = await fetch(url, {
                        method: 'GET',
                        signal: getController.signal,
                    });

                    clearTimeout(getTimeoutId);
                    const text = await getResponse.text();
                    const titleMatch = text.match(/<title[^>]*>([^<]+)<\/title>/i);
                    const title = titleMatch ? titleMatch[1].trim() : '无标题';

                    resolve({
                        status: 'success',
                        message: `链接可访问 (${response.status})`,
                        statusCode: response.status,
                        title: title
                    });
                } catch (e) {
                    // GET失败但HEAD成功
                    resolve({
                        status: 'success',
                        message: `链接可访问 (${response.status})`,
                        statusCode: response.status,
                        title: '无法获取标题'
                    });
                }
            }).catch((error) => {
                clearTimeout(timeoutId);

                if (error.name === 'AbortError') {
                    reject(new Error('请求超时'));
                } else {
                    // HEAD请求失败，尝试直接GET
                    const getController = new AbortController();
                    const getTimeoutId = setTimeout(() => getController.abort(), 6000);

                    fetch(url, {
                        method: 'GET',
                        signal: getController.signal,
                    }).then(async (response) => {
                        clearTimeout(getTimeoutId);

                        try {
                            const text = await response.text();
                            const titleMatch = text.match(/<title[^>]*>([^<]+)<\/title>/i);
                            const title = titleMatch ? titleMatch[1].trim() : '无标题';

                            resolve({
                                status: 'success',
                                message: `链接可访问 (${response.status})`,
                                statusCode: response.status,
                                title: title
                            });
                        } catch (e) {
                            resolve({
                                status: 'success',
                                message: `链接可访问 (${response.status})`,
                                statusCode: response.status,
                                title: '无法解析标题'
                            });
                        }
                    }).catch((getError) => {
                        clearTimeout(getTimeoutId);
                        if (getError.name === 'AbortError') {
                            reject(new Error('请求超时'));
                        } else {
                            // 如果所有fetch都失败，尝试iframe方法
                            this.tryIframeMethod(url).then(resolve).catch(reject);
                        }
                    });
                }
            });
        });
    }

    async tryIframeMethod(url) {
        return new Promise((resolve, reject) => {
            // 创建隐藏的iframe来获取页面信息
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.style.width = '0';
            iframe.style.height = '0';
            iframe.style.position = 'absolute';
            iframe.style.left = '-9999px';

            const timeout = setTimeout(() => {
                if (iframe.parentNode) {
                    document.body.removeChild(iframe);
                }
                reject(new Error('iframe加载超时'));
            }, 6000);

            iframe.onload = () => {
                clearTimeout(timeout);
                try {
                    const title = iframe.contentDocument?.title || '无法获取标题';
                    if (iframe.parentNode) {
                        document.body.removeChild(iframe);
                    }
                    resolve({
                        status: 'success',
                        message: '链接可访问 (iframe)',
                        statusCode: 200,
                        title: title
                    });
                } catch (e) {
                    if (iframe.parentNode) {
                        document.body.removeChild(iframe);
                    }
                    // 如果无法访问iframe内容（跨域），仍然认为链接可访问
                    resolve({
                        status: 'success',
                        message: '链接可访问 (跨域)',
                        statusCode: 200,
                        title: '跨域限制，无法获取标题'
                    });
                }
            };

            iframe.onerror = () => {
                clearTimeout(timeout);
                if (iframe.parentNode) {
                    document.body.removeChild(iframe);
                }
                reject(new Error('iframe加载失败'));
            };

            document.body.appendChild(iframe);
            iframe.src = url;
        });
    }

    async simpleConnectivityTest(url) {
        return new Promise((resolve, reject) => {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 8000);

            // 尝试HEAD请求
            fetch(url, {
                method: 'HEAD',
                mode: 'no-cors',
                signal: controller.signal
            }).then(() => {
                clearTimeout(timeoutId);
                resolve({
                    status: 'success',
                    message: '链接可访问 (HEAD)',
                    statusCode: 'no-cors',
                    title: 'CORS限制，无法获取'
                });
            }).catch(() => {
                // HEAD失败，尝试GET请求
                const controller2 = new AbortController();
                const timeoutId2 = setTimeout(() => controller2.abort(), 5000);

                fetch(url, {
                    method: 'GET',
                    mode: 'no-cors',
                    signal: controller2.signal
                }).then(() => {
                    clearTimeout(timeoutId2);
                    resolve({
                        status: 'success',
                        message: '链接可访问 (GET)',
                        statusCode: 'no-cors',
                        title: 'CORS限制，无法获取'
                    });
                }).catch((error) => {
                    clearTimeout(timeoutId2);
                    let message = '链接无法访问';
                    if (error.name === 'AbortError') {
                        message = '请求超时';
                    } else if (error.message.includes('Failed to fetch')) {
                        message = '网络错误或CORS限制';
                    }

                    reject(new Error(message));
                });
            });
        });
    }

    shouldSkipInternal(item) {
        let url = item.url;
        if (!url || url.trim() === '') {
            return false;
        }

        // 清理URL
        url = url.trim();

        // 如果URL不包含协议，尝试添加协议进行检查
        if (!url.includes('://')) {
            if (url.match(/^(\d+\.){3}\d+/) || url.toLowerCase().includes('localhost')) {
                url = 'http://' + url;
            } else {
                url = 'https://' + url;
            }
        }

        return this.isInternalUrl(url);
    }

    isInternalUrl(url) {
        try {
            const urlObj = new URL(url);
            const hostname = urlObj.hostname;

            // 检查IPv4内网IP
            const ipv4InternalPatterns = [
                /^192\.168\./,
                /^10\./,
                /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
                /^127\./,
                /^localhost$/i,
                /\.local$/i
            ];

            // 检查IPv6内网地址
            const ipv6InternalPatterns = [
                /^::1$/,           // IPv6 localhost
                /^::$/,            // IPv6 any
                /^fe80:/i,         // IPv6 link-local
                /^fc00:/i,         // IPv6 unique local
                /^fd00:/i,         // IPv6 unique local
                /^\[::1\]$/,       // IPv6 localhost with brackets
                /^\[fe80:/i,       // IPv6 link-local with brackets
                /^\[fc00:/i,       // IPv6 unique local with brackets
                /^\[fd00:/i        // IPv6 unique local with brackets
            ];

            return ipv4InternalPatterns.some(pattern => pattern.test(hostname)) ||
                   ipv6InternalPatterns.some(pattern => pattern.test(hostname));
        } catch {
            // 如果URL解析失败，检查原始字符串
            const urlLower = url.toLowerCase();
            return urlLower.includes('192.168.') ||
                   urlLower.includes('10.') ||
                   urlLower.includes('172.') ||
                   urlLower.includes('127.') ||
                   urlLower.includes('localhost') ||
                   urlLower.includes('.local') ||
                   urlLower.includes('::1') ||
                   urlLower.includes('[::1]') ||
                   urlLower.includes('fe80:') ||
                   urlLower.includes('fc00:') ||
                   urlLower.includes('fd00:');
        }
    }

    addResult(result) {
        const resultsSection = document.getElementById('resultsSection');
        const resultItem = document.createElement('div');
        resultItem.className = 'result-item';

        const statusClass = `status-${result.status}`;
        const statusText = {
            'success': '✅ 成功',
            'failed': '❌ 失败',
            'skipped': '⏭️ 跳过',
            'invalid': '🚫 无效',
            'internal': '🏠 内网'
        }[result.status] || '❓ 未知';

        // 构建详细信息
        let detailsHtml = `<small style="color: #666;">${result.message}</small>`;

        if (result.statusCode) {
            detailsHtml += `<br><small style="color: #888;">状态码: ${result.statusCode}</small>`;
        }

        if (result.title && result.title !== '跨域限制，无法获取标题' && result.title !== 'CORS限制，无法获取') {
            const truncatedTitle = result.title.length > 50 ? result.title.substring(0, 50) + '...' : result.title;
            detailsHtml += `<br><small style="color: #555;" title="${result.title}">标题: ${truncatedTitle}</small>`;
        }

        resultItem.innerHTML = `
            <div style="flex: 1;">
                <strong>${result.name}</strong><br>
                <small style="color: #007bff;">${result.url || result.domain}</small><br>
                ${detailsHtml}
            </div>
            <div style="text-align: right; min-width: 120px;">
                <span class="${statusClass}">${statusText}</span><br>
                <small style="color: #999;">${result.timestamp}</small>
            </div>
        `;

        resultsSection.appendChild(resultItem);
        resultsSection.scrollTop = resultsSection.scrollHeight;

        this.results.push(result);
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    pauseAnalysis() {
        this.isPaused = true;
        this.isRunning = false;
        document.getElementById('pauseBtn').disabled = true;
        document.getElementById('startBtn').disabled = false;
        document.getElementById('startBtn').textContent = '▶️ 继续分析';
        this.showMessage(`已暂停，当前进度: ${this.currentIndex}/${this.data.length}`, 'info');
    }

    skipCurrent() {
        if (this.currentIndex < this.data.length) {
            const item = this.data[this.currentIndex];
            const result = {
                ...item,
                status: 'skipped',
                message: '用户跳过',
                statusCode: null,
                title: null,
                timestamp: new Date().toLocaleTimeString()
            };
            this.addResult(result);
            this.stats.skipped++;
            this.stats.tested++;
            this.updateStats();
            this.currentIndex++;

            if (this.currentIndex >= this.data.length) {
                this.completeAnalysis();
            } else if (this.isRunning) {
                // 如果正在运行，继续下一个
                setTimeout(() => this.runAnalysis(), 100);
            }
        }
    }

    markInvalid() {
        if (this.currentIndex < this.data.length) {
            const item = this.data[this.currentIndex];
            const result = {
                ...item,
                status: 'invalid',
                message: '用户标记为无效网站',
                statusCode: null,
                title: null,
                timestamp: new Date().toLocaleTimeString()
            };
            this.addResult(result);
            this.stats.invalid++;
            this.stats.tested++;
            this.updateStats();
            this.currentIndex++;

            if (this.currentIndex >= this.data.length) {
                this.completeAnalysis();
            } else if (this.isRunning) {
                // 如果正在运行，继续下一个
                setTimeout(() => this.runAnalysis(), 100);
            }
        }
    }

    markInternal() {
        if (this.currentIndex < this.data.length) {
            const item = this.data[this.currentIndex];
            const result = {
                ...item,
                status: 'internal',
                message: '用户标记为内网网站',
                statusCode: null,
                title: null,
                timestamp: new Date().toLocaleTimeString()
            };
            this.addResult(result);
            this.stats.internal++;
            this.stats.tested++;
            this.updateStats();
            this.currentIndex++;

            if (this.currentIndex >= this.data.length) {
                this.completeAnalysis();
            } else if (this.isRunning) {
                // 如果正在运行，继续下一个
                setTimeout(() => this.runAnalysis(), 100);
            }
        }
    }

    completeAnalysis() {
        this.isRunning = false;
        document.getElementById('startBtn').disabled = false;
        document.getElementById('startBtn').textContent = '🚀 开始分析';
        document.getElementById('pauseBtn').disabled = true;
        document.getElementById('currentTest').style.display = 'none';
        document.getElementById('exportBtn').disabled = false;

        this.generateReport();
        this.showMessage('分析完成！', 'success');
    }

    generateReport() {
        const reportContent = document.getElementById('reportContent');

        let html = `
            <h4>📊 分析摘要</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 20px;">
                <div style="text-align: center; padding: 10px; background: #f0f8ff; border-radius: 8px;">
                    <div style="font-size: 1.5em; font-weight: bold; color: #4facfe;">${this.stats.total}</div>
                    <div style="font-size: 0.9em; color: #666;">总条目</div>
                </div>
                <div style="text-align: center; padding: 10px; background: #f0fff0; border-radius: 8px;">
                    <div style="font-size: 1.5em; font-weight: bold; color: #4caf50;">${this.stats.success}</div>
                    <div style="font-size: 0.9em; color: #666;">有效链接</div>
                </div>
                <div style="text-align: center; padding: 10px; background: #fff0f0; border-radius: 8px;">
                    <div style="font-size: 1.5em; font-weight: bold; color: #f44336;">${this.stats.failed}</div>
                    <div style="font-size: 0.9em; color: #666;">失败链接</div>
                </div>
                <div style="text-align: center; padding: 10px; background: #fff8e1; border-radius: 8px;">
                    <div style="font-size: 1.5em; font-weight: bold; color: #ff9800;">${this.stats.duplicates}</div>
                    <div style="font-size: 0.9em; color: #666;">重复项</div>
                </div>
            </div>
        `;

        // 添加重复项详情
        if (this.duplicates.length > 0) {
            html += `<h4>🔍 重复项详情</h4>`;

            this.duplicates.forEach(duplicate => {
                const typeText = duplicate.type === 'domain' ? '重复域名' : '重复密码';
                const icon = duplicate.type === 'domain' ? '🌐' : '🔑';

                html += `
                    <div class="duplicate-group">
                        <h5>${icon} ${typeText}: ${duplicate.key}</h5>
                `;

                duplicate.items.forEach(item => {
                    html += `
                        <div class="duplicate-item">
                            <strong>${item.name}</strong> - ${item.url || item.domain}
                            ${item.username ? `<br><small>用户名: ${item.username}</small>` : ''}
                        </div>
                    `;
                });

                html += `</div>`;
            });
        }

        // 添加失败链接详情
        const failedResults = this.results.filter(r => r.status === 'failed' || r.status === 'invalid');
        if (failedResults.length > 0) {
            html += `<h4>❌ 问题链接详情</h4>`;

            failedResults.forEach(result => {
                html += `
                    <div style="padding: 10px; margin-bottom: 10px; background: #fff5f5; border-left: 4px solid #f44336; border-radius: 4px;">
                        <strong>${result.name}</strong><br>
                        <small>${result.url}</small><br>
                        <small style="color: #f44336;">${result.message}</small>
                    </div>
                `;
            });
        }

        reportContent.innerHTML = html;
    }

    exportReport() {
        const report = this.generateExportData();
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `bitwarden-analysis-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // 同时导出CSV格式
        this.exportCSV();
    }

    generateExportData() {
        return {
            timestamp: new Date().toISOString(),
            summary: this.stats,
            duplicates: this.duplicates,
            results: this.results,
            recommendations: this.generateRecommendations()
        };
    }

    generateRecommendations() {
        const recommendations = [];

        if (this.stats.failed > 0) {
            recommendations.push({
                type: 'cleanup',
                priority: 'high',
                message: `发现 ${this.stats.failed} 个无效链接，建议清理这些条目`
            });
        }

        if (this.stats.duplicates > 0) {
            recommendations.push({
                type: 'security',
                priority: 'medium',
                message: `发现 ${this.stats.duplicates} 组重复项，建议检查并合并重复的账户信息`
            });
        }

        if (this.stats.internal > 0) {
            recommendations.push({
                type: 'organization',
                priority: 'low',
                message: `发现 ${this.stats.internal} 个内网网站，建议单独分类管理`
            });
        }

        return recommendations;
    }

    exportCSV() {
        const csvData = [
            ['名称', '网址', '用户名', '域名', '状态', '状态码', '网页标题', '消息', '时间']
        ];

        this.results.forEach(result => {
            csvData.push([
                result.name || '',
                result.url || '',
                result.username || '',
                result.domain || '',
                result.status || '',
                result.statusCode || '',
                result.title || '',
                result.message || '',
                result.timestamp || ''
            ]);
        });

        const csvContent = csvData.map(row =>
            row.map(field => `"${field.toString().replace(/"/g, '""')}"`).join(',')
        ).join('\n');

        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `bitwarden-analysis-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// 全局函数
let analyzer;

function initializeApp() {
    analyzer = new BitwardenAnalyzer();
}

function startAnalysis() {
    analyzer.startAnalysis();
}

function pauseAnalysis() {
    analyzer.pauseAnalysis();
}

function skipCurrent() {
    analyzer.skipCurrent();
}

function markInvalid() {
    analyzer.markInvalid();
}

function markInternal() {
    analyzer.markInternal();
}

function exportReport() {
    analyzer.exportReport();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initializeApp);
