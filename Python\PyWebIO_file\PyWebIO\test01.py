from pywebio.input import *
from pywebio.output import *
import time

# pword = input('passwd:', type=PASSWORD)

# slt = select('Which one',['A','B'])

# check_box = checkbox('choice one', ['a', 'b'])

# single_choice = radio('Choice one', ['a','b'])

# text = textarea('Text Area', rows=3, placeholder='aaaaaaaabbbbbb')

# img=file_upload("Select one pic", accept='image/*')

# silder_bar = slider('Slider test')

# popup('popup title','popup text content')
# toast('New message ')

# toast('New message 2')

# put_table([
#     ['Name','Hobbies'],
#     ['Tom', put_scope('hobby', content=put_text('Coding'))]
# ])
# time.sleep(1)
# with use_scope('hobby', clear=True):
#     put_text('Movie')# hobby is reset to Movie# append Music, Drama to hobby
# time.sleep(1)
# with use_scope('hobby'):
#     put_text('Music')
#     put_text('Drama')

# with use_scope('scope3'):
#     put_text('text1 in scope3') # output to current scope: scope3
#     put_text('text in ROOT scope', scope='ROOT') # output to ROOT Scope
# put_text('text2 in scope3', scope='scope3') 
# with put_collapse('This is title'):
#     for i in range(4):
#      put_text(i)
# put_table([
# ['Commodity', 'Price'],
# ['Apple', '5.5'],
# ['Banana', '7'],
# ])

# from functools import partial
# def edit_row(choice, row):
#     put_text("You click %s button ar row %s" % (choice, row))
# put_table([
# ['Idx', 'Actions'],
# [1, put_buttons(['edit', 'delete'], onclick=partial(edit_row, row=1))],
# [2, put_buttons(['edit', 'delete'], onclick=partial(edit_row, row=2))],
# [3, put_buttons(['edit', 'delete'], onclick=partial(edit_row, row=3))],
# ])


# def btn_click(btn_val):
#     put_text("You click %s button" % btn_val)
# put_buttons(['A', 'B', 'C'], onclick=btn_click) # a group of buttons
# put_button("Click me", onclick=lambda: toast("Clicked")) # single button

put_table([['a','b'],[1,2],['a端', put_table([['jifang','a'],['zhandian','bbb']])],[3,put_scope('aa', content=['scope_aa'])]])
time.sleep(1)
with use_scope('aa', clear=True):
    put_text('socpe_aa2')
put_table([['a','b'],[1]])
