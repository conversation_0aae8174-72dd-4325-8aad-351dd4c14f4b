#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

from app import analyzer
import os

def test_analyzer():
    print("测试分析器...")
    
    # 测试数据解析
    test_file = "test-python.csv"
    if os.path.exists(test_file):
        print(f"解析文件: {test_file}")
        data = analyzer.parse_data_file(test_file)
        print(f"解析结果: {len(data)} 条记录")
        
        if data:
            print("前3条记录:")
            for i, item in enumerate(data[:3]):
                print(f"  {i+1}. {item}")
        
        # 测试URL检查
        if data:
            test_item = data[0]
            print(f"\n测试URL: {test_item.get('url')}")
            
            # 测试内网检查
            is_internal = analyzer.is_internal_url(test_item.get('url', ''))
            print(f"是否内网: {is_internal}")
            
            # 测试URL测试 - 使用百度测试
            test_url = "https://www.baidu.com"
            print(f"测试URL连接: {test_url}")
            result = analyzer.test_url(test_url)
            print(f"测试结果: {result}")
    else:
        print(f"文件不存在: {test_file}")

if __name__ == '__main__':
    test_analyzer()
