# Fetch MCP工具演示指南

## 概述

这是一个专门针对fetch功能的MCP（Model Context Protocol）工具演示应用，使用Flet构建了优化的左右分栏GUI界面。

## 界面布局

### 🎨 左右分栏设计
- **左侧面板**: 配置区域（500px宽）
- **右侧面板**: 结果输出区域（680px宽）
- **总宽度**: 1200px，高度700px

### 📱 界面组件

#### 左侧配置面板
1. **LLM API配置**
   - API URL输入框
   - API Key输入框（密码模式）

2. **Fetch工具配置**
   - 目标URL输入框
   - HTTP方法下拉选择
   - 请求头输入框（JSON格式）
   - 请求体输入框（JSON格式）

3. **执行按钮**
   - 🚀 直接执行Fetch
   - 🤖 MCP协议调用

#### 右侧结果面板
- 状态显示文本
- 大型结果显示区域（使用等宽字体）

## 功能说明

### 1. 直接执行Fetch

**工作流程：**
```
用户配置 → 本地HTTP请求 → 显示原始结果
```

**实现过程：**
1. 获取用户配置的参数
2. 解析JSON格式的请求头和请求体
3. 使用requests库执行HTTP请求
4. 格式化响应结果
5. 在右侧面板显示结果

**预期响应格式：**
```json
{
  "execution_type": "直接执行",
  "tool": "fetch",
  "timestamp": "2024-01-01 12:00:00",
  "request": {
    "url": "https://httpbin.org/json",
    "method": "GET",
    "headers": {},
    "body": null
  },
  "response": {
    "success": true,
    "status_code": 200,
    "status_text": "OK",
    "headers": {
      "content-type": "application/json",
      "content-length": "429"
    },
    "content_length": 429,
    "content": "响应内容...",
    "json": {
      "解析后的": "JSON数据"
    }
  }
}
```

### 2. MCP协议调用

**工作流程：**
```
用户配置 → 构建MCP Prompt → 调用LLM API → 显示LLM响应
```

**实现过程：**
1. 构建包含工具信息的详细prompt
2. 调用配置的LLM API
3. 获取LLM的理解和响应
4. 展示完整的MCP调用过程

**MCP Prompt示例：**
```
请使用fetch工具获取网络资源。以下是详细的配置信息：

工具名称: fetch
描述: 执行HTTP请求获取网络资源

参数配置:
- URL: https://api.github.com/users/octocat
- HTTP方法: GET
- 请求头: {"User-Agent": "MCP-Tool"}
- 请求体: 无

请执行fetch工具并返回详细的结果...
```

**预期MCP响应格式：**
```json
{
  "execution_type": "MCP协议调用",
  "tool": "fetch",
  "timestamp": "2024-01-01 12:00:00",
  "mcp_request": {
    "tool_name": "fetch",
    "parameters": {
      "url": "https://api.github.com/users/octocat",
      "method": "GET",
      "headers": "{\"User-Agent\": \"MCP-Tool\"}",
      "body": ""
    },
    "prompt": "完整的prompt内容..."
  },
  "llm_response": "LLM的响应内容...",
  "mcp_workflow": [
    "1. 用户配置fetch参数",
    "2. 构建MCP工具调用prompt",
    "3. 发送prompt到LLM API",
    "4. LLM理解并模拟工具执行",
    "5. 返回结构化响应"
  ]
}
```

## 使用步骤

### 第一步：启动应用
```bash
cd Python/Flet
python fetch_mcp_tool.py
```

### 第二步：配置LLM API（用于MCP调用）
1. 在"LLM API URL"中输入API端点
   - OpenAI: `https://api.openai.com/v1/chat/completions`
   - 其他兼容API端点
2. 在"API Key"中输入对应的密钥

### 第三步：配置Fetch参数
1. **目标URL**: 输入要请求的网址
   - 示例: `https://httpbin.org/json`
   - 示例: `https://api.github.com/users/octocat`

2. **HTTP方法**: 选择请求方法
   - GET, POST, PUT, DELETE

3. **请求头** (可选): JSON格式
   ```json
   {
     "Content-Type": "application/json",
     "User-Agent": "MCP-Fetch-Tool",
     "Authorization": "Bearer your-token"
   }
   ```

4. **请求体** (POST/PUT时使用): JSON格式
   ```json
   {
     "message": "Hello from MCP",
     "timestamp": "2024-01-01T12:00:00Z"
   }
   ```

### 第四步：执行操作
1. **直接执行**: 点击"🚀 直接执行Fetch"
   - 本地执行HTTP请求
   - 显示实际的网络响应

2. **MCP调用**: 点击"🤖 MCP协议调用"
   - 通过LLM API模拟MCP工具调用
   - 展示LLM对工具调用的理解

### 第五步：查看结果
- 右侧面板实时显示执行结果
- 状态栏显示当前操作状态
- 结果以格式化的JSON显示

## 配置示例

### 示例1：获取GitHub用户信息
```
URL: https://api.github.com/users/octocat
方法: GET
请求头: {"User-Agent": "MCP-Tool-Demo"}
请求体: (空)
```

### 示例2：POST请求到测试API
```
URL: https://httpbin.org/post
方法: POST
请求头: {"Content-Type": "application/json"}
请求体: {"name": "MCP Test", "type": "fetch"}
```

### 示例3：带认证的API请求
```
URL: https://api.example.com/data
方法: GET
请求头: {"Authorization": "Bearer your-api-token"}
请求体: (空)
```

## 技术特性

### 🎯 核心功能
- ✅ 专注于fetch工具的MCP演示
- ✅ 左右分栏的优化界面布局
- ✅ 实时状态显示和结果输出
- ✅ 支持完整的HTTP方法和参数
- ✅ JSON格式的请求头和请求体
- ✅ 错误处理和异常显示

### 🔧 技术实现
- **GUI框架**: Flet (基于Flutter)
- **HTTP客户端**: requests库
- **LLM集成**: 支持OpenAI兼容API
- **数据格式**: JSON结构化输出
- **字体**: 结果区域使用等宽字体

### 🎨 界面优化
- **响应式布局**: 自适应窗口大小
- **视觉分离**: 清晰的左右分栏
- **状态反馈**: 实时的操作状态显示
- **结果格式化**: 美观的JSON输出

## 扩展建议

1. **添加更多HTTP功能**
   - 文件上传支持
   - Cookie管理
   - 重定向控制

2. **增强MCP集成**
   - 工具链调用
   - 批量操作
   - 结果缓存

3. **改进用户体验**
   - 配置保存/加载
   - 历史记录
   - 结果导出

4. **安全增强**
   - API密钥加密存储
   - 请求验证
   - 错误日志

这个fetch MCP工具演示为理解和实现MCP协议提供了一个清晰、实用的示例。
