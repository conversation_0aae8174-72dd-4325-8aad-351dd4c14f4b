# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON>o
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-04 00:19+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:2
#: f07db4f0a8674cbfb9eca931fc5c07e7
msgid "tree_view"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:8
#: 2c0dad1607fc4f9c87cc6c51b691017e
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:21:<autosummary>:1
#: b8de3ba320b64512bdebb118dc655f00
msgid ""
":py:obj:`TreeItemDelegate "
"<qfluentwidgets.components.widgets.tree_view.TreeItemDelegate>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:26
#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:21:<autosummary>:1
#: 5c290ae1822a4fa1b97c416bca34cd34 f91107e009a443a2ae511da8d664c4ae
msgid "Tree item delegate"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:21:<autosummary>:1
#: f6c94d4f9e714719abf4c9073ac61212
msgid ""
":py:obj:`TreeViewBase "
"<qfluentwidgets.components.widgets.tree_view.TreeViewBase>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:34
#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:21:<autosummary>:1
#: f91107e009a443a2ae511da8d664c4ae
msgid "Tree view base class"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:21:<autosummary>:1
#: e1b64a52e1754968947f294be7197587
msgid ""
":py:obj:`TreeWidget "
"<qfluentwidgets.components.widgets.tree_view.TreeWidget>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:44
#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:21:<autosummary>:1
#: 92fef19293d1428797d35613b445cff4
msgid "Tree widget"
msgstr "树状部件"

#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:21:<autosummary>:1
#: f6c94d4f9e714719abf4c9073ac61212
msgid ""
":py:obj:`TreeView "
"<qfluentwidgets.components.widgets.tree_view.TreeView>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:51
#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:21:<autosummary>:1
#: 94fa9a4897814aca8da40f34e5b015de
msgid "Tree view"
msgstr "树状视图"

#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:24
#: 706f72312b5f46efb75f2c42e7739d2d
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QStyledItemDelegate`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:42
#: 706f72312b5f46efb75f2c42e7739d2d
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTreeWidget`, :py:obj:`TreeViewBase`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/widgets/tree_view/index.rst:49
#: 706f72312b5f46efb75f2c42e7739d2d
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTreeView`, :py:obj:`TreeViewBase`"
msgstr ""

#~ msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTreeWidget`"
#~ msgstr ""

#~ msgid "Bases: :py:obj:`PyQt5.QtWidgets.QTreeView`"
#~ msgstr ""

