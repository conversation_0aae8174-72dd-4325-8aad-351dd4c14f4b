SampleCard {
    border: 1px solid rgb(35, 35, 35);
    border-radius: 6px;
    background-color: rgb(50, 50, 50);
}

SampleCard:hover {
    background-color: rgb(62, 62, 62);
    border-color: rgb(37, 37, 37);
}


#titleLabel {
    color: white;
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    font-weight: bold;
}

#contentLabel {
    color: rgb(208, 208, 208);
    font: 12px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
}

#viewTitleLabel {
    color: white;
    font: 20px "Segoe UI SemiBold", "Microsoft YaHei", 'PingFang SC';
}