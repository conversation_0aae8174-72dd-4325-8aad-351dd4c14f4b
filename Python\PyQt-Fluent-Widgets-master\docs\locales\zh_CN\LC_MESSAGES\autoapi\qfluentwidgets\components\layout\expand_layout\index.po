# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2021, z<PERSON><PERSON><PERSON>o
# This file is distributed under the same license as the PyQt-Fluent-Widgets
# package.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PyQt-Fluent-Widgets \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-22 20:49+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#: ../../source/autoapi/qfluentwidgets/components/layout/expand_layout/index.rst:2
#: 3ec3eb77bf3f4ecfba152dab5e75f713
msgid "expand_layout"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/layout/expand_layout/index.rst:8
#: f4b6f61aed274753863c409deb1a2e72
msgid "Module Contents"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/layout/expand_layout/index.rst:18:<autosummary>:1
#: 3aa290f6235a4f298dac3678c68145f3
msgid ""
":py:obj:`ExpandLayout "
"<qfluentwidgets.components.layout.expand_layout.ExpandLayout>`\\"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/layout/expand_layout/index.rst:23
#: ../../source/autoapi/qfluentwidgets/components/layout/expand_layout/index.rst:18:<autosummary>:1
#: 1cb8672bb6a248c39e62ffb261064430 e1a1b7035da148589f0030a0c8a3cc4c
msgid "Expand layout"
msgstr "可自动伸缩的布局"

#: ../../source/autoapi/qfluentwidgets/components/layout/expand_layout/index.rst:21
#: 63c21cabf5db4a0e999279892de17c8e
msgid "Bases: :py:obj:`PyQt5.QtWidgets.QLayout`"
msgstr ""

#: ../../source/autoapi/qfluentwidgets/components/layout/expand_layout/index.rst:48
#: 6ae76ecad7934c92a4fe3dc4d41741c5
msgid "get the minimal height according to width"
msgstr "根据宽度获取最小高度"

