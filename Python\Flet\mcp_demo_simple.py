"""
MCP工具演示 - 简化版本
展示MCP工具的核心概念和实现过程
"""

import flet as ft
import requests
import json
import asyncio
from typing import Dict, Any

class SimpleMCPDemo:
    def __init__(self, page: ft.Page):
        self.page = page
        self.page.title = "MCP工具演示 - 简化版"
        self.page.window_width = 900
        self.page.window_height = 600
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 标题和说明
        title = ft.Text("MCP工具演示 - Fetch功能", size=20, weight=ft.FontWeight.BOLD)
        description = ft.Text(
            "这个演示展示了MCP工具的实现过程：\n"
            "1. 直接执行 - 本地调用工具功能\n"
            "2. MCP模拟 - 构建prompt并显示预期响应",
            size=14,
            color=ft.Colors.GREY_700
        )
        
        # 配置区域
        self.url_field = ft.TextField(
            label="URL",
            value="https://httpbin.org/json",
            width=400
        )
        
        self.method_dropdown = ft.Dropdown(
            label="方法",
            value="GET",
            width=100,
            options=[
                ft.dropdown.Option("GET"),
                ft.dropdown.Option("POST"),
            ]
        )
        
        # 按钮
        direct_btn = ft.ElevatedButton(
            "1. 直接执行Fetch",
            on_click=self.direct_fetch,
            bgcolor=ft.colors.BLUE_600,
            color=ft.colors.WHITE
        )
        
        mcp_btn = ft.ElevatedButton(
            "2. MCP模拟调用",
            on_click=self.mcp_simulation,
            bgcolor=ft.colors.GREEN_600,
            color=ft.colors.WHITE
        )
        
        # 结果显示
        self.result_text = ft.TextField(
            label="执行结果",
            multiline=True,
            min_lines=15,
            max_lines=20,
            width=850,
            read_only=True
        )
        
        # 状态
        self.status = ft.Text("就绪", color=ft.colors.GREEN_600)
        
        # 布局
        self.page.add(
            ft.Column([
                title,
                description,
                ft.Divider(),
                
                ft.Text("配置参数", size=16, weight=ft.FontWeight.BOLD),
                ft.Row([self.url_field, self.method_dropdown]),
                
                ft.Text("执行步骤", size=16, weight=ft.FontWeight.BOLD),
                ft.Row([direct_btn, mcp_btn], spacing=20),
                
                ft.Divider(),
                self.status,
                self.result_text,
            ], spacing=10)
        )
    
    async def direct_fetch(self, e):
        """步骤1: 直接执行fetch工具"""
        try:
            self.status.value = "正在执行直接fetch请求..."
            self.status.color = ft.colors.ORANGE_600
            self.page.update()
            
            url = self.url_field.value
            method = self.method_dropdown.value
            
            # 执行实际的HTTP请求
            response = requests.request(method=method, url=url, timeout=10)
            
            # 构建结果
            result = {
                "step": "1. 直接执行",
                "tool": "fetch",
                "input": {
                    "url": url,
                    "method": method
                },
                "output": {
                    "success": True,
                    "status_code": response.status_code,
                    "headers": dict(list(response.headers.items())[:5]),  # 只显示前5个头
                    "content_length": len(response.text),
                    "content_preview": response.text[:200] + "..." if len(response.text) > 200 else response.text
                },
                "execution_time": "实际执行时间",
                "notes": "这是直接调用工具的结果，实际执行了HTTP请求"
            }
            
            # 尝试解析JSON
            try:
                json_data = response.json()
                result["output"]["json_preview"] = str(json_data)[:300] + "..." if len(str(json_data)) > 300 else json_data
            except:
                result["output"]["json_preview"] = "非JSON响应"
            
            self.result_text.value = json.dumps(result, indent=2, ensure_ascii=False)
            self.status.value = f"直接执行成功 - 状态码: {response.status_code}"
            self.status.color = ft.colors.GREEN_600
            
        except Exception as ex:
            error_result = {
                "step": "1. 直接执行",
                "tool": "fetch",
                "input": {
                    "url": self.url_field.value,
                    "method": self.method_dropdown.value
                },
                "output": {
                    "success": False,
                    "error": str(ex)
                },
                "notes": "直接执行失败"
            }
            
            self.result_text.value = json.dumps(error_result, indent=2, ensure_ascii=False)
            self.status.value = f"直接执行失败: {str(ex)}"
            self.status.color = ft.colors.RED_600
        
        self.page.update()
    
    async def mcp_simulation(self, e):
        """步骤2: MCP协议模拟"""
        try:
            self.status.value = "正在模拟MCP调用过程..."
            self.status.color = ft.colors.ORANGE_600
            self.page.update()
            
            url = self.url_field.value
            method = self.method_dropdown.value
            
            # 模拟MCP协议的调用过程
            mcp_process = {
                "step": "2. MCP协议模拟",
                "description": "模拟通过MCP协议调用LLM来执行fetch工具",
                
                "mcp_request": {
                    "protocol": "MCP (Model Context Protocol)",
                    "tool_name": "fetch",
                    "tool_description": "获取网络资源的工具",
                    "parameters": {
                        "url": url,
                        "method": method,
                        "headers": {},
                        "timeout": 30
                    }
                },
                
                "llm_prompt": f"""
请使用fetch工具获取以下URL的数据：

工具调用：
- 工具名称: fetch
- URL: {url}
- 方法: {method}
- 超时: 30秒

请执行fetch请求并返回结构化的结果。
""",
                
                "expected_llm_response": {
                    "tool_call": "fetch",
                    "reasoning": f"用户要求获取 {url} 的数据，我将使用fetch工具执行{method}请求",
                    "execution": "正在执行fetch工具...",
                    "result": {
                        "success": True,
                        "status_code": 200,
                        "content_type": "application/json",
                        "data": "根据实际URL返回的数据",
                        "execution_time": "约500ms"
                    },
                    "summary": f"成功获取了 {url} 的数据，返回状态码200"
                },
                
                "mcp_workflow": [
                    "1. 用户发起工具调用请求",
                    "2. MCP协议封装工具调用信息",
                    "3. LLM接收并理解工具调用意图",
                    "4. LLM执行工具并获取结果",
                    "5. LLM格式化并返回结果给用户"
                ],
                
                "advantages": [
                    "标准化的工具调用协议",
                    "LLM可以理解和推理工具使用",
                    "支持复杂的工具组合和链式调用",
                    "提供丰富的上下文和错误处理"
                ],
                
                "notes": "这是MCP协议的模拟演示，展示了LLM如何理解和执行工具调用"
            }
            
            self.result_text.value = json.dumps(mcp_process, indent=2, ensure_ascii=False)
            self.status.value = "MCP模拟完成 - 展示了协议调用过程"
            self.status.color = ft.colors.GREEN_600
            
        except Exception as ex:
            self.result_text.value = f"MCP模拟错误: {str(ex)}"
            self.status.value = f"MCP模拟失败: {str(ex)}"
            self.status.color = ft.colors.RED_600
        
        self.page.update()

def main(page: ft.Page):
    SimpleMCPDemo(page)

if __name__ == "__main__":
    print("启动MCP工具演示...")
    print("这个演示展示了:")
    print("1. 直接执行工具的过程")
    print("2. MCP协议的工作原理")
    print("3. LLM如何理解和执行工具调用")
    print("\n应用正在启动，请在浏览器中查看界面...")
    
    ft.app(target=main)
