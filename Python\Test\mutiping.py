#!/usr/bin/env python3

import sys

try:
    import asyncio
except ImportError:
    sys.exit('Error: Python 3.4 or higher is required.')

import argparse
import curses
import re
import signal


class MultiPing(object):
    def __init__(self, window, hosts, *args):
        self.window = window
        self.hosts = hosts
        self.args = args
        max_host_length = max(len(host) for host in hosts)
        max_host_length = min(max_host_length, 28)
        self.host_col_length = max_host_length

        _, width = window.getmaxyx()
        self.avg_col_pos = width - 7
        self.loss_col_pos = self.avg_col_pos - 7
        self.tr_col_pos = self.loss_col_pos - 8
        self.output_col_length = self.tr_col_pos - self.host_col_length - 2

        self.init_window()

        self.result_dict = {i: {'cid': i, 'host': host} for i, host in enumerate(self.hosts)}

    def init_window(self):
        self.window.addstr(' '.join(['HOST'.ljust(self.host_col_length),
                                     'PING OUTPUT'.ljust(self.output_col_length),
                                     '    T/R   LOSS AVG(ms)']), curses.A_STANDOUT)
        for i, host in enumerate(self.hosts):
            if len(host) <= self.host_col_length:
                self.window.addstr(i + 1, 0, host.ljust(self.host_col_length))
            else:
                self.window.addstr(i + 1, 0, host[:self.host_col_length - 4] + '... ')
        self.window.refresh()

    def run(self):
        loop = asyncio.get_event_loop()

        coros = [self.ping(host, i) for i, host in enumerate(self.hosts)]
        commands = asyncio.gather(*coros)

        loop.add_signal_handler(signal.SIGINT, lambda: 0)

        loop.run_until_complete(commands)

        self.embolden_result()

        loop.close()

    def embolden_result(self):
        l = [r for r in self.result_dict.values() if r.get('loss')]
        if len(l) > 2:
            l.sort(key=lambda r: float(r['loss'].rstrip('%')))
            if l[0]['loss'] != l[-1]['loss']:
                for r in l:
                    if r['loss'] in (l[0]['loss'], l[-1]['loss']):
                        self.add_loss(r['cid'] + 1, r['loss'], curses.A_BOLD)

        l = [r for r in self.result_dict.values() if r.get('avg')]
        if len(l) > 2:
            l.sort(key=lambda r: float(r['avg']))
            if l[0]['avg'] != l[-1]['avg']:
                for r in l:
                    if r['avg'] in (l[0]['avg'], l[-1]['avg']):
                        self.add_avg(r['cid'] + 1, r['avg'], curses.A_BOLD)

    def add_output(self, y, output, *args):
        if len(output) > self.output_col_length:
            pos = (self.output_col_length - 3) // 2
            output = '{}...{}'.format(output[:pos], output[-pos:])
        self.window.addstr(y, self.host_col_length + 1, output.ljust(self.output_col_length), *args)

    def add_avg(self, y, avg, *args):
        self.window.addstr(y, self.avg_col_pos, avg.rjust(7), *args)

    def add_loss(self, y, loss, *args):
        self.window.addstr(y, self.loss_col_pos, loss.rjust(6), *args)

    def add_tr(self, y, trans, recv, *args):
        self.window.addstr(y, self.tr_col_pos, '{}/{}'.format(trans, recv).rjust(7), *args)

    async def ping(self, host, cid):
        process = await asyncio.create_subprocess_exec(
            'ping',
            host,
            *self.args,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        while True:
            stdout = await process.stdout.readline()
            if not stdout:
                _, stderr = await process.communicate()
                if stderr:
                    self.add_output(cid + 1, stderr.decode().strip(), curses.color_pair(1))
                    self.window.refresh()
                break

            line = stdout.decode().strip()
            self.add_output(cid + 1, line)

            parse_result = parse_rtt(line) or parse_loss(line)
            if parse_result:
                d = self.result_dict[cid]
                d.update(**parse_result.groupdict())

                if 'avg' in d:
                    self.add_avg(cid + 1, d['avg'])

                trans, recv, loss = d.get('trans', ''), d.get('recv', ''), d.get('loss', '')
                if trans:
                    self.add_loss(cid + 1, loss)
                    self.add_tr(cid + 1, trans, recv)

            self.window.refresh()




def parse_rtt(line):
    pattern = r'^(round-trip|rtt) min/avg/max/(std|m)dev = (?P<min>.+)/(?P<avg>.+)/(?P<max>.+)/(?P<dev>.+) ms(, pipe (?P<pipe>\d+))?$'
    return re.match(pattern, line)


def parse_loss(line):
    pattern = r'^(?P<trans>\d+) packets transmitted, (?P<recv>\d+) (packets )?received, (?P<loss>.+) packet loss(, time (?P<time>.+))?$'
    return re.match(pattern, line)


class PingArgumentFormatter(argparse.HelpFormatter):
    def add_usage(self, usage, actions, groups, prefix=None):
        actions.append(argparse.ArgumentParser().add_argument('extra', nargs='*', help='extra arguments passed to ping task'))
        return super().add_usage(usage, actions, groups, prefix)


def get_arguments():
    """get arguments from user input
    :return:Namespace object
    """
    parser = argparse.ArgumentParser(description='Ping multiple hosts and/or IP in one window.',
                                     epilog='Any extra arguments will be passed to each ping task.',
                                     formatter_class=PingArgumentFormatter)
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--host', metavar='HOST', dest='hosts', nargs='*', help='host to ping')
    group.add_argument('--host-file', dest='file', help='file contains hosts, one host per line',
                       type=argparse.FileType('r'))

    arguments, unknown = parser.parse_known_args()
    arguments.host_list = arguments.hosts or arguments.file.read().split()

    return arguments, unknown


def main(window, arguments, extra):
    loop = asyncio.get_event_loop()
    curses.use_default_colors()
    curses.init_pair(1, curses.COLOR_RED, -1)
    curses.curs_set(0)

    y, x = window.getmaxyx()
    errors = []
    if y < len(arguments.host_list) + 2:
        errors.append('not enough lines')
    if x < 80:
        errors.append('not enough columns')

    if errors:
        [window.addstr('ERROR: {}\n'.format(error), curses.color_pair(1)) for error in errors]
        window.addstr('Your window is too small to properly display results, resize it and try again.\n',
                      curses.color_pair(1))
        window.addstr('Press any key to exit...')

    else:
        MultiPing(window, arguments.host_list, *extra).run()
        window.addstr(len(arguments.host_list) + 1, 0, 'Press any key to exit...')

    window.refresh()
    try:
        window.getch()
    except KeyboardInterrupt:
        pass
    finally:
        curses.endwin()
        loop.close()




if __name__ == '__main__':
    loop = asyncio.get_event_loop()
    curses.wrapper(main, *get_arguments(), loop=loop)


