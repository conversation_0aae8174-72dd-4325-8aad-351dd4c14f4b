import requests

# 设置 API 密钥
GROQ_API_KEY = "********************************************************"

# 设置请求头
headers = {
    "Authorization": f"Bearer {GROQ_API_KEY}"
}

# 发送 GET 请求
response = requests.get(
    "https://api.groq.com/openai/v1/models",
    headers=headers
)

print(response.json())
# 检查响应状态码
if response.status_code == 200:
    # 打印响应内容
    print("Response JSON:", response.json())
else:
    print(f"Request failed with status code {response.status_code}")
    print("Response:", response.text)
