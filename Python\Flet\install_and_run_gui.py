#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文本替换编辑工具 - GUI版本选择器
支持多种GUI框架：tkinter, CustomTkinter, PyQt5
"""

import sys
import subprocess
import warnings
import importlib

# 抑制字体相关警告
warnings.filterwarnings("ignore", message=".*FontBBox.*")
warnings.filterwarnings("ignore", message=".*font descriptor.*")

def install_package(package_name):
    """安装Python包"""
    try:
        print(f"正在安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package_name} 安装失败")
        return False

def check_and_install_dependencies():
    """检查并安装依赖库"""
    print("=== 检查基础依赖库 ===")
    
    # 基础PDF处理库
    basic_deps = ["PyPDF2", "pdfplumber", "reportlab"]
    missing_basic = []
    
    for dep in basic_deps:
        try:
            importlib.import_module(dep.lower() if dep != "PyPDF2" else "PyPDF2")
            print(f"✅ {dep} - 已安装")
        except ImportError:
            missing_basic.append(dep)
            print(f"❌ {dep} - 未安装")
    
    if missing_basic:
        print(f"\n正在安装基础依赖: {', '.join(missing_basic)}")
        for dep in missing_basic:
            if not install_package(dep):
                print(f"无法安装 {dep}，请手动安装")
                return False
    
    return True

def check_gui_frameworks():
    """检查可用的GUI框架"""
    print("\n=== 检查GUI框架 ===")
    
    available_guis = {}
    
    # 检查tkinter (Python内置)
    try:
        import tkinter
        available_guis['tkinter'] = {
            'name': 'Tkinter (内置)',
            'description': '轻量级，无需额外安装',
            'module': 'pdf_editor_tkinter'
        }
        print("✅ Tkinter - 可用 (推荐)")
    except ImportError:
        print("❌ Tkinter - 不可用")
    
    # 检查CustomTkinter
    try:
        import customtkinter
        available_guis['customtkinter'] = {
            'name': 'CustomTkinter',
            'description': '现代化界面，美观易用',
            'module': 'pdf_editor_customtkinter'
        }
        print("✅ CustomTkinter - 可用 (推荐)")
    except ImportError:
        print("❌ CustomTkinter - 不可用")
        print("   可以安装: pip install customtkinter")
    
    # 检查PyQt5
    try:
        from PyQt5.QtWidgets import QApplication
        available_guis['pyqt5'] = {
            'name': 'PyQt5',
            'description': '专业级界面，功能强大',
            'module': 'pdf_editor_pyqt5'
        }
        print("✅ PyQt5 - 可用")
    except ImportError:
        print("❌ PyQt5 - 不可用")
        print("   可以安装: pip install PyQt5")
    
    return available_guis

def show_menu(available_guis):
    """显示GUI选择菜单"""
    print("\n" + "="*50)
    print("PDF文本替换编辑工具 - GUI版本选择")
    print("="*50)
    
    if not available_guis:
        print("❌ 没有可用的GUI框架")
        print("请安装以下任一框架:")
        print("1. pip install customtkinter  (推荐)")
        print("2. pip install PyQt5")
        return None
    
    print("可用的GUI版本:")
    options = list(available_guis.keys())
    
    for i, (key, info) in enumerate(available_guis.items(), 1):
        print(f"{i}. {info['name']} - {info['description']}")
    
    print(f"{len(options) + 1}. 安装CustomTkinter (如果未安装)")
    print(f"{len(options) + 2}. 安装PyQt5 (如果未安装)")
    print("0. 退出")
    
    while True:
        try:
            choice = input(f"\n请选择 (1-{len(options) + 2}, 0退出): ").strip()
            
            if choice == "0":
                return None
            elif choice.isdigit():
                choice_num = int(choice)
                if 1 <= choice_num <= len(options):
                    selected_key = options[choice_num - 1]
                    return available_guis[selected_key]
                elif choice_num == len(options) + 1:
                    # 安装CustomTkinter
                    if install_package("customtkinter"):
                        print("CustomTkinter安装成功，请重新运行此脚本")
                    return None
                elif choice_num == len(options) + 2:
                    # 安装PyQt5
                    if install_package("PyQt5"):
                        print("PyQt5安装成功，请重新运行此脚本")
                    return None
            
            print("无效选择，请重新输入")
            
        except (ValueError, KeyboardInterrupt):
            print("\n程序已取消")
            return None

def run_gui_app(gui_info):
    """运行选定的GUI应用"""
    try:
        print(f"\n正在启动 {gui_info['name']}...")
        
        # 动态导入并运行
        module_name = gui_info['module']
        module = importlib.import_module(module_name)
        
        if hasattr(module, 'main'):
            module.main()
        else:
            print(f"❌ {module_name} 模块没有main函数")
            
    except ImportError as e:
        print(f"❌ 无法导入 {gui_info['module']}: {e}")
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("PDF文本替换编辑工具 - 多GUI版本")
    print("解决FontBBox字体问题，支持多种界面框架")
    print("-" * 50)
    
    # 检查基础依赖
    if not check_and_install_dependencies():
        print("\n❌ 基础依赖安装失败，无法继续")
        input("按回车键退出...")
        return
    
    # 检查GUI框架
    available_guis = check_gui_frameworks()
    
    # 显示选择菜单
    selected_gui = show_menu(available_guis)
    
    if selected_gui:
        run_gui_app(selected_gui)
    else:
        print("程序已退出")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序已取消")
    except Exception as e:
        print(f"程序出错: {e}")
        input("按回车键退出...")
