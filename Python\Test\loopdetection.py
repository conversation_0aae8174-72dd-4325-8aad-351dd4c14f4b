import flet as ft
import psutil
from scapy.all import *
import threading
import time
from datetime import datetime

class LoopDetection:
    def __init__(self, page: ft.Page):
        self.page = page
        self.page.title = "网络环路检测工具"
        self.page.window_width = 800
        self.page.window_height = 600
        self.is_detecting = False
        self.selected_interface = None
        self.packet_count = 0
        self.mac_count = {}  # 用于记录MAC地址出现次数
        
        # 获取网络接口列表
        self.interfaces = self.get_network_interfaces()
        
        # 创建界面元素
        self.interface_dropdown = ft.Dropdown(
            label="选择网络接口",
            options=[ft.dropdown.Option(key=iface, text=iface) for iface in self.interfaces],
            width=300,
            on_change=self.on_interface_selected
        )
        
        self.status_text = ft.Text("请选择网络接口开始检测", size=16)
        self.start_button = ft.ElevatedButton(
            text="开始检测",
            on_click=self.toggle_detection,
            disabled=True
        )

        # 创建数据包显示表格
        self.packets_table = ft.DataTable(
            width=750,
            height=400,
            border=ft.border.all(2, "grey"),
            columns=[
                ft.DataColumn(ft.Text("时间")),
                ft.DataColumn(ft.Text("源MAC")),
                ft.DataColumn(ft.Text("目标MAC")),
                ft.DataColumn(ft.Text("协议")),
                ft.DataColumn(ft.Text("长度")),
            ],
            rows=[]
        )

        # 添加界面元素
        self.page.add(
            ft.Column(
                controls=[
                    ft.Text("网络环路检测工具", size=24, weight=ft.FontWeight.BOLD),
                    self.interface_dropdown,
                    ft.Row([self.start_button, self.status_text]),
                    ft.Text("捕获的数据包:", size=16),
                    self.packets_table,
                ],
                spacing=20,
                alignment=ft.MainAxisAlignment.START,
            )
        )
    
    def get_network_interfaces(self):
        """获取系统中的网络接口列表"""
        interfaces = []
        for iface, addrs in psutil.net_if_addrs().items():
            interfaces.append(iface)
        return interfaces
    
    def on_interface_selected(self, e):
        """当选择网络接口时触发"""
        self.selected_interface = e.control.value
        self.start_button.disabled = False
        self.page.update()
    
    def toggle_detection(self, e):
        """切换检测状态"""
        if not self.is_detecting:
            self.is_detecting = True
            self.start_button.text = "停止检测"
            self.status_text.value = "正在检测环路..."
            self.mac_count.clear()
            self.packet_count = 0
            # 启动检测线程
            self.detect_thread = threading.Thread(target=self.detect_loop, daemon=True)
            self.detect_thread.start()
        else:
            self.is_detecting = False
            self.start_button.text = "开始检测"
            self.status_text.value = "检测已停止"
        self.page.update()

    def packet_callback(self, pkt):
        """处理捕获的数据包"""
        if not self.is_detecting:
            return

        if Ether in pkt:
            self.packet_count += 1
            src_mac = pkt[Ether].src
            dst_mac = pkt[Ether].dst
            
            # 更新MAC地址计数
            if src_mac not in self.mac_count:
                self.mac_count[src_mac] = 0
            self.mac_count[src_mac] += 1

            # 检测是否存在环路（如果同一个MAC地址在短时间内出现过多次）
            if self.mac_count[src_mac] > 100:  # 阈值可以调整
                self.page.add_async(self.update_status(f"警告：检测到可能的环路！源MAC地址 {src_mac} 出现次数过多"))
                self.mac_count[src_mac] = 0  # 重置计数

            # 获取协议类型
            if IP in pkt:
                proto = "IP"
            elif ARP in pkt:
                proto = "ARP"
            else:
                proto = "Other"

            # 更新数据包显示表格
            current_time = datetime.now().strftime("%H:%M:%S")
            new_row = ft.DataRow(
                cells=[
                    ft.DataCell(ft.Text(current_time)),
                    ft.DataCell(ft.Text(src_mac)),
                    ft.DataCell(ft.Text(dst_mac)),
                    ft.DataCell(ft.Text(proto)),
                    ft.DataCell(ft.Text(str(len(pkt)))),
                ]
            )

            # 在主线程中更新UI
            self.page.add_async(self.add_packet_row(new_row))

    def add_packet_row(self, row):
        """添加新的数据包行到表格"""
        # 保持最多显示100行
        if len(self.packets_table.rows) >= 100:
            self.packets_table.rows.pop(0)
        self.packets_table.rows.append(row)
        self.page.update()
    
    def detect_loop(self):
        """检测环路的主要逻辑"""
        try:
            # 开始捕获数据包
            sniff(iface=self.selected_interface, 
                 prn=self.packet_callback, 
                 store=0,  # 不存储数据包以节省内存
                 stop_filter=lambda x: not self.is_detecting)  # 当is_detecting为False时停止
        except Exception as e:
            self.page.add_async(self.update_status(f"检测出错: {str(e)}"))
    
    def update_status(self, message):
        """更新状态文本"""
        self.status_text.value = message
        self.page.update()

def main(page: ft.Page):
    LoopDetection(page)

if __name__ == "__main__":
    ft.app(target=main)