GalleryInterface, ToolBar, #view {
    background-color: transparent;
}

QScrollArea {
    border: none;
}

ToolBar > StrongBodyLabel {
    color: black;
}

ToolBar > CaptionLabel {
    color: rgb(95, 95, 95);
}

ExampleCard {
    background-color: transparent;
}

TitleLabel,
StrongBodyLabel {
    color: black;
}

ExampleCard > #card {
    border: 1px solid rgb(234, 234, 234);
    border-radius: 10px;
    background-color: rgb(243, 243, 243);
}

ExampleCard > #card QLabel {
    font: 14px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    color: black;
}

ExampleCard> #card InfoBadge {
    font-size: 11px;
}

#sourceWidget {
    background-color: rgb(253, 253, 253);
    border-top: 1px solid rgb(234, 234, 234);
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}
