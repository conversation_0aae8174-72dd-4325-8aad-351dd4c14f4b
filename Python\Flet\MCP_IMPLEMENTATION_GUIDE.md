# MCP工具实现指南

## 概述

本指南详细说明了如何使用Flet实现一个本地MCP（Model Context Protocol）工具演示平台，展示了工具的实现过程和预期响应。

## 实现步骤

### 第一步：理解MCP工具架构

MCP工具是一种标准化的方式，让LLM能够调用外部功能。核心组件包括：

```python
class MCPTool:
    """MCP工具基类"""
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
    
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """执行工具 - 子类必须实现"""
        raise NotImplementedError
```

### 第二步：实现具体工具

#### Fetch工具实现
```python
class FetchTool(MCPTool):
    def __init__(self):
        super().__init__("fetch", "获取网络资源")
    
    async def execute(self, url: str, method: str = "GET", 
                     headers: Dict = None, data: Any = None) -> Dict[str, Any]:
        try:
            response = requests.request(
                method=method, url=url, 
                headers=headers or {}, json=data, timeout=30
            )
            
            return {
                "success": True,
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "content": response.text,
                "json": response.json() if response.headers.get('content-type', '').startswith('application/json') else None
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
```

**预期响应格式：**
```json
{
  "success": true,
  "status_code": 200,
  "headers": {
    "content-type": "application/json",
    "content-length": "1024"
  },
  "content": "原始响应内容",
  "json": {
    "key": "解析后的JSON数据"
  }
}
```

### 第三步：构建GUI界面

使用Flet创建用户友好的界面：

```python
def setup_ui(self):
    # 工具选择器
    self.tool_selector = ft.Dropdown(
        label="选择MCP工具",
        options=[
            ft.dropdown.Option("fetch", "Fetch - 网络请求"),
            ft.dropdown.Option("shell", "Shell - 系统命令"),
            ft.dropdown.Option("filesystem", "FileSystem - 文件操作"),
        ],
        on_change=self.on_tool_change
    )
    
    # 动态配置区域
    self.tool_config_container = ft.Container(
        content=self.create_tool_config(),
        padding=10,
        border=ft.border.all(1, ft.colors.GREY_300)
    )
```

### 第四步：实现两种执行模式

#### 模式1：直接执行
```python
async def execute_tool(self, e):
    """直接执行工具功能"""
    tool = self.tools[self.current_tool]
    
    # 获取用户配置的参数
    params = self.get_tool_parameters()
    
    # 执行工具
    result = await tool.execute(**params)
    
    # 显示结果
    self.display_result(result)
```

#### 模式2：MCP协议调用
```python
async def call_via_mcp(self, e):
    """通过MCP协议调用LLM"""
    # 1. 构建包含工具信息的prompt
    prompt = self.build_mcp_prompt()
    
    # 2. 调用LLM API
    response = await self.call_llm_api(prompt)
    
    # 3. 显示LLM的响应
    self.display_mcp_result(response)

def build_mcp_prompt(self) -> str:
    """构建MCP工具调用的prompt"""
    config = self.get_current_tool_config()
    
    return f"""
请使用{config['tool']}工具执行以下操作：

工具配置：
{json.dumps(config, indent=2, ensure_ascii=False)}

请执行工具并返回详细的结果。
"""
```

### 第五步：LLM API集成

```python
async def call_llm_api(self, prompt: str) -> str:
    """调用LLM API"""
    headers = {
        "Authorization": f"Bearer {self.api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "gpt-3.5-turbo",
        "messages": [{"role": "user", "content": prompt}],
        "max_tokens": 2000
    }
    
    response = requests.post(self.api_url, headers=headers, json=payload)
    result = response.json()
    return result["choices"][0]["message"]["content"]
```

## 预期响应示例

### 1. Fetch工具 - 成功响应
```json
{
  "tool": "fetch",
  "input": {
    "url": "https://api.github.com/users/octocat",
    "method": "GET"
  },
  "output": {
    "success": true,
    "status_code": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8",
      "server": "GitHub.com"
    },
    "json": {
      "login": "octocat",
      "id": 1,
      "name": "The Octocat"
    }
  },
  "execution_time": "245ms"
}
```

### 2. Shell工具 - 成功响应
```json
{
  "tool": "shell",
  "input": {
    "command": "echo 'Hello MCP!'",
    "cwd": "/current/directory"
  },
  "output": {
    "success": true,
    "return_code": 0,
    "stdout": "Hello MCP!\n",
    "stderr": "",
    "execution_time": "12ms"
  }
}
```

### 3. FileSystem工具 - 读取文件响应
```json
{
  "tool": "filesystem",
  "input": {
    "action": "read",
    "path": "./config.json"
  },
  "output": {
    "success": true,
    "content": "{\n  \"version\": \"1.0\",\n  \"name\": \"MCP Demo\"\n}",
    "size": 45,
    "encoding": "utf-8"
  }
}
```

### 4. 错误响应格式
```json
{
  "tool": "fetch",
  "input": {
    "url": "https://invalid-url.example",
    "method": "GET"
  },
  "output": {
    "success": false,
    "error": "Connection timeout",
    "error_code": "TIMEOUT",
    "retry_suggested": true
  }
}
```

## MCP协议工作流程

### 1. 工具注册阶段
```
Client -> MCP Server: 请求可用工具列表
MCP Server -> Client: 返回工具清单
{
  "tools": [
    {
      "name": "fetch",
      "description": "获取网络资源",
      "parameters": {
        "url": {"type": "string", "required": true},
        "method": {"type": "string", "default": "GET"}
      }
    }
  ]
}
```

### 2. 工具调用阶段
```
Client -> MCP Server: 工具调用请求
{
  "tool": "fetch",
  "parameters": {
    "url": "https://api.example.com/data",
    "method": "GET"
  }
}

MCP Server -> Client: 执行结果
{
  "success": true,
  "result": { ... }
}
```

### 3. LLM集成阶段
```
User -> LLM: "请获取GitHub用户信息"
LLM -> MCP: 调用fetch工具
MCP -> LLM: 返回API响应
LLM -> User: "我已经获取到用户信息：..."
```

## 关键实现要点

### 1. 异步执行
所有工具执行都使用异步模式，避免界面阻塞：
```python
async def execute(self, **kwargs):
    # 异步执行工具逻辑
    pass
```

### 2. 错误处理
完善的错误处理机制：
```python
try:
    result = await tool.execute(**params)
except Exception as e:
    result = {"success": False, "error": str(e)}
```

### 3. 参数验证
在工具执行前验证参数：
```python
def validate_parameters(self, tool_name: str, params: Dict) -> bool:
    # 验证必需参数
    # 验证参数类型
    # 验证参数范围
    pass
```

### 4. 结果格式化
统一的结果格式便于处理：
```python
def format_result(self, raw_result: Any) -> Dict[str, Any]:
    return {
        "success": True,
        "data": raw_result,
        "timestamp": datetime.now().isoformat(),
        "tool": self.name
    }
```

## 扩展建议

1. **添加更多工具**：数据库查询、文件处理、图像生成等
2. **改进UI**：添加语法高亮、结果可视化
3. **增强安全性**：参数校验、权限控制
4. **性能优化**：缓存、并发执行
5. **日志记录**：详细的执行日志和调试信息

这个实现展示了MCP工具的核心概念，为开发更复杂的MCP应用提供了基础框架。
