#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化启动脚本 - 不依赖额外模块
"""

import os
import sys
import webbrowser
import time
import threading

def check_dependencies():
    """检查核心依赖"""
    required_modules = ['flask', 'requests']
    missing = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} 已安装")
        except ImportError:
            missing.append(module)
            print(f"❌ {module} 未安装")
    
    if missing:
        print(f"\n缺少依赖: {', '.join(missing)}")
        print("请运行: python install.py")
        return False
    
    return True

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 浏览器已打开")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print("请手动访问: http://localhost:5000")

def main():
    print("=" * 60)
    print("🔐 Bitwarden 数据筛查工具 - 简化启动")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        sys.exit(1)
    
    try:
        # 导入应用
        from app import app
        
        print("✅ 应用加载成功")
        print("🚀 启动服务器...")
        
        # 在后台线程中打开浏览器
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        print("📱 访问地址: http://localhost:5000")
        print("🛑 按 Ctrl+C 停止服务器")
        print("-" * 60)
        
        # 启动Flask应用
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请先运行: python install.py")
        input("按回车键退出...")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")
        sys.exit(1)

if __name__ == '__main__':
    main()
